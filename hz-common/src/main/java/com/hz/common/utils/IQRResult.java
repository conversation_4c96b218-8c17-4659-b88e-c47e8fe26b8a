package com.hz.common.utils;

import java.util.List;

public class IQRResult {

    public Double[] sortedData;
    public double q1;
    public double q3;
    public double iqr;
    public double lowerBound;
    public double upperBound;
    public List<Double> outliers;

    public IQRResult(Double[] sortedData, double q1, double q3, double iqr, double lowerBound, double upperBound, List<Double> outliers) {
        this.sortedData = sortedData;
        this.q1 = q1;
        this.q3 = q3;
        this.iqr = iqr;
        this.lowerBound = lowerBound;
        this.upperBound = upperBound;
        this.outliers = outliers;
    }
}

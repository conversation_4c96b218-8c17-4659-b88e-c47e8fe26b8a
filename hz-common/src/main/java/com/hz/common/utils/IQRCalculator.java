package com.hz.common.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class IQRCalculator {

    public static IQRResult calculateIQR(Double[] data, Double lb, Double ub) {
        // 1.排序数据
        Double[] sortedData = Arrays.copyOf(data, data.length);
        Arrays.sort(sortedData);

        // 2.计算 Q1, Q3, IQR
        double q1 = calculatePercentile(sortedData, 25);
        double q3 = calculatePercentile(sortedData, 75);
        double iqr = q3 - q1;

        // 3.计算上下限
        double lowerBound = q1 - lb * iqr;
        double upperBound = q3 + ub * iqr;

        // 4.找出异常值
        List<Double> outliers = new ArrayList<>();
        for (double num : data) {
            if (num < lowerBound || num > upperBound) {
                outliers.add(num);
            }
        }

        return new IQRResult(sortedData, q1, q3, iqr, lowerBound, upperBound, outliers);
    }

    // 计算百分位数（线性插值法）
    private static double calculatePercentile(Double[] sortedData, double percentile) {
        if (sortedData.length == 0) {
            throw new IllegalArgumentException("数据不能为空");
        }

        double index = percentile / 100.0 * (sortedData.length - 1);
        int lowerIndex = (int) Math.floor(index);
        int upperIndex = (int) Math.ceil(index);

        if (lowerIndex == upperIndex) {
            return sortedData[lowerIndex];
        }

        // 线性插值
        double weight = index - lowerIndex;
        return sortedData[lowerIndex] * (1 - weight) + sortedData[upperIndex] * weight;
    }

    public static void main(String[] args) {
//        Double[] data = {0.0, 850.0, 800.0, 700.0, 780.0, 720.0, 1000.0, 730.0, 750.0, 720.0, 0.0, 1000.0, 850.0, 750.0};
//
//        // 计算 IQR 和上下限
//        IQRResult result = calculateIQR(data, 1.5, 1.5);
//
//        System.out.println("排序后的数据: " + Arrays.toString(result.sortedData));
//        System.out.println("Q1 (下四分位数): " + result.q1);
//        System.out.println("Q3 (上四分位数): " + result.q3);
//        System.out.println("IQR (四分位距): " + result.iqr);
//        System.out.println("下限 (Q1 - 1.5 * IQR): " + result.lowerBound);
//        System.out.println("上限 (Q3 + 1.5 * IQR): " + result.upperBound);
//        System.out.println("异常值: " + result.outliers);
//
//
//        IQRUtil.IQR res = IQRUtil.getIqr(Arrays.stream(data).toList(), 1.5, 1.5);
//        System.out.println(res);

        Integer a = 0;
        System.out.println(a.compareTo(0));
    }
}

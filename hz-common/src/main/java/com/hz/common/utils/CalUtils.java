package com.hz.common.utils;

import cn.hutool.core.collection.CollectionUtil;
import org.apache.commons.math3.distribution.TDistribution;
import org.apache.commons.math3.stat.descriptive.SummaryStatistics;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class CalUtils {

    /**
     * 计算箱线图
     *
     * @param values
     * @return
     */
    public static List<Double> calXxt(List<Double> values) {
        if(CollectionUtil.isEmpty(values)){
            return Arrays.asList(0D, 0D, 0D, 0D, 0D);
        }
        // 如果只有一个元素
        if(values.size() == 1){
            double value = values.get(0);
            // 置信区间（添加模拟数据）
            values.add(value - 1);
            values.add(value - 2);
            values.add(value + 1);
            values.add(value + 2);
            // 置信区间
            double[] interval = calculateConfidenceInterval1(values.toArray(Double[]::new), 0.9);
            // 返回
            return Arrays.asList(interval[0], interval[0], interval[1], interval[2], interval[2]);
        } else {
            // 置信区间
            double[] interval = calculateConfidenceInterval1(values.toArray(Double[]::new), 0.9);
            // 最大值、最小值
            Double max = Collections.max(values);
            Double min = Collections.min(values);
            // 返回
            return Arrays.asList(Math.min(interval[0], min), interval[0], interval[1], interval[2], Math.max(max, interval[2]));
        }
    }

    /**
     * 计算置信区间
     *
     * @param data
     * @param confidenceLevel
     * @return
     */
    public static double[] calculateConfidenceInterval1(Double[] data, double confidenceLevel) {
        SummaryStatistics stats = new SummaryStatistics();
        for (double value : data) {
            stats.addValue(value);
        }

        double mean = stats.getMean();
        double stdDev = stats.getStandardDeviation();
        int n = (int) stats.getN();

        double alpha = 1 - confidenceLevel;
        TDistribution tDist = new TDistribution(n - 1);
        double criticalValue = Math.abs(tDist.inverseCumulativeProbability(1 - alpha/2));
        double marginOfError = criticalValue * (stdDev / Math.sqrt(n));
        // 下线如果为负值，则设置为0
        double v1 = mean - marginOfError;
        return new double[] {
                Math.max(v1, 0D),  // 置信下限
                mean,
                mean + marginOfError   // 置信上限
        };
    }

    /**
     * 计算置信区间
     *
     * @param data
     * @param confidenceLevel
     * @return
     */
    public static double[] calculateConfidenceInterval2(double[] data, double confidenceLevel) {
        // 计算样本统计量
        double sum = 0.0;
        for (double d : data) sum += d;
        double mean = sum / data.length;

        double squaredDiffSum = 0.0;
        for (double d : data) {
            squaredDiffSum += Math.pow(d - mean, 2);
        }
        double stdDev = Math.sqrt(squaredDiffSum / (data.length - 1));

        // 使用固定 z 值简化计算 (适用于 n > 30)
        double z;
        if (confidenceLevel == 0.90) z = 1.645;
        else if (confidenceLevel == 0.95) z = 1.96;
        else if (confidenceLevel == 0.99) z = 2.576;
        else throw new IllegalArgumentException("不支持的置信水平");

        double marginOfError = z * (stdDev / Math.sqrt(data.length));

        return new double[] {
                mean - marginOfError,
                mean + marginOfError
        };
    }

    public static double getMean(List<Double> list){
        if(CollectionUtil.isEmpty(list)){
            return 0;
        }
        if(list.size() == 1){
            return list.get(0);
        }
        SummaryStatistics stats = new SummaryStatistics();
        for (double value : list) {
            stats.addValue(value);
        }
        return stats.getMean();
    }
}

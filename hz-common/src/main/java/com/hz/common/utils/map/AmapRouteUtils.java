//package com.hz.common.utils.map;
//
//import com.hz.merchant.domain.MerchantInfo;
//
///**
// * 高德地图路径规划工具类
// *
// * <AUTHOR>
// */
//public class AmapRouteUtils {
//
//    /**
//     * 获取用于高德地图路径规划的经纬度坐标
//     * 高德地图使用GCJ-02坐标系（偏移后坐标）
//     *
//     * @param merchantInfo 商户信息
//     * @return 格式化的坐标字符串 "经度,纬度"
//     */
//    public static String getAmapCoordinate(MerchantInfo merchantInfo) {
//        if (merchantInfo == null) {
//            return null;
//        }
//
//        // 优先使用偏移后坐标（GCJ-02），这是高德地图的标准坐标系
//        String longitude = merchantInfo.getLongitudeAfterOffset();
//        String latitude = merchantInfo.getLatitudeAfterOffset();
//
//        // 如果没有偏移后坐标，则使用原始坐标（但需要注意精度问题）
//        if (isEmpty(longitude) || isEmpty(latitude)) {
//            longitude = merchantInfo.getOriginalLongitude();
//            latitude = merchantInfo.getOriginalLatitude();
//        }
//
//        if (isEmpty(longitude) || isEmpty(latitude)) {
//            return null;
//        }
//
//        return longitude + "," + latitude;
//    }
//
//    /**
//     * 获取起点坐标（用于高德地图路径规划）
//     *
//     * @param startMerchant 起点商户
//     * @return 起点坐标字符串
//     */
//    public static String getOriginCoordinate(MerchantInfo startMerchant) {
//        return getAmapCoordinate(startMerchant);
//    }
//
//    /**
//     * 获取终点坐标（用于高德地图路径规划）
//     *
//     * @param endMerchant 终点商户
//     * @return 终点坐标字符串
//     */
//    public static String getDestinationCoordinate(MerchantInfo endMerchant) {
//        return getAmapCoordinate(endMerchant);
//    }
//
//    /**
//     * 检查字符串是否为空
//     */
//    private static boolean isEmpty(String str) {
//        return str == null || str.trim().isEmpty();
//    }
//
//    /**
//     * 验证坐标格式是否正确
//     *
//     * @param coordinate 坐标字符串 "经度,纬度"
//     * @return 是否有效
//     */
//    public static boolean isValidCoordinate(String coordinate) {
//        if (isEmpty(coordinate)) {
//            return false;
//        }
//
//        String[] parts = coordinate.split(",");
//        if (parts.length != 2) {
//            return false;
//        }
//
//        try {
//            double longitude = Double.parseDouble(parts[0].trim());
//            double latitude = Double.parseDouble(parts[1].trim());
//
//            // 检查经纬度范围（中国境内大致范围）
//            return longitude >= 73.0 && longitude <= 135.0 &&
//                   latitude >= 18.0 && latitude <= 54.0;
//        } catch (NumberFormatException e) {
//            return false;
//        }
//    }
//}
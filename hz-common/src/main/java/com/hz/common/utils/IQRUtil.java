package com.hz.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

/** IQR 工具类
 * <AUTHOR>
 * @date 2025/6/25 14:21
 */
public class IQRUtil{

    private static final Logger logger = LoggerFactory.getLogger(IQRUtil.class);

    /** 计算 IQR
     * @param list 数据列表
     * @return IQR 对象{IQR, Q1, Q3, 下限, 上限}
     */
    public static IQR getIqr(List<Double> list, Double lb, Double ub){
        double iqr;
        double lowerBound;
        double upperBound;

        if(list == null || list.isEmpty()){
            return null;
        }

        list = list.stream()
                   .filter(newPrize -> newPrize != null && newPrize > 0.0)
                   .sorted()
                   .toList();

        int size = list.size();
        if(size < 4){
            // 数据量小于 4 无法计算 IQR，直接返回
            return null;
        }

        // 计算下四分位数 Q1 的索引
        double q1Index = (size - 1) * 0.25;
        int q1Floor = (int) Math.floor(q1Index);
        int q1Ceil = (int) Math.ceil(q1Index);
        double q1 = list.get(q1Floor) + (list.get(q1Ceil) - list.get(q1Floor)) * (q1Index - q1Floor);

        // 计算上四分位数 Q3 的索引
        double q3Index = (size - 1) * 0.75;
        int q3Floor = (int) Math.floor(q3Index);
        int q3Ceil = (int) Math.ceil(q3Index);
        double q3 = list.get(q3Floor) + (list.get(q3Ceil) - list.get(q3Floor)) * (q3Index - q3Floor);

        // 计算 IQR
        iqr = q3 - q1;
        // 计算上下限
        lowerBound = q1 - lb * iqr;
        upperBound = q3 + ub * iqr;
        // 可以在这里添加对上下限的处理逻辑，例如记录日志
        logger.error("【IQR】: \t{}\t, 【Q1】: \t{}\t, 【Q3】: \t{}\t, 【下限】: \t{}\t, 【上限】: \t{}\t", iqr, q1, q3, lowerBound, upperBound);
        return new IQR(iqr, q1, q3, lowerBound, upperBound);
    }

    public record IQR(double iqr, double q1, double q3, double lowerBound, double upperBound){}


}

package com.hz.common.utils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/25 15:00
 */
class IQRUtilTest{

    public static void main(String[] args){
        List<Double> list = List.of(0.0, 0.0, 0.0, 100.0, 850.0, 800.0, 700.0, 780.0, 720.0, 1000.0, 730.0, 750.0, 730.0, 350.0, 1000.0, 350.0, 750.0, 900.0, 1000.0, 750.0, 1000.0,
                                    1000.0,
                                    1000.0, 690.0, 800.0, 900.0, 750.0, 700.0, 730.0, 800.0, 950.0, 950.0, 670.0, 100.0, 100.0, 900.0, 1000.0, 100.0, 750.0, 750.0, 700.0, 780750.0,
                                    720.0, 800.0, 780.0, 900.0, 710.0, 100.0, 720.0, 1000.0, 750.0);
        IQRUtil.IQR res = IQRUtil.getIqr(list, 1.5, 1.5);
        System.out.println(res);

    }
}
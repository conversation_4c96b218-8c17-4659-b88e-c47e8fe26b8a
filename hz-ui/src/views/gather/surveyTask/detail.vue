<template>
  <div class="app-container">
    <div class="store_header">
      <div class="store_header_title">
        <div class="flex flex-row">
          <img @click="back" src="@/assets/images/sjcj_back.png" alt="back" class="mr-10px cursor-pointer" />
          <span>{{ name }}</span>
        </div>
      </div>
    </div>

    <div class="from_container">
      <el-row :gutter="10" class="">
        <el-col :span="1.5">
          <el-button class="btn-add" plain icon="Plus"
                     @click="handleImport('1')"
                     v-hasPermi="['gather:surveyTask:query']">导入价格数据
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button class="btn-add" plain icon="Plus"
                     @click="handleImport('2')"
                     v-hasPermi="['gather:surveyTask:query']">导入库存数据
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button class="btn-add" plain icon="Plus"
                     @click="handleImport('3')"
                     v-hasPermi="['gather:surveyTask:query']">导入四员联动
          </el-button>
        </el-col>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName" class="mt-20px!">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="数据类型" width="180" align="center" prop="sjlx" show-overflow-tooltip/>
<!--        <el-table-column label="操作人" align="center" prop="czr">-->
<!--          <template #default="scope">-->
<!--            <span v-if="scope.row.czr && scope.row.czr.length > 0">{{ scope.row.czr }}</span>-->
<!--            <span v-else>-</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column label="导入状态" align="center" prop="drzt">
          <template #default="scope">
            <div class="flex flex-row items-center justify-center">
              <dict-tag :options="sjcj_drzt" :value="scope.row.drzt" />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="导入时间" align="center" prop="drsj">
          <template #default="scope">
            <span v-if="scope.row.drsj && scope.row.drsj.length > 0">{{ scope.row.drsj }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="480">
          <template #default="scope">
            <div class="table-btn">
              <el-button v-if="scope.row.sjlx !== '四员联动'" link class="table-btn-delete" v-hasPermi="['gather:surveyTask:query']" @click="dataProcess(scope.row)">数据清洗</el-button>
              <div v-if="scope.row.sjlx !== '四员联动'" class="btn-line"></div>
              <el-button link class="table-btn-edit" v-hasPermi="['gather:surveyTask:query']" @click="queryOriginData(scope.row)">查看原始数据</el-button>
              <div v-if="scope.row.sjlx !== '四员联动'" class="btn-line"></div>
              <el-button v-if="scope.row.sjlx !== '四员联动'" link class="table-btn-edit" v-hasPermi="['gather:surveyTask:query']" @click="queryCleanRemind(scope.row)">查看清洗提示</el-button>
              <div v-if="scope.row.sjlx !== '四员联动'" class="btn-line"></div>
              <el-button v-if="scope.row.sjlx !== '四员联动'" link class="table-btn-edit" v-hasPermi="['gather:surveyTask:query']" @click="queryCleanSh(scope.row)">查看清洗商户</el-button>
              <div v-if="scope.row.sjlx !== '四员联动'" class="btn-line"></div>
              <el-button v-if="scope.row.sjlx !== '四员联动'" link class="table-btn-edit" v-hasPermi="['gather:surveyTask:query']" @click="queryCleanData(scope.row)" >查看清洗数据</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>


    </div>

    <!-- 数据导入对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body class="dialog_component">
      <div class="dialog_header">
        <div class="header_left">
          <img src="@/assets/images/dialog-icon.png" alt="">
          <div class="dialog_title">{{ title }}</div>
        </div>
        <div class="dialog_close" @click="open = false"><el-icon><CloseBold /></el-icon></div>
      </div>
      <el-upload
        ref="uploadRef"
        :headers="headers"
        :action="uploadFileUrl + '?taskId=' + id + '&type=' + type"
        :auto-upload="false"
        accept=".xlsx,.xls"
        :limit="1"
        :on-exceed="handleExceed"
        :on-success="handleFileSuccess"drag>
        <div class="el-upload__text w-full flex flex-row items-center justify-center">
          <div class=" flex flex-row items-center justify-center w-120px" style="border: 1px solid #d0d0d0; border-radius: 3px; padding: 4px 12px 4px 12px;">
            <el-icon class="mr-5px" color="#21a042"><Upload /></el-icon>
            <span>上传文件</span>
          </div>
        </div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span><span style="color: red;">重复导入会覆盖上次数据</span>，仅允许导入xls、xlsx格式文件。</span>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="dialog_submit" @click="submitFileForm">确 定</el-button>
          <el-button class="dialog_cancel" @click="open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 数据展示：原始数据、清洗后提示、清洗后数据、四员联动 -->
    <dataComp ref="dataCompRef"  />

    <!-- 数据清洗 -->
    <processComp ref="processCompRef" />
  </div>
</template>

<script setup>
import { ref } from "vue";
import { getToken } from "@/utils/auth.js";
import { getTask } from "@/api/gather/surveyTask.js"
import dataComp from './components/data.vue';
import processComp from './components/process.vue';

const { proxy } = getCurrentInstance()
const { sjcj_drzt } = proxy.useDict("sjcj_drzt")

const route = useRoute();
const id = ref(route.query.id)
const name = ref(route.query.name)
const type = ref("1")

//
const loading = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const open = ref(false)
const title = ref("")
const dataList = ref([])
const headers = ref({ Authorization: "Bearer " + getToken() })
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/gather/surveyTask/upload")

//
const dataCompRef = ref()
const processCompRef = ref()

//--------------------------------------------------------------- 数据请求 ---------------------------------------------------------------------

// 数据请求
const getDetail = () => {
  getTask(id.value).then(res => {
    const data = []
    data.push({ sjlx: '价格数据', type: '1', drzt: res.data.importStatusPrice, drsj: res.data.importTimePrice, czr: '' })
    data.push({ sjlx: '库存数据', type: '2', drzt: res.data.importStatusInventory, drsj: res.data.importTimeInventory, czr: '' })
    data.push({ sjlx: '四员联动', type: '3', drzt: res.data.importStatusLink, drsj: res.data.importTimeLink, czr: '' })
    dataList.value = data
    loading.value = false
  })
}

//--------------------------------------------------------------- 事件管理 ---------------------------------------------------------------------

// 多选框选中数据
function handleSelectionChange(selection){
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

const tableRowClassName = ({ row, rowIndex }) => {
  if ((rowIndex + 1) % 2 === 0) {
    return 'table-row'
  }
  return ''
}

// 导入
const handleImport = (flag) => {
  if(flag === '1'){
    title.value = "价格数据导入"
  }
  if(flag === '2'){
    title.value = "库存数据导入"
  }
  if(flag === '3'){
    title.value = "四员联动数据导入"
  }
  type.value = flag
  open.value = true
}

// 数据清洗
const dataProcess = (row) => {
  processCompRef.value.show(id.value, row.type);
}

// 查看原始数据
const queryOriginData = (row) => {
  dataCompRef.value.show(id.value, row.type, '1');
}

// 查看清洗提示
const queryCleanRemind = (row) => {
  dataCompRef.value.show(id.value, row.type, '4');
}

// 查看清洗商户
const queryCleanSh = (row) => {
  dataCompRef.value.show(id.value, row.type, '5');
}

// 查看清洗数据
const queryCleanData = (row) => {
  dataCompRef.value.show(id.value, row.type, '2');
}

// 数据导入：文件校验
function handleExceed(files, fileList){
  proxy.$modal.msgWarning('只能上传一个文件，请移除多余文件！');
}

// 数据导入：结束事件
const handleFileSuccess = (response, file, fileList) => {
  proxy.$modal.closeLoading()
  if(response.code === 200){
    proxy.$modal.msgSuccess("导入成功");
  }else{
    proxy.$modal.msgError("导入失败");
  }
  proxy.$refs["uploadRef"].handleRemove(file)
  getDetail()
  open.value = false
}

/** 提交上传文件 */
function submitFileForm() {
  proxy.$modal.loading("数据提交中，请稍候...")
  proxy.$refs["uploadRef"].submit()
}

// 返回
const back = () => {
  const obj = { path: "/gather/surveyTask" }
  proxy.$tab.closeOpenPage(obj)
}

//--------------------------------------------------------------- vue相关 ---------------------------------------------------------------------

onMounted(() => {
  getDetail()
})

</script>

<style lang="scss" scoped>
.app-container {
  background-color: #f4f8ff;
  color: #333333;
}

.store_header {
  box-sizing: border-box;
  padding: 20px 30px;
  background-color: #fff;
  border-radius: 5px;

  .store_header_title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    display: flex;
    align-items: center;
  }
}

.store_content {
  box-sizing: border-box;
  padding: 20px 30px;
  background-color: #fff;
  border-radius: 5px;
  margin-top: 10px;

  .store_content_header {
    display: flex;
    align-items: center;

    .title_line {
      width: 3px;
      height: 19px;
      background: #21A042;
      border-radius: 2px;
    }

    .title {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-left: 10px;
    }
  }

  .store_content_form {
    margin-top: 25px;
  }
}

.from_container {
  min-height: calc(100vh - 600px);
}
</style>
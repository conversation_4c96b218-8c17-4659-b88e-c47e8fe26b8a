<template>
  <div class="app-container">
    <div class="from_header">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="任务名称" prop="name">
          <el-input v-model="queryParams.name"
                    placeholder="请输入任务名称"
                    clearable
                    @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item label="日期" prop="year">
          <el-date-picker v-model="queryParams.datePicker" style="width: 240px"
                          type="daterange"
                          range-separator="-"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          size="default"/>
        </el-form-item>
        <el-form-item label="价格导入" prop="importStatusPrice">
          <el-select v-model="queryParams.importStatusPrice" placeholder="全部" style="width: 120px">
            <el-option v-for="item in statusOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="库存导入" prop="importStatusInventory">
          <el-select v-model="queryParams.importStatusInventory" placeholder="全部" style="width: 120px">
            <el-option v-for="item in statusOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="四员联动" prop="importStatusLink">
          <el-select v-model="queryParams.importStatusLink" placeholder="全部" style="width: 120px">
            <el-option v-for="item in statusOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button class="btn-search" icon="Search" @click="handleQuery">查询</el-button>
          <el-button class="btn-refresh" icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <div class="from_container">
      <el-row :gutter="10" class="">
        <el-col :span="1.5">
          <el-button class="btn-add" plain icon="Plus"
                     @click="handleAdd"
                     v-hasPermi="['gather:surveyTask:add']">新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button class="btn-edit" plain icon="Edit"
                     :disabled="single"
                     @click="handleUpdate"
                     v-hasPermi="['gather:surveyTask:edit']">修改
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button class="btn-delete" plain icon="Delete"
                     :disabled="multiple"
                     @click="handleDelete"
                     v-hasPermi="['gather:surveyTask:remove']">删除
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button class="btn-download" plain icon="Download"
                     :disabled="single"
                     @click="handleExportData"
                     v-hasPermi="['gather:surveyTask:export']">导出任务数据
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button class="btn-download" plain icon="Download"
                     @click="handleExport"
                     v-hasPermi="['gather:surveyTask:export']">导出任务列表
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button class="btn-data" plain icon="Position"
                     @click="handleDataView"
                     v-hasPermi="['gather:surveyTask:export']">数据大屏
          </el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
      
      <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName" class="mt-20px!">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="任务名称" width="240" align="center" prop="name" show-overflow-tooltip/>
        <el-table-column label="任务时间" width="180" align="center" prop="year">
          <template #default="scope">
            <span>
              <strong style="color:dodgerblue;">{{ scope.row.year }}</strong> 年
              <strong style="color:dodgerblue;">{{ scope.row.month }}</strong> 月第
              <strong style="color:dodgerblue;">{{ scope.row.week }}</strong> 周
            </span>
          </template>
        </el-table-column>
        <el-table-column label="价格调研" width="160" align="center" prop="importStatusPrice">
          <template #default="scope">
            <span :style="{ color: scope.row.importStatusPrice === '1' ? 'darkgreen' : 'red' }">
              {{ scope.row.importStatusPrice === '1' ? '已导入' : '未导入' }}
              {{ scope.row.importStatusPrice === '1' ? parseTime(scope.row.importTimePrice, '{y}-{m}-{d}') : '' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="库存调研" width="160" align="center" prop="importStatusInventory">
          <template #default="scope">
            <span :style="{ color: scope.row.importStatusInventory === '1' ? 'darkgreen' : 'red' }">
              {{ scope.row.importStatusInventory === '1' ? '已导入' : '未导入' }}
              {{ scope.row.importStatusInventory === '1' ? parseTime(scope.row.importTimeInventory, '{y}-{m}-{d}') : '' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="四员联动" width="160" align="center" prop="importStatusLink">
          <template #default="scope">
          <span :style="{ color: scope.row.importStatusLink === '1' ? 'darkgreen' : 'red' }">
            {{ scope.row.importStatusLink === '1' ? '已导入' : '未导入' }}
            {{ scope.row.importStatusLink === '1' ? parseTime(scope.row.importTimeLink, '{y}-{m}-{d}') : '' }}
          </span>
          </template>
        </el-table-column>
        <el-table-column label="说明" align="center" prop="description"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="210">
          <template #default="scope">
            <div class="table-btn">
              <el-button link class="table-btn-edit" @click="handleUpdate(scope.row)" v-hasPermi="['gather:surveyTask:edit']">修改</el-button>
              <div class="btn-line"></div>
              <el-button link class="table-btn-delete" @click="handleDelete(scope.row)" v-hasPermi="['gather:surveyTask:remove']">删除</el-button>
              <div class="btn-line"></div>
              <el-button link class="table-btn-data" @click="handleDetail(scope.row)" v-hasPermi="['gather:surveyTask:query']">调研数据</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList"/>
    </div>
    
    <!-- 添加或修改调研任务对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body class="dialog_component">
      <div class="dialog_header">
        <div class="header_left">
          <img src="@/assets/images/dialog-icon.png" alt="">
          <div class="dialog_title"> {{ title }}</div>
        </div>
        <div class="dialog_close" @click="cancel">
          <el-icon>
            <CloseBold/>
          </el-icon>
        </div>
      </div>
      <el-form ref="taskRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="任务名称" prop="name">
          <el-input type="text" v-model="form.name" placeholder="请输入任务名称" minlength="6" maxlength="30"/>
        </el-form-item>
        <el-form-item label="年" prop="year">
          <el-input type="number" v-model="form.year" placeholder="请输入年" minlength="4" maxlength="4" @input="updateTaskName"/>
        </el-form-item>
        <el-form-item label="月" prop="month">
          <el-input type="number" v-model="form.month" placeholder="请输入月" minlength="1" maxlength="2" @input="updateTaskName"/>
        </el-form-item>
        <el-form-item label="周" prop="week">
          <el-input type="number" v-model="form.week" placeholder="请输入周" minlength="1" maxlength="1" @input="updateTaskName"/>
        </el-form-item>
        <el-form-item label="说明" prop="description">
          <el-input type="textarea" v-model="form.description" placeholder="请输入内容" maxlength="50"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="dialog_submit" @click="submitForm">确 定</el-button>
          <el-button class="dialog_cancel" @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="SurveyTask">
import {ref} from 'vue'
import {listTask, getTask, delTask, addTask, updateTask} from "@/api/gather/surveyTask.js"
import {useRouter} from 'vue-router';
import {requestFullscreen} from '@/utils/hz.js'
import {useFullscreen} from '@vueuse/core'
import dayjs from "dayjs";

const router = useRouter();
const {proxy} = getCurrentInstance()
const {isFullscreen, enter, exit, toggle} = useFullscreen()
const taskList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    id: 0,
    pageNum: 1,
    pageSize: 10,
    name: null,
    year: null,
    month: null,
    week: null,
    yearMonthWeek: null,
    importStatusPrice: null,
    importStatusInventory: null,
    importStatusLink: null,
    description: null,
    datePicker: null,
  },
  rules: {
    name: [{required: true, message: "任务名称不能为空", trigger: "blur"}],
    year: [{required: true, message: "年不能为空", trigger: "blur"}],
    month: [{required: true, message: "月不能为空", trigger: "blur"}],
    week: [{required: true, message: "周不能为空", trigger: "blur"}],
  }
})
// 上传状态
const statusOption = [
  {value: '0', label: '未上传',},
  {value: '1', label: '已上传',},
]

const {queryParams, form, rules} = toRefs(data)

/** 查询调研任务列表 */
function getList(){
  loading.value = true
  if(queryParams.value.datePicker != null && queryParams.value.datePicker.length === 2){
    queryParams.value.yearMonthWeek = JSON.stringify(queryParams.value.datePicker)
  }
  listTask(queryParams.value).then(response => {
    taskList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel(){
  open.value = false
  reset()
}

// 表单重置
function reset(){
  form.value = {
    id: null,
    name: null,
    year: null,
    month: null,
    week: null,
    yearMonthWeek: null,
    importStatusPrice: null,
    importStatusInventory: null,
    importStatusLink: null,
    importTimePrice: null,
    importTimeInventory: null,
    importTimeLink: null,
    description: null,
    createId: null,
    createBy: null,
    createTime: null,
    updateId: null,
    updateBy: null,
    updateTime: null,
    remark: null,
    datePicker: null,
  }
  proxy.resetForm("taskRef")
}

/** 搜索按钮操作 */
function handleQuery(){
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery(){
  queryParams.value.yearMonthWeek = null
  queryParams.value.datePicker = null
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection){
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

const tableRowClassName = ({row, rowIndex}) => {
  if((rowIndex + 1) % 2 === 0){
    return 'table-row'
  }
  return ''
}

/** 新增按钮操作 */
function handleAdd(){
  reset()
  open.value = true
  title.value = "添加调研任务"
}

/** 修改按钮操作 */
function handleUpdate(row){
  reset()
  const _id = row.id || ids.value
  getTask(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改调研任务"
  })
}

/** 提交按钮 */
function submitForm(){
  proxy.$refs["taskRef"].validate(valid => {
    if(valid){
      if(form.value.id != null){
        updateTask(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      }else{
        addTask(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row){
  const _ids = row.id ? [row.id] : ids.value;
  let name = '';
  _ids.forEach(_id => {
    taskList.value.forEach(task => {
      if(task.id === _id)
        name += '【' + task.name + '】';
    })
  })
  proxy.$modal.confirm('是否确认删除' + name + '？').then(function(){
    return delTask(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {
  })
}

/** 查看按钮操作 */
function handleDetail(row){
  router.push({
    path: '/gather/surveyTaskDetail/index',
    query: {
      id: row.id,
      name: row.name,
    }
  });
}

/** 导出按钮操作 */
function handleExport(){
  proxy.download('/gather/surveyTask/export', {
    ...queryParams.value
  }, `调研任务` + dayjs(new Date()).format("YYYYMMDDHHmmss") + `.xlsx`)
}

/** 导出按钮操作 */
function handleExportData(){
  const  _id = ids.value[0]
  let fileName = '';
  taskList.value.forEach(task => {
    if(task.id === _id)
      fileName = '调研任务【' + task.name + '】';
  });
  const newQueryParams = {...queryParams.value};
  newQueryParams.id = _id;
  proxy.download('/gather/surveyTask/exportData', {
    ...newQueryParams
  }, fileName + dayjs(new Date()).format("YYYYMMDDHHmmss") + `.xlsx`);
}

/** 数据大屏 */
function handleDataView(){
  requestFullscreen()
  router.push('/dataView');
  // enter()
}

getList()


/** 根据年、月、周更新任务名称 */
const updateTaskName = () => {
  const {year, month, week} = form.value;
  let name = "";
  if(year != null){
    name = name + `${year}年`;
  }
  if(month != null){
    name = name + `${month}月`;
  }
  if(week != null){
    name = name + `第${week}周调研任务`;
  }
  form.value.name = name;
}

</script>
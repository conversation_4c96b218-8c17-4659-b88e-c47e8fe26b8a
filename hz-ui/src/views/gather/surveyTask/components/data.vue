<template>
  <el-dialog v-model="visible" width="86%" append-to-body class="dialog_component">
    <div class="dialog_header">
      <div class="header_left">
        <img src="@/assets/images/dialog-icon.png" alt="">
        <div class="dialog_title">{{ title }}</div>
      </div>
      <div class="dialog_close" @click="visible = false">
        <el-icon>
          <CloseBold/>
        </el-icon>
      </div>
    </div>
    
    <div v-if="(dataType === '1' && dataOpType === '1') || (dataType === '1' && dataOpType === '2') || (dataType === '1' && dataOpType === '5') ||
               (dataType === '2' && dataOpType === '1') || (dataType === '2' && dataOpType === '2') || (dataType === '2' && dataOpType === '5')">
      <el-table height="700px" v-loading="loading" :data="dataList" :row-class-name="tableRowClassName">
        <el-table-column type="index" width="50" label="序号" align="center" v-if="dataList.length > 0"/>
        
        <el-table-column width="120" label="许可证号" align="left" prop="key1"/>
        <el-table-column width="210" label="客户:名称" align="left" prop="key2" show-overflow-tooltip/>
        <el-table-column width="100" label="客户:法人" align="center" prop="key3"/>
        <el-table-column width="100" label="客户:档位" align="center" prop="key4"/>
        <el-table-column width="100" label="客户:城镇" align="center" prop="key5"/>
        <el-table-column width="100" label="客户:业态" align="center" prop="key6"/>
        <el-table-column width="150" label="客户:营销线" align="center" prop="key7"/>
        
        <el-table-column v-for="(item, ind) in tableHeader.slice(7, tableHeader.length)" :label="item" align="center" :prop="'key' + (ind + 1 + 7)" show-overflow-tooltip>
          <template #default="scope">
            <span :class="getClass(scope.row['key' + (ind + 1 + 7)])" >{{ getValue(scope.row['key' + (ind + 1 + 7)]) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <div v-if="(dataType === '3' && dataOpType === '1') || (dataOpType === '4')">
      <el-table height="700px" v-loading="loading" :data="dataList" :row-class-name="tableRowClassName">
        <el-table-column type="index" width="50" label="序号" align="center" v-if="dataList.length > 0"/>
        <el-table-column v-for="(item, ind) in tableHeader" :label="item" align="center" :prop="'key' + (ind + 1)" show-overflow-tooltip/>
      </el-table>
    </div>
  
  </el-dialog>
</template>
<script setup>
import {statisticTableData} from "@/api/gather/surveyTask.js"

//
const {proxy} = getCurrentInstance();
//
const visible = ref(false);

//
const dataType = ref('0')
const dataOpType = ref('0')
const dataList = ref([])
const tableHeader = ref([])
const loading = ref(true)
const title = ref('')

//--------------------------------------------------------------- 方法管理 ---------------------------------------------------------------------

// 显示弹框
const show = (id, type, opType) => {
  
  console.log(type)
  console.log(opType)
  
  dataType.value = type
  dataOpType.value = opType
  dataList.value = []
  // 标题
  if(type === '1'){
    if(opType === '1'){
      title.value = '价格数据（原始数据）'
    }
    if(opType === '2'){
      title.value = '价格数据（清洗后数据）'
    }
    if(opType === '4'){
      title.value = '价格数据（清洗提示）'
    }
    if(opType === '5'){
      title.value = '价格数据（清洗商户）'
    }
  }
  if(type === '2'){
    if(opType === '1'){
      title.value = '库存数据（原始数据）'
    }
    if(opType === '2'){
      title.value = '库存数据（清洗后数据）'
    }
    if(opType === '4'){
      title.value = '库存数据（清洗提示）'
    }
    if(opType === '5'){
      title.value = '库存数据（清洗商户）'
    }
  }
  if(type === '3'){
    title.value = '四员联动数据'
  }
  visible.value = true;
  
  // 加载数据
  loading.value = true
  let formData = new FormData()
  formData.append("taskId", id)
  formData.append("type", type)
  formData.append("operate", opType)
  statisticTableData(formData).then(res => {
    console.log(res)
    console.log(res)
    console.log(res)
    let dn = []
    if(res.data.tableData && res.data.tableData.length > 0){
      tableHeader.value = res.data.tableHeader
      res.data.tableData.forEach(item => {
        let obj = {}
        item.forEach((it, ind) => {
          obj['key' + (ind + 1)] = it
        })
        dn.push(obj)
      })
    }
    dataList.value = dn
    loading.value = false
  })
}

// 隐藏弹框
const hide = () => {
  visible.value = false;
}

// 表样式
const tableRowClassName = ({row, rowIndex}) => {
  if((rowIndex + 1) % 2 === 0){
    return 'table-row'
  }
  return ''
}

const getClass = (value) => {
  if(dataOpType.value === '5' || (dataType.value === '1' && dataOpType.value === '2') || (dataType.value === '2' && dataOpType.value === '2')){
    if(value.indexOf('数据错误') != -1 || value.indexOf('规则清理') != -1){
      return 'text-[#f40c0c]'
    }
  }
  return ''
}

const getValue = (value) => {
  if(dataOpType.value === '5' || (dataType.value === '1' && dataOpType.value === '2') || (dataType.value === '2' && dataOpType.value === '2')){
    value = value.replace('(数据错误)', '')
    value = value.replace('(规则清理)', '')
    return value
  }
  return value
}

//--------------------------------------------------------------- 数据请求 ---------------------------------------------------------------------


//--------------------------------------------------------------- 事件管理 ---------------------------------------------------------------------


//--------------------------------------------------------------- vue相关 ---------------------------------------------------------------------

onMounted(() => {

})

//--------------------------------------------------------------- 函数暴露 ---------------------------------------------------------------------

defineExpose({
  show,
  hide,
});
</script>

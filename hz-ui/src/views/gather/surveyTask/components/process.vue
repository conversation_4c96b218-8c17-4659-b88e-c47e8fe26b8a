<template>
  <el-dialog v-model="visible" width="86%" append-to-body class="dialog_component">
    <div class="dialog_header">
      <div class="header_left">
        <img src="@/assets/images/dialog-icon.png" alt="">
        <div class="dialog_title">{{ dataType === '2' ? '库存数据清洗' : '价格数据清洗' }}</div>
      </div>
      <div class="dialog_close" @click="visible = false">
        <el-icon>
          <CloseBold/>
        </el-icon>
      </div>
    </div>

    <el-row :gutter="10" class="mb-10px">
      <el-col :span="12">
        <el-form-item label="全局设置规则" prop="name">
          <el-select v-model="formCr" @change="handleFormCr" style="width: 140px;">
            <el-option label="不清洗" value="-1"></el-option>
            <el-option v-for="item in clearRules"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"/>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" class="flex! justify-end">
        <el-button class="btn-edit" plain @click="handleClear">重置所有规则</el-button>
      </el-col>
    </el-row>

    <div v-if="dataType === '1'">
      <el-table height="640px" v-loading="loading" :data="dataList" :row-class-name="tableRowClassName" class="st-table">
        <el-table-column type="index" width="50" label="序号" align="center" v-if="dataList.length > 0">
          <template #default="scope">
            <div v-if="scope.$index !== 0">{{ scope.$index }}</div>
            <div v-else>-</div>
          </template>
        </el-table-column>

        <el-table-column width="120" label="许可证号" align="left" prop="key1">
          <template #default="scope">
            <div v-if="scope.$index !== 0">{{ scope.row.key1 }}</div>
            <div v-else></div>
          </template>
        </el-table-column>
        <el-table-column width="210" label="客户:名称" align="left" prop="key2" show-overflow-tooltip><template #default="scope"><div v-if="scope.$index !== 0">{{ scope.row.key2 }}</div><div v-else></div></template></el-table-column>
        <el-table-column width="85" label="客户:法人" align="center" prop="key3"><template #default="scope"><div v-if="scope.$index !== 0">{{ scope.row.key3 }}</div><div v-else></div></template></el-table-column>
        <el-table-column width="85" label="客户:档位" align="center" prop="key4"><template #default="scope"><div v-if="scope.$index !== 0">{{ scope.row.key4 }}</div><div v-else></div></template></el-table-column>
        <el-table-column width="85" label="客户:城镇" align="center" prop="key5"><template #default="scope"><div v-if="scope.$index !== 0">{{ scope.row.key5 }}</div><div v-else></div></template></el-table-column>
        <el-table-column width="85" label="客户:业态" align="center" prop="key6"><template #default="scope"><div v-if="scope.$index !== 0">{{ scope.row.key6 }}</div><div v-else></div></template></el-table-column>
        <el-table-column width="150" label="客户:营销线" align="center" prop="key7"><template #default="scope"><div v-if="scope.$index !== 0">{{ scope.row.key7 }}</div><div v-else></div></template></el-table-column>

        <el-table-column v-for="(item, ind) in tableHeader.slice(7, tableHeader.length)" :label="item" align="center" width="180" :prop="'key' + (ind + 1 + 7)" show-overflow-tooltip>
          <template #default="scope">
            <div v-if="scope.$index !== 0">{{ getValue(scope.row['key' + (ind + 1 + 7)]) }}</div>
            <div v-else>
              <el-select class="not-bg" v-model="scope.row['key' + (ind + 1 + 7)]" style="width: 140px;">
                <el-option label="不清洗" value="-1"></el-option>
                <el-option v-for="item in clearRules"
                           :key="item.value"
                           :label="item.label"
                           :value="item.value"/>
              </el-select>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div v-if="dataType === '2'">
      <el-table height="640px" v-loading="loading" :data="dataList" :row-class-name="tableRowClassName" class="st-table">
        <el-table-column type="index" width="50" label="序号" align="center" v-if="dataList.length > 0">
          <template #default="scope">
            <div v-if="scope.$index > 0">{{ scope.$index }}</div>
            <div v-else>-</div>
          </template>
        </el-table-column>

        <el-table-column width="120" label="许可证号" align="left" prop="key1">
          <template #default="scope">
            <div v-if="scope.$index !== 0">{{ scope.row.key1 }}</div>
            <div v-else></div>
          </template>
        </el-table-column>
        <el-table-column width="150" label="客户:名称"   align="left"   prop="key2" show-overflow-tooltip><template #default="scope"><div v-if="scope.$index !== 0">{{ scope.row.key2 }}</div><div v-else></div></template></el-table-column>
        <el-table-column width="85"  label="客户:法人"   align="center" prop="key3"><template #default="scope"><div v-if="scope.$index !== 0">{{ scope.row.key3 }}</div><div v-else></div></template></el-table-column>
        <el-table-column width="85"  label="客户:档位"   align="center" prop="key4"><template #default="scope"><div v-if="scope.$index !== 0">{{ scope.row.key4 }}</div><div v-else></div></template></el-table-column>
        <el-table-column width="85"  label="客户:城镇"   align="center" prop="key5"><template #default="scope"><div v-if="scope.$index !== 0">{{ scope.row.key5 }}</div><div v-else></div></template></el-table-column>
        <el-table-column width="85"  label="客户:业态"   align="center" prop="key6"><template #default="scope"><div v-if="scope.$index !== 0">{{ scope.row.key6 }}</div><div v-else></div></template></el-table-column>
        <el-table-column width="150" label="客户:营销线" align="center" prop="key7"><template #default="scope"><div v-if="scope.$index !== 0">{{ scope.row.key7 }}</div><div v-else></div></template></el-table-column>

        <el-table-column v-for="(item, ind) in tableHeader.slice(7, tableHeader.length)" :label="item" align="center" width="180" :prop="'key' + (ind + 1 + 7)" show-overflow-tooltip>
          <template #default="scope">
            <div v-if="scope.$index !== 0">{{ getValue(scope.row['key' + (ind + 1 + 7)]) }}</div>
            <div v-else>
              <el-select class="not-bg" v-model="scope.row['key' + (ind + 1 + 7)]" style="width: 140px;">
                <el-option label="不清洗" value="-1"></el-option>
                <el-option v-for="item in clearRules"
                           :key="item.value"
                           :label="item.label"
                           :value="item.value"/>
              </el-select>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button class="dialog_submit" @click="submitForm">确 定</el-button>
        <el-button class="dialog_cancel" @click="visible = false">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import {statisticTableData, ruleCleanData} from "@/api/gather/surveyTask.js"
import {listAllRules} from "@/api/gather/rules.js"
import {formatDate} from '@/utils/index.js'

const {proxy} = getCurrentInstance();
const visible = ref(false);

const id = ref(0)
const dataType = ref('')
const dataList = ref([])
const tableHeader = ref([])
const loading = ref(true)
const title = ref('')

const formCr = ref()
const clearRules = ref([]);

//--------------------------------------------------------------- 方法管理 ---------------------------------------------------------------------

// 显示弹框
const show = (_id, type) => {
  id.value = _id
  dataType.value = type
  dataList.value = []
  visible.value = true;

  // 加载数据
  loading.value = true
  let formData = new FormData()
  formData.append("taskId", _id)
  formData.append("type", type)
  formData.append("operate", '2')
  statisticTableData(formData).then(res => {
    let dn = []
    if(res.data.tableData && res.data.tableData.length > 0){
      tableHeader.value = res.data.tableHeader
      res.data.tableData.forEach(item => {
        let obj = {}
        item.forEach((it, ind) => {
          obj['key' + (ind + 1)] = it
        })
        dn.push(obj)
      })
      let first = {}
      tableHeader.value.forEach((item, ind) => {
        first['key' + (ind + 1)] = '-1'
      })
      dn.unshift(first)
    }

    dataList.value = dn
    loading.value = false
  })

  // 获取清洗规则
  let params = {
    unenable: 0,
    type: type,
  }
  listAllRules(params).then(res => {
    if(res.code === 200){
      clearRules.value = [];
      res.data.forEach(item => {
        clearRules.value.push({
          label: item.name,
          value: item.id
        });
      });
    }else{
      proxy.$message.error(res.msg);
    }
  })
}

// 隐藏弹框
const hide = () => {
  visible.value = false;
}

// 表样式
const tableRowClassName = ({row, rowIndex}) => {
  if((rowIndex + 1) % 2 === 0){
    return 'table-row'
  }
  return ''
}

const getValue = (value) => {
  console.log(value)
  value = value.replace('(数据错误)', '')
  value = value.replace('(规则清理)', '')
  return value
}

//--------------------------------------------------------------- 数据请求 ---------------------------------------------------------------------


//--------------------------------------------------------------- 事件管理 ---------------------------------------------------------------------


const handleFormCr = () => {
  const temp = JSON.parse(JSON.stringify(dataList.value))
  let first = {}
  tableHeader.value.forEach((item, ind) => {
    first['key' + (ind + 1)] = formCr.value
  })
  temp.splice(0, 1);
  temp.unshift(first)
  dataList.value = temp
}

const handleClear = () => {
  const temp = JSON.parse(JSON.stringify(dataList.value))
  let first = {}
  tableHeader.value.forEach((item, ind) => {
    first['key' + (ind + 1)] = '-1'
  })
  temp.splice(0, 1);
  temp.unshift(first)
  dataList.value = temp
}

const submitForm = () => {
  const row0 = dataList.value[0]
  let crIds = []
  let spNames = []
  tableHeader.value.forEach((item, ind) => {
    if(ind > 6){
      crIds.push(row0['key' + (ind + 1)])
      spNames.push(item)
    }
  })

  proxy.$modal.loading("数据提交中，请稍候...")
  ruleCleanData({
    id: id.value,
    type: dataType.value,
    crIds: crIds,
    spNames: spNames
  }).then(res => {
    proxy.$modal.closeLoading()
    proxy.$modal.msgSuccess("清理完毕");
    formCr.value = ''
    hide()
  })
}

//--------------------------------------------------------------- vue相关 ---------------------------------------------------------------------

onMounted(() => {
})

//--------------------------------------------------------------- 函数暴露 ---------------------------------------------------------------------

defineExpose({
  show,
  hide,
});
</script>

<style>
.st-table {
}

.not-bg {
  .el-select__wrapper {
    box-shadow: none !important;
    background-color: #fff !important;
    min-height: 27px !important;
  }

  .el-select__selected-item {
    color: #21A042;
  }
}
</style>

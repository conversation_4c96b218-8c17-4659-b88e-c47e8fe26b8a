<template>
  <div class="app-container">
    <div class="from_header">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
        <el-form-item label="品规名称" prop="name">
          <el-input v-model="queryParams.name"
                    placeholder="请输入卷烟品规名称"
                    clearable
                    @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item label="建议零售价" prop="lsPrice">
          <el-input v-model="queryParams.lsPrice"
                    placeholder="请输入建议零售价"
                    clearable
                    @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item label="批发价" prop="pfPrice">
          <el-input v-model="queryParams.pfPrice"
                    placeholder="请输入批发价"
                    clearable
                    @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item>
          <el-button class="btn-search" icon="Search" @click="handleQuery">查询</el-button>
          <el-button class="btn-refresh" icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="from_container">
      <el-row :gutter="10" class="">
        <el-col :span="1.5">
          <el-button class="btn-add" plain icon="Plus"
                     @click="handleAdd"
                     v-hasPermi="['gather:smokePrice:add']">新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button class="btn-edit" plain icon="Edit"
                     :disabled="single"
                     @click="handleUpdate"
                     v-hasPermi="['gather:smokePrice:edit']">修改
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button class="btn-delete" plain icon="Delete"
                     :disabled="multiple"
                     @click="handleDelete"
                     v-hasPermi="['gather:smokePrice:remove']">删除
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button class="btn-add" plain icon="Plus"
                     @click="handleImport"
                     v-hasPermi="['gather:smokePrice:query']">导入
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button class="btn-download" plain icon="Download"
                     @click="handleExport"
                     v-hasPermi="['gather:smokePrice:query']">导出
          </el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="gatherPriceList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName" class="mt-20px!">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="品规名称" align="center" prop="name"/>
        <el-table-column label="建议零售价" align="center" prop="lsPrice"/>
        <el-table-column label="批发价" align="center" prop="pfPrice"/>
        <el-table-column label="备注" align="center" prop="remark"/>
        <el-table-column label="创建时间" align="center" prop="createTime"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <div class="table-btn">
              <el-button link class="table-btn-edit" @click="handleUpdate(scope.row)" v-hasPermi="['gather:smokePrice:edit']">修改</el-button>
              <div class="btn-line"></div>
              <el-button link class="table-btn-delete" @click="handleDelete(scope.row)" v-hasPermi="['gather:smokePrice:remove']">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList"/>
    </div>

    <!-- 添加或修改卷烟价格对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body class="dialog_component">
      <div class="dialog_header">
        <div class="header_left">
          <img src="@/assets/images/dialog-icon.png" alt="">
          <div class="dialog_title">
            {{ title }}
          </div>
        </div>
        <div class="dialog_close" @click="cancel">
          <el-icon>
            <CloseBold/>
          </el-icon>
        </div>
      </div>
      <el-form ref="gatherPriceRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="品规名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入品规名称"/>
        </el-form-item>
        <el-form-item label="建议零售价" prop="lsPrice">
          <el-input v-model="form.lsPrice" placeholder="请输入建议零售价"/>
        </el-form-item>
        <el-form-item label="批发价" prop="pfPrice">
          <el-input v-model="form.pfPrice" placeholder="请输入批发价"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="dialog_submit" @click="submitForm">确 定</el-button>
          <el-button class="dialog_cancel" @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 数据导入对话框 -->
    <el-dialog :title="importTitle" v-model="importOpen" width="500px" append-to-body class="dialog_component">
      <div class="dialog_header">
        <div class="header_left">
          <img src="@/assets/images/dialog-icon.png" alt="">
          <div class="dialog_title">{{ importTitle }}</div>
        </div>
        <div class="dialog_close" @click="importOpen = false">
          <el-icon>
            <CloseBold/>
          </el-icon>
        </div>
      </div>
      <el-upload ref="uploadRef"
                 :headers="headers"
                 :action=uploadFileUrl
                 :auto-upload="false"
                 accept=".xlsx,.xls"
                 :limit="1"
                 :on-exceed="handleExceed"
                 :on-success="handleFileSuccess" drag>
        <div class="el-upload__text w-full flex flex-row items-center justify-center">
          <div class=" flex flex-row items-center justify-center w-120px" style="border: 1px solid #d0d0d0; border-radius: 3px; padding: 4px 12px 4px 12px;">
            <el-icon class="mr-5px" color="#21a042">
              <Upload/>
            </el-icon>
            <span>上传文件</span>
          </div>
        </div>
        <template #tip>
          <div class="el-upload__tip text-left">
            <span style="color: red;">● 重复导入会覆盖上次数据。</span>
          </div>
          <div class="el-upload__tip text-left">
            <span style="color: red;">● 重复导入同一种香烟品规价格，仅会保留最后一次价格数据。</span>
          </div>
          <div class="el-upload__tip text-left">
            <span>● 仅允许导入xls、xlsx格式文件。</span>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="dialog_submit" @click="submitFileForm">确 定</el-button>
          <el-button class="dialog_cancel" @click="importOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GatherSmokePrice">
import {listGatherSmokePrice, getGatherSmokePrice, delGatherSmokePrice, addGatherSmokePrice, updateGatherSmokePrice} from "@/api/gather/smokePrice.js"
import dayjs from "dayjs";
import {getToken} from '@/utils/auth.js'

const {proxy} = getCurrentInstance()

const gatherPriceList = ref([])
const title = ref("")
const open = ref(false)
const importOpen = ref(false)
const importTitle = ref("")
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const headers = ref({Authorization: "Bearer " + getToken()})
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/gather/smokePrice/import")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    lsPrice: null,
    pfPrice: null,
  },
  rules: {
    name: [
      {required: true, message: "卷烟品规名称不能为空", trigger: "blur"}
    ],
    lsPrice: [
      {required: true, message: "建议零售价不能为空", trigger: "blur"}
    ],
    pfPrice: [
      {required: true, message: "批发价不能为空", trigger: "blur"}
    ],
  }
})

const {queryParams, form, rules} = toRefs(data)

/** 查询卷烟价格列表 */
function getList(){
  loading.value = true
  listGatherSmokePrice(queryParams.value).then(response => {
    gatherPriceList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel(){
  open.value = false
  reset()
}

// 表单重置
function reset(){
  form.value = {
    id: null,
    name: null,
    lsPrice: null,
    pfPrice: null,
    remark: null
  }
  proxy.resetForm("gatherPriceRef")
}

/** 搜索按钮操作 */
function handleQuery(){
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery(){
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection){
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

const tableRowClassName = ({row, rowIndex}) => {
  if((rowIndex + 1) % 2 === 0){
    return 'table-row'
  }
  return ''
}

/** 新增按钮操作 */
function handleAdd(){
  reset()
  open.value = true
  title.value = "添加卷烟价格"
}

/** 修改按钮操作 */
function handleUpdate(row){
  reset()
  const _id = row.id || ids.value
  getGatherSmokePrice(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改卷烟价格"
  })
}

/** 提交按钮 */
function submitForm(){
  proxy.$refs["gatherPriceRef"].validate(valid => {
    if(valid){
      if(form.value.id != null){
        updateGatherSmokePrice(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      }else{
        addGatherSmokePrice(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row){
  const _ids = row.id ? [row.id] : ids.value;
  let name = '';
  _ids.forEach(id => {
    const item = gatherPriceList.value.find(item => item.id === id);
    if(item){
      name += '【' + item.name + '】';
    }
  });
  proxy.$modal.confirm('是否确认删除"' + name + '"的数据项？').then(function(){
    return delGatherSmokePrice(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {
  })
}

/** 导出按钮操作 */
function handleExport(){
  proxy.download('gather/smokePrice/export', {
    ...queryParams.value
  }, `香烟价格` + dayjs(new Date()).format("YYYYMMDDHHmmss") + `.xlsx`)
}

// 导入
const handleImport = () => {
  importTitle.value = "导入卷烟价格"
  importOpen.value = true
}

// 数据导入：文件校验
function handleExceed(files, fileList){
  proxy.$modal.msgWarning('只能上传一个文件，请移除多余文件！');
}

// 数据导入：结束事件
const handleFileSuccess = (response, file, fileList) => {
  proxy.$modal.closeLoading()
  if(response.code === 200){
    proxy.$modal.msgSuccess("导入成功");
  }else{
    proxy.$modal.msgError("导入失败");
  }
  proxy.$refs["uploadRef"].handleRemove(file)
  getList()
  importOpen.value = false
}

// 数据导入: 提交
function submitFileForm(){
  proxy.$modal.loading("数据提交中，请稍候...")
  proxy.$refs["uploadRef"].submit()
}

getList()
</script>

<template>
  <div class="app-container">
    <div class="from_header">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="queryParams.name"
                    placeholder="请输入规则名称"
                    clearable
                    @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item>
          <el-button class="btn-search" icon="Search" @click="handleQuery">查询</el-button>
          <el-button class="btn-refresh" icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="from_container">
      <el-row :gutter="10">
        <el-col :span="1.5">
          <el-button class="btn-add" plain icon="Plus"
                     @click="handleAdd"
                     v-hasPermi="['gather:cleanRules:add']">新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button class="btn-edit" plain icon="Edit"
                     :disabled="single"
                     @click="handleUpdate"
                     v-hasPermi="['gather:cleanRules:edit']">修改
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger"
                     plain
                     icon="Delete"
                     :disabled="multiple"
                     @click="handleDelete"
                     v-hasPermi="['gather:cleanRules:remove']">删除
          </el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="rulesList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName" class="mt-20px!">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="规则名称" align="center" prop="name"/>
        <el-table-column label="规则类型" align="center" prop="type">
          <template #default="scope">
            <div class="flex flex-row items-center justify-center">
              <dict-tag :options="sjcj_qlgz_type" :value="scope.row.type"/>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="下限" align="center">
          <template #default="scope">
            <div v-if="scope.row.type === '1'">
              <span v-if="scope.row.floorPfPriceEnable === '1'">{{ '批发价 * ' + scope.row.floorRatio + '%'  }}</span>
              <span v-else>{{ '建议零售价 * ' + scope.row.floorRatio + '%'  }}</span>
            </div>
            <div v-else-if="scope.row.type === '3'">
              {{ scope.row.floorRatio }} * IRQ
            </div>
            <div v-else>
              {{ scope.row.floorNum }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="上限" align="center">
          <template #default="scope">
            <div v-if="scope.row.type === '1'">
              <span v-if="scope.row.ceilPfPriceEnable === '1'">{{ '批发价 * ' + scope.row.ceilRatio + '%'  }}</span>
              <span v-else>{{ '建议零售价 * ' + scope.row.ceilRatio + '%'  }}</span>
            </div>
            <div v-else-if="scope.row.type === '3'">
              {{ scope.row.ceilRatio }} * IRQ
            </div>
            <div v-else>
              {{ scope.row.ceilNum }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <div class="table-btn">
              <el-button link class="table-btn-edit" @click="handleUpdate(scope.row)"
                         v-hasPermi="['gather:cleanRules:edit']">修改
              </el-button>
              <div class="btn-line"></div>
              <el-button link class="table-btn-delete" @click="handleDelete(scope.row)"
                         v-hasPermi="['gather:cleanRules:remove']">删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total"
                  :total="total"
                  v-model:page="queryParams.pageNum"
                  v-model:limit="queryParams.pageSize"
                  @pagination="getList"/>
    </div>

    <!-- 添加或修改香烟数据清理规则对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body class="dialog_component">
      <div class="dialog_header">
        <div class="header_left">
          <img src="@/assets/images/dialog-icon.png" alt="">
          <div class="dialog_title">
            {{ title }}
          </div>
        </div>
        <div class="dialog_close" @click="cancel">
          <el-icon>
            <CloseBold/>
          </el-icon>
        </div>
      </div>

      <el-form ref="rulesRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入规则名称"/>
        </el-form-item>
        <el-form-item label="规则类型" prop="type" >
          <el-select v-model="form.type" >
            <el-option v-for="dict in sjcj_qlgz_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>

        <el-row :gutter="10" v-if="form.type === '1'">
          <el-col :span="12">
            <el-form-item
              label="下限类型"
              prop="xxPriceType">
              <el-select v-model="form.xxPriceType" >
                <el-option label="批发价" value="批发价"></el-option>
                <el-option label="建议零售价" value="建议零售价"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="下限百分比"
              prop="floorRatio">
              <el-input-number v-model="form.floorRatio" :precision="2" :min="1" :max="100000"  controls-position="right" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" v-if="form.type === '1'">
          <el-col :span="12">
            <el-form-item
              label="上限类型"
              prop="sxPriceType">
              <el-select v-model="form.sxPriceType" >
                <el-option label="批发价" value="批发价"></el-option>
                <el-option label="建议零售价" value="建议零售价"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="上限百分比"
              prop="floorRatio">
              <el-input-number v-model="form.ceilRatio" :precision="2" :min="1" :max="100000"  controls-position="right" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10" v-if="form.type === '2'">
          <el-col :span="12">
            <el-form-item
              label="下限数值"
              prop="floorNum">
              <el-input-number v-model="form.floorNum" :min="0" :max="10000000"  controls-position="right" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="上限数值"
              prop="ceilNum">
              <el-input-number v-model="form.ceilNum" :min="0" :max="10000000"  controls-position="right" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="10" v-if="form.type === '3'">
          <el-col :span="12">
            <el-form-item
              label="下限阀值"
              prop="floorRatio">
              <el-input-number v-model="form.floorRatio" :min="0" :max="10000000" :precision="2" controls-position="right" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="上限阀值"
              prop="ceilRatio">
              <el-input-number v-model="form.ceilRatio" :min="0" :max="10000000" :precision="2" controls-position="right" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="dialog_submit" @click="submitForm">确 定</el-button>
          <el-button class="dialog_cancel" @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Rules">
import {listRules, getRules, delRules, addRules, updateRules} from "@/api/gather/rules"

const {proxy} = getCurrentInstance()
const {sjcj_qlgz_type} = proxy.useDict("sjcj_qlgz_type")

const rulesList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    type: null,
    floorPfPriceEnable: null,
    floorLsPriceEnable: null,
    floorRatio: null,
    floorNum: null,
    ceilPfPriceEnable: null,
    ceilLsPriceEnable: null,
    ceilRatio: null,
    ceilNum: null,
    isDefault: null,
    del: null,
    unenable: null,
  },
  rules: {
    name: [
      {required: true, message: "规则名称不能为空", trigger: "blur"}
    ],
    type: [
      {required: true, message: "规则类型不能为空", trigger: "blur"}
    ],
    xxPriceType: [
      {required: true, message: "下限类型不能为空", trigger: "blur"}
    ],
    sxPriceType: [
      {required: true, message: "上限类型不能为空", trigger: "blur"}
    ],
    floorRatio: [
      {required: true, message: "下限百分比不能为空", trigger: "blur"}
    ],
    ceilRatio: [
      {required: true, message: "上限百分比不能为空", trigger: "blur"}
    ],
    floorNum: [
      {required: true, message: "下限数值不能为空", trigger: "blur"}
    ],
    ceilNum: [
      {required: true, message: "上限数值不能为空", trigger: "blur"}
    ],
  }
})

const {queryParams, form, rules} = toRefs(data)

/** 查询香烟数据清理规则列表 */
function getList() {
  loading.value = true
  listRules(queryParams.value).then(response => {
    rulesList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    type: '1',
    floorPfPriceEnable: null,
    floorLsPriceEnable: null,
    floorRatio: null,
    floorNum: null,
    ceilPfPriceEnable: null,
    ceilLsPriceEnable: null,
    ceilRatio: null,
    ceilNum: null,
    isDefault: null,
    del: null,
    unenable: null,
    createId: null,
    createBy: null,
    createTime: null,
    updateId: null,
    updateBy: null,
    updateTime: null,
    remark: null,
    xxPriceType: null,
    sxPriceType: null,
  }
  proxy.resetForm("rulesRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}
const tableRowClassName = ({ row, rowIndex }) => {
  if ((rowIndex + 1) % 2 === 0) {
    return 'table-row'
  }
  return ''
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加清理规则"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getRules(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改清理规则"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["rulesRef"].validate(valid => {
    if (valid) {
      if(form.value.type === '1'){
        form.value.floorPfPriceEnable = form.value.xxPriceType === '批发价' ? '1' : '0'
        form.value.floorLsPriceEnable = form.value.xxPriceType === '批发价' ? '0' : '1'
        form.value.ceilPfPriceEnable = form.value.sxPriceType === '批发价' ? '1' : '0'
        form.value.ceilLsPriceEnable = form.value.sxPriceType === '批发价' ? '0' : '1'
      }
      if(form.type === '1' && form.value.floorRatio >= form.value.ceilRatio){
        proxy.$modal.msgError("下限百分比不可大于等于上限百分比")
        return;
      }
      if(form.type === '2' && form.value.floorNum >= form.value.ceilNum){
        proxy.$modal.msgError("下限数值不可大于等于上限数值")
        return
      }
      if (form.value.id != null) {
        updateRules(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addRules(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除香烟数据清理规则编号为"' + _ids + '"的数据项？').then(function () {
    return delRules(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {
  })
}

getList()
</script>

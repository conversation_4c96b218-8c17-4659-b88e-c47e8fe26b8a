<template>
  <el-dialog v-model="visible" style="top: 40px;" width="86%" append-to-body :show-close="false" class="dv_dialog_component">
    <div class="flex flex-row justify-center w-full text-[#FFFFFF] fs-22 font-bold" style="position: relative;">
      <div>{{ classify === 'price' ? '价格' : '库存' }}累计箱线图（业态）</div>
      <img @click="visible = false" src="@/assets/images/sjcj_close.png" alt="close" style="position: absolute; right: 0; top: -2px;"/>
    </div>
    <div class="w-full h-700px mt-30px" style="padding: 0 20px 0 20px">
      <condition ref="condComp" :smoke-change="smokeChangeByLjXxtYt" yt :ytChange="ytChange" />
      <div class="lj-bar" style="overflow-x: auto;">
        <div class="h-668px br-3" :style="{ width: compWidth }">
          <div ref="echartLjComp" class="wh-full"></div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script setup>
import {leijApi} from '@/api/gather/dataView.js'
import {
  setXxtColorAndYMax,
  xxtOptionByLjJg,
  xxtOptionByLjKc,
} from '@/utils/dv.js'
import condition from './condition.vue'
import * as echarts from 'echarts';

//
const {proxy} = getCurrentInstance()

//
const visible = ref(false);

//
const classify = ref()
const spId = ref()

//
const condComp = ref()
const echartLjComp = ref()
const compWidth = ref('100%')

// echart对象
let echartLj = null

//--------------------------------------------------------------- 方法管理 ---------------------------------------------------------------------

// 显示弹框
const show = (_classify, _spListData, _spId, _ytValue) => {
  visible.value = true;
  classify.value = _classify
  spId.value = _spId
  // 设置品规列表
  nextTick(() => {
    condComp.value.setSpDataList(_spListData, spId.value)
  })
  // 累计
  const params = { isAll: "1", classify: classify.value, spId: spId.value, xxtYtLoad: '1', ytValue: _ytValue }
  leijRequest(params)
}

// 隐藏弹框
const hide = () => {
  visible.value = false;
}

//--------------------------------------------------------------- 数据请求 ---------------------------------------------------------------------

// 累计
const leijRequest = (params) => {
  leijApi(params).then(res => {
    // 箱线图：档位
    if (params.xxtYtLoad && params.xxtYtLoad === '1') {
      const obj = {
        xData1: res.data.xData1,
        xData2: res.data.xData2,
        sData: res.data.sData4,
        zxData1: res.data.zxData1_4,
        zxData2: res.data.zxData2_4,
        lsj: res.data.lsj,
        pfj: res.data.pfj,
      }
      setXxtColorAndYMax(obj)
      //
      if(res.data.sData4 && res.data.sData4.length > 10){
        compWidth.value = res.data.sData4.length * 225 + 'px'
      }
      //
      nextTick(() => {
        if (!echartLj) {
          echartLj = echarts.init(echartLjComp.value);
        } else {
          echartLj.dispose()
          echartLj = echarts.init(echartLjComp.value);
        }
        echartLj.setOption(classify.value === 'price' ? xxtOptionByLjJg('2', obj) : xxtOptionByLjKc('2', obj));
      })
    }
  })
}

//--------------------------------------------------------------- 事件管理 ---------------------------------------------------------------------

// 箱线图（累计）：品规切换（业态）
const smokeChangeByLjXxtYt = (spId, yt) => {
  const params = { isAll: "1", classify: classify.value, spId: spId, xxtYtLoad: '1', ytValue: yt }
  leijRequest(params)
}

// 箱线图（累计）：业态切换
const ytChange = (yt, spId) => {
  const params = { isAll: "1", classify: classify.value, spId: spId, xxtYtLoad: '1', ytValue: yt }
  leijRequest(params)
}

//--------------------------------------------------------------- vue相关 ---------------------------------------------------------------------

onMounted(() => {

})

//--------------------------------------------------------------- 函数暴露 ---------------------------------------------------------------------

defineExpose({
  show,
  hide,
});
</script>

<style>
.dv_dialog_component {
  background-color: #061526;
  border: 1px solid #3094d5;
}

.dv_dialog_component .el-dialog__header {
  padding-bottom: 0 !important;
}

/** 滚动条 */
.lj-bar::-webkit-scrollbar{
  width: 5px;
  height: 6px;
}
.lj-bar::-webkit-scrollbar-track{
  background: #061526;
  border-radius:2px;
}
.lj-bar::-webkit-scrollbar-thumb{
  background-color: #3094d5;
  transition: background-color .3s;
}
.lj-bar::-moz-scrollbar{
  width: 5px;
  height: 6px;
}
.lj-bar::-moz-scrollbar-track{
  background: #FFFFFF;
  border-radius:2px;
}
.lj-bar::-moz-scrollbar-thumb{
  background-color: #3094d5;
  transition: background-color .3s;
}
.lj-bar::-o-scrollbar{
  width: 5px;
  height: 6px;
}
.lj-bar::-o-scrollbar-track{
  background: #FFFFFF;
  border-radius:2px;
}
.lj-bar::-o-scrollbar-thumb{
  background-color: #3094d5;
}
</style>

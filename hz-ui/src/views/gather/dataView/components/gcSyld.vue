<template>
  <el-dialog v-model="visible" style="top: 40px;" width="86%" append-to-body :show-close="false" class="dv_dialog_component">
    <div class="flex flex-row justify-center w-full text-[#FFFFFF] fs-22 font-bold" style="position: relative;">
      <div>该次四员联动</div>
      <img @click="visible = false" src="@/assets/images/sjcj_close.png" alt="close" style="position: absolute; right: 0; top: -2px;"/>
    </div>
    <div class="w-full h-650px " style="padding: 15px 15px 15px 15px;">
      <div class="text-[#FFFFFF]" >
        <div class="flex w-full h-40px mt-10px bg-[#709fff]">
          <div class="text-center fs-15 flex items-center justify-center" style="flex: 1;">提交时间</div>
          <div class="text-center fs-15 flex items-center justify-center" style="flex: 1; border-left: 1px solid #51acbc; border-right: 1px solid #51acbc;">提交人</div>
          <div class="text-center fs-15 flex items-center justify-center" style="flex: 1;">详情</div>
        </div>
        <div class="w-full h-570px syld-bar" style="overflow-y: auto; border-bottom: 1px solid #51acbc; border-left: 1px solid #51acbc; border-right: 1px solid #51acbc; ">
          <div class="flex h-44px text-[#FFFFFF]" v-for="(item) in dataList" style="">
            <div class="text-center fs-13 flex items-center justify-center" style="flex: 1; border-bottom: 1px solid #51acbc; ">{{ item.tjsj }}</div>
            <div class="text-center fs-13 flex items-center justify-center" style="flex: 1; border-left: 1px solid #51acbc; border-right: 1px solid #51acbc; border-bottom: 1px solid #51acbc;">{{ item.tjr }}</div>
            <div class="text-center fs-13 flex items-center justify-center" style="flex: 1;border-bottom: 1px solid #51acbc;">{{ item.content }}</div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script setup>
import condition from './condition.vue'

//
const { proxy } = getCurrentInstance()

//
const visible = ref(false);

//
const dataList = ref()

//--------------------------------------------------------------- 方法管理 ---------------------------------------------------------------------

// 显示弹框
const show = (_dataList) => {
  visible.value = true;
  dataList.value = _dataList
}

// 隐藏弹框
const hide = () => {
  visible.value = false;
}

const tableRowClassName = ({ row, rowIndex }) => {
  if ((rowIndex + 1) % 2 === 0) {
    return 'table-row'
  }
  return ''
}

//--------------------------------------------------------------- 数据请求 ---------------------------------------------------------------------


//--------------------------------------------------------------- 事件管理 ---------------------------------------------------------------------


//--------------------------------------------------------------- vue相关 ---------------------------------------------------------------------

onMounted(() => {

})

//--------------------------------------------------------------- 函数暴露 ---------------------------------------------------------------------

defineExpose({
  show,
  hide,
});
</script>

<style>
.dv_dialog_component{
  background-color: #061526;
  border: 1px solid #3094d5;
}
.dv_dialog_component .el-dialog__header{
  padding-bottom: 0 !important;
}

/** 滚动条 */
.syld-bar::-webkit-scrollbar{
  width: 1px;
  height: 1px;
}
.syld-bar::-webkit-scrollbar-track{
  background: #061526;
  border-radius:2px;
}
.syld-bar::-webkit-scrollbar-thumb{
  background-color: #3094d5;
  transition: background-color .3s;
}
.syld-bar::-moz-scrollbar{
  width: 1px;
  height: 1px;
}
.syld-bar::-moz-scrollbar-track{
  background: #FFFFFF;
  border-radius:2px;
}
.syld-bar::-moz-scrollbar-thumb{
  background-color: #3094d5;
  transition: background-color .3s;
}
.syld-bar::-o-scrollbar{
  width: 1px;
  height: 1px;
}
.syld-bar::-o-scrollbar-track{
  background: #FFFFFF;
  border-radius:2px;
}
.syld-bar::-o-scrollbar-thumb{
  background-color: #3094d5;
}
</style>

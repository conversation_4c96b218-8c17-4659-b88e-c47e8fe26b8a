<template>
  <div class="h-32px flex fs-14 flex-row items-center justify-between text-[#FFFFFF] font-bold pl-3px pr-3px " >
    <div v-if="title && title.length > 0" class="flex items-center ml-3px">
      <img src="@/assets/images/sjcj_jt.png" class="mr-3px" alt="jt" />
      <span>{{ title }}</span>
    </div>
    <div v-else>  </div>
    <div class="flex flex-row">
      <div v-if="tl" class="flex flex-row">
        <div v-for="item in tlList" class="flex flex-row items-center">
          <div class="w-15px h-15px mr-3px" :style="item.style"></div>
          <div class="mr-15px">{{ item.name }}</div>
        </div>
      </div>
      <div v-if="smoke" >
        <div class="pl-5px pr-5px cursor-pointer" >
          <el-popover ref="smokeComp" popper-class="sjcj-popover" placement="bottom-start" :width="400" trigger="click" >
            <template #reference>
              <div class="flex flex-row items-center justify-center">
                <div>{{ smokeLabel }}</div>
                <img src="@/assets/images/sjcj_xjt.png" class="ml-3px w-12px h-10px" alt="jt" />
              </div>
            </template>
            <div class="dv-condition-c dv-bar select-menu-box text-[#FFFFFF]" style="max-height: 500px; overflow-y: auto;">
              <div class="dv-condition-sw">
                <div style="border: 1px solid #0b49a4; border-radius: 5px; background-color: #041022;">
                  <el-input v-model="search" @input="handleInput" placeholder="请输入关键字搜索" />
                </div>
              </div>
              <div class="select-menu-item" v-for="(item, index) in spDataList" :key="index" @click="smokeClick(item.name, item.id)" >{{ item.name }}</div>
            </div>
          </el-popover>
        </div>
      </div>
      <div v-if="dw">
        <div class="pl-5px pr-5px cursor-pointer">
          <el-popover ref="dwComp" popper-class="sjcj-popover" placement="bottom-start" :width="400" trigger="click">
            <template #reference>
              <div class="flex flex-row items-center justify-center">
                <div>{{ dwLabel }}</div>
                <img src="@/assets/images/sjcj_xjt.png" class="ml-3px w-12px h-10px" alt="jt" />
              </div>
            </template>
            <div class="select-menu-box text-[#FFFFFF]">
              <div class="select-menu-item" @click="dwClick('1-5档', '1')" >1-5档</div>
              <div class="select-menu-item" @click="dwClick('6-10档', '2')" >6-10档</div>
              <div class="select-menu-item" @click="dwClick('11-15档', '3')" >11-15档</div>
              <div class="select-menu-item" @click="dwClick('16-20档', '4')" >16-20档</div>
              <div class="select-menu-item" @click="dwClick('21-25档', '5')" >21-25档</div>
              <div class="select-menu-item" @click="dwClick('26-30档', '6')" >26-30档</div>
            </div>
          </el-popover>
        </div>
      </div>
      <div v-if="czxc">
        <div class="pl-5px pr-5px cursor-pointer">
          <el-popover ref="czxcComp" popper-class="sjcj-popover" placement="bottom-start" :width="400" trigger="click">
            <template #reference>
              <div class="flex flex-row items-center justify-center">
                <div>{{ czxcLabel }}</div>
                <img src="@/assets/images/sjcj_xjt.png" class="ml-3px w-12px h-10px" alt="jt" />
              </div>
            </template>
            <div class="select-menu-box text-[#FFFFFF]">
              <div class="select-menu-item" @click="czxcClick('城镇', '城镇')" >城镇</div>
              <div class="select-menu-item" @click="czxcClick('乡村', '乡村')" >乡村</div>
            </div>
          </el-popover>
        </div>
      </div>
      <div v-if="yt">
        <div class="pl-5px pr-5px cursor-pointer">
          <el-popover ref="ytComp" popper-class="sjcj-popover" placement="bottom-start" :width="400" trigger="click">
            <template #reference>
              <div class="flex flex-row items-center justify-center">
                <div>{{ ytLabel }}</div>
                <img src="@/assets/images/sjcj_xjt.png" class="ml-3px w-12px h-10px" alt="jt" />
              </div>
            </template>
            <div class="select-menu-box text-[#FFFFFF]">
              <div class="select-menu-item" @click="ytClick('便利店', '便利店')" >便利店</div>
              <div class="select-menu-item" @click="ytClick('烟酒商店', '烟酒商店')" >烟酒商店</div>
              <div class="select-menu-item" @click="ytClick('超市', '超市')" >超市</div>
              <div class="select-menu-item" @click="ytClick('商场', '商场')" >商场</div>
              <div class="select-menu-item" @click="ytClick('娱乐服务业', '娱乐服务业')" >娱乐服务业</div>
              <div class="select-menu-item" @click="ytClick('其他', '其他')" >其他</div>
            </div>
          </el-popover>
        </div>
      </div>
      <div v-if="dim">
        <div class="pl-5px pr-5px cursor-pointer" >
          <el-popover ref="dimComp" popper-class="sjcj-popover" placement="bottom-start" :width="400" trigger="click" >
            <template #reference>
              <div class="flex flex-row items-center justify-center">
                <div>{{ dimLabel }}</div>
                <img src="@/assets/images/sjcj_xjt.png" class="ml-3px w-12px h-10px" alt="jt" />
              </div>
            </template>
            <div class="select-menu-box text-[#FFFFFF]">
              <div class="select-menu-item" @click="dimClick('档位', 'dw')">档位</div>
              <div class="select-menu-item" @click="dimClick('城镇/乡村', 'czxc')">城镇/乡村</div>
              <div class="select-menu-item" @click="dimClick('业态', 'yt')">业态</div>
            </div>
          </el-popover>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

// 属性定义
const props = defineProps({
  title: {
    required: false,
    type: String,
    default: ""
  },
  tl: {
    required: false,
    type: Boolean,
    default: false
  },
  smoke: {
    required: false,
    type: Boolean,
    default: true
  },
  dw: {
    required: false,
    type: Boolean,
    default: false
  },
  czxc: {
    required: false,
    type: Boolean,
    default: false
  },
  yt: {
    required: false,
    type: Boolean,
    default: false
  },
  dim: {
    required: false,
    type: Boolean,
    default: false
  },
  smokeChange: {
    required: false,
    type: Function,
    default: () => {}
  },
  dwChange: {
    required: false,
    type: Function,
    default: () => {}
  },
  czxcChange: {
    required: false,
    type: Function,
    default: () => {}
  },
  ytChange: {
    required: false,
    type: Function,
    default: () => {}
  },
  zztDimChange: {
    required: false,
    type: Function,
    default: () => {}
  },
});

// 图例
const tlList = ref([])

// 卷烟品规
const spDataList = ref([])
const spDataListCopy = ref([])

// 搜索条件
const search = ref('')

// 条件信息
const smokeComp = ref()
const smokeLabel = ref('卷烟品规')
const smokeValue = ref(0)
const dwComp = ref()
const dwLabel = ref('1-5档')
const dwValue = ref('1')
const czxcComp = ref()
const czxcLabel = ref('城镇')
const czxcValue = ref('城镇')
const ytComp = ref()
const ytLabel = ref('便利店')
const ytValue = ref('便利店')
const dimComp = ref()
const dimLabel = ref('档位')
const dimValue = ref('dw')

//--------------------------------------------------------------- 方法管理 ---------------------------------------------------------------------

// 图例列表
const setTlList = (obj) => {
  let arr = []
  const dim = Array.from(obj.dimensions).slice(1);
  for (let i = 0; i < dim.length; i++) {
    const color = obj.series[i].itemStyle
    const borderColor = color.borderColor
    const bgColor = color.color.colorStops[1].color
    arr.push({ name: dim[i], style: "background-color: "+bgColor+"; border: 1px solid "+borderColor+";" })
  }
  tlList.value = arr
}

// 卷烟列表
const setSpDataList = (obj, spId) => {
  if(spId && spId > 0){
    const sp = obj.filter(item => item.id === spId)[0]
    smokeLabel.value = sp.name
    smokeValue.value = sp.id
  } else {
    smokeLabel.value = obj[0].name
    smokeValue.value = obj[0].id
  }
  spDataList.value = obj
  spDataListCopy.value = JSON.parse(JSON.stringify(obj))
}

//--------------------------------------------------------------- 事件管理 ---------------------------------------------------------------------

//
const smokeClick = (label, value) => {
  if(smokeValue.value === value){
    return
  }
  smokeLabel.value = label
  smokeValue.value = value
  if(props.dw){
    props.smokeChange(smokeValue.value, dwValue.value)
  } else if(props.czxc){
    props.smokeChange(smokeValue.value, czxcValue.value)
  } else if(props.yt){
    props.smokeChange(smokeValue.value, ytValue.value)
  } else {
    props.smokeChange(smokeValue.value)
  }
  smokeComp.value.hide()
}

//
const dwClick = (label, value) => {
  if(dwValue.value === value){
    return
  }
  dwLabel.value = label
  dwValue.value = value
  props.dwChange(dwValue.value, smokeValue.value)
  dwComp.value.hide()
}

//
const czxcClick = (label, value) => {
  if(czxcValue.value === value){
    return
  }
  czxcLabel.value = label
  czxcValue.value = value
  props.czxcChange(czxcValue.value, smokeValue.value)
  czxcComp.value.hide()
}

//
const ytClick = (label, value) => {
  if(ytValue.value === value){
    return
  }
  ytLabel.value = label
  ytValue.value = value
  props.ytChange(ytValue.value, smokeValue.value)
  ytComp.value.hide()
}

//
const dimClick = (label, value) => {
  if(dimValue.value === value){
    return
  }
  dimLabel.value = label
  dimValue.value = value
  props.zztDimChange(dimValue.value)
  dimComp.value.hide()
}

// 搜索
const handleInput = () => {
  if(search.value && search.value.length > 0){
    spDataList.value = spDataListCopy.value.filter( item => item.name.indexOf(search.value) !== -1)
  } else {
    spDataList.value = spDataListCopy.value
  }
}

//--------------------------------------------------------------- 方法管理 ---------------------------------------------------------------------

defineExpose({
  setTlList,
  setSpDataList
})

</script>

<style lang='scss' scoped>
.select-menu-box{
  background-color: #061526;
  border: 1px solid #38AAF2;
  border-radius: 5px;
}
.select-menu-item{
  padding: 3px 0px 3px 10px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
</style>
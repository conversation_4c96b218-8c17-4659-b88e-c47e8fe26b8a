<template>
  <el-dialog v-model="visible" style="top: 40px;" width="86%" append-to-body :show-close="false" class="dv_dialog_component">
    <div class="flex flex-row justify-center w-full text-[#FFFFFF] fs-22 font-bold" style="position: relative;">
      <div>该次{{ classify === 'price' ? '价格' : '库存' }}平均值柱状图</div>
      <img @click="visible = false" src="@/assets/images/sjcj_close.png" alt="close" style="position: absolute; right: 0; top: -2px;"/>
    </div>
    <div class="w-full h-700px mt-20px" style="padding: 0 20px 20px 20px">
      <condition ref="condGcZztComp" :smoke="false" :dim="true" tl :zztDimChange="zztDimChange" />
      <div class="zzt-bar mt-10px" style="overflow-x: auto;">
        <div class="h-643px br-3" :style="{ width: zztWidth }">
          <div ref="echartZztComp" class="wh-full"></div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script setup>
import { gaicZztApi} from '@/api/gather/dataView.js'
import { setXxtColorAndYMax, getZztColor, zztOption } from '@/utils/dv.js'
import condition from './condition.vue'
import * as echarts from 'echarts';

//
const { proxy } = getCurrentInstance()

//
const visible = ref(false);

//
const classify = ref()
const stId = ref()

//
let echartZzt = null;
const zztWidth = ref('100%')
const echartZztComp = ref()
const condGcZztComp = ref()

//--------------------------------------------------------------- 方法管理 ---------------------------------------------------------------------

// 显示弹框
const show = (_classify, _stId) => {
  visible.value = true;
  classify.value = _classify
  stId.value = _stId

  const params = { classify: classify.value, stId: stId.value, zztDim: 'dw' }
  gaicZztRequest(params)
}

// 隐藏弹框
const hide = () => {
  visible.value = false;
}

//--------------------------------------------------------------- 数据请求 ---------------------------------------------------------------------

const gaicZztRequest = (params) => {
  gaicZztApi(params).then(res => {
    const obj = {
      dimensions: res.data.gjZztDimensions,
      source: res.data.gjZztSource,
      series: getZztColor(params.zztDim)
    }
    if(res.data.gjZztSource && res.data.gjZztSource.length > 10){
      zztWidth.value = res.data.gjZztSource.length * 150 + 'px'
    }
    nextTick(() => {
      condGcZztComp.value.setTlList(obj)
      if(echartZzt){
        echartZzt.dispose()
        echartZzt = echarts.init(echartZztComp.value);
      } else {
        echartZzt = echarts.init(echartZztComp.value);
      }
      echartZzt.setOption(zztOption(obj));
      // 事件
      echartZzt.getZr().on('click', params => {
        gcZztComp.value.show()
      })
    })
  })
}

//--------------------------------------------------------------- 事件管理 ---------------------------------------------------------------------

// 柱状图：维度切换
const zztDimChange = (zztDim) => {
  const params = { classify: classify.value, stId: stId.value, zztDim: zztDim, }
  gaicZztRequest(params)
}

//--------------------------------------------------------------- vue相关 ---------------------------------------------------------------------

onMounted(() => {

})

//--------------------------------------------------------------- 函数暴露 ---------------------------------------------------------------------

defineExpose({
  show,
  hide,
});
</script>

<style>
.dv_dialog_component{
  background-color: #061526;
  border: 1px solid #3094d5;
}
.dv_dialog_component .el-dialog__header{
  padding-bottom: 0 !important;
}

/** 滚动条 */
.zzt-bar::-webkit-scrollbar{
  width: 5px;
  height: 6px;
}
.zzt-bar::-webkit-scrollbar-track{
  background: #061526;
  border-radius:2px;
}
.zzt-bar::-webkit-scrollbar-thumb{
  background-color: #3094d5;
  transition: background-color .3s;
}
.zzt-bar::-moz-scrollbar{
  width: 5px;
  height: 6px;
}
.zzt-bar::-moz-scrollbar-track{
  background: #FFFFFF;
  border-radius:2px;
}
.zzt-bar::-moz-scrollbar-thumb{
  background-color: #3094d5;
  transition: background-color .3s;
}
.zzt-bar::-o-scrollbar{
  width: 5px;
  height: 6px;
}
.zzt-bar::-o-scrollbar-track{
  background: #FFFFFF;
  border-radius:2px;
}
.zzt-bar::-o-scrollbar-thumb{
  background-color: #3094d5;
}
</style>

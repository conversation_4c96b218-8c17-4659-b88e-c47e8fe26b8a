<template>
  <el-dialog v-model="visible" style="top: 40px;" width="86%" append-to-body :show-close="false" class="dv_dialog_component">
    <div class="flex flex-row justify-center w-full text-[#FFFFFF] fs-22 font-bold" style="position: relative;">
      <div>该次{{ classify === 'price' ? '价格' : '库存' }}箱线图（档位）</div>
      <img @click="visible = false" src="@/assets/images/sjcj_close.png" alt="close" style="position: absolute; right: 0; top: -2px;"/>
    </div>
    <div class="w-full h-700px mt-30px" style="padding: 0 20px 0 20px">
      <condition ref="condComp" :smokeChange="smokeChangeByGcXxtDw" />
      <div class="dvd-bar" style="overflow-x: auto;">
        <div class="h-668px br-3">
          <div ref="echartGcComp" class="wh-full"></div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script setup>
import { gaicApi, gaicSmokePrice } from '@/api/gather/dataView.js'
import { setXxtColorAndYMax, xxtOptionByGcJg, xxtOptionByGcKc } from '@/utils/dv.js'
import condition from './condition.vue'
import * as echarts from 'echarts';

//
const { proxy } = getCurrentInstance()

//
const visible = ref(false);

//
const classify = ref()
const stId = ref()
const spId = ref()

//
const condComp = ref()
const echartGcComp = ref()

// echart对象
let echartGc = null

//--------------------------------------------------------------- 方法管理 ---------------------------------------------------------------------

// 显示弹框
const show = (_classify, _stId, _spId) => {
  visible.value = true;
  classify.value = _classify
  spId.value = _spId
  // 卷烟价格（该次）
  getGaicSmokePrice()
}

// 隐藏弹框
const hide = () => {
  visible.value = false;
}

//--------------------------------------------------------------- 数据请求 ---------------------------------------------------------------------

// 该次的（卷烟列表）
const getGaicSmokePrice = () => {
  gaicSmokePrice({ classify: classify.value }).then(res => {
    if(res.data.stId && res.data.spDataList && res.data.spDataList.length > 0){
      condComp.value.setSpDataList(res.data.spDataList, spId.value)
      // 任务id
      stId.value = res.data.stId
      // 该次
      let params = { classify: classify.value, stId: stId.value, spId: spId.value, xxtDwLoad: '1' }
      gaicRequest(params)
    }
  })
}

// 该次的
const gaicRequest = (params) => {
  gaicApi(params).then(res => {
    // 箱线图：档位统计（某品规）
    if(params.xxtDwLoad && params.xxtDwLoad === '1'){
      const obj = {
        xData: res.data.gjXxtDwX,
        sData: res.data.gjXxtDwData,
        zxData1: res.data.gjXxtDwJzzx,
        lsj: res.data.lsj,
        pfj: res.data.pfj,
        thj: res.data.thj,
      }
      setXxtColorAndYMax(obj)
      if(!echartGc){
        echartGc = echarts.init(echartGcComp.value);
      } else {
        echartGc.dispose()
        echartGc = echarts.init(echartGcComp.value);
      }
      echartGc.setOption(classify.value === 'price' ? xxtOptionByGcJg(obj) : xxtOptionByGcKc(obj));
    }
  })
}

//--------------------------------------------------------------- 事件管理 ---------------------------------------------------------------------

// 箱线图（该次）：品规切换（档位）
const smokeChangeByGcXxtDw = (spId) => {
  const params = { classify: classify.value, stId: stId.value, spId: spId, xxtDwLoad: '1' }
  gaicRequest(params)
}

//--------------------------------------------------------------- vue相关 ---------------------------------------------------------------------

onMounted(() => {

})

//--------------------------------------------------------------- 函数暴露 ---------------------------------------------------------------------

defineExpose({
  show,
  hide,
});
</script>

<style>
.dv_dialog_component{
  background-color: #061526;
  border: 1px solid #3094d5;
}
.dv_dialog_component .el-dialog__header{
  padding-bottom: 0 !important;
}

/** 滚动条 */
.dvd-bar::-webkit-scrollbar{
  width: 5px;
  height: 6px;
}
.dvd-bar::-webkit-scrollbar-track{
  background: #061526;
  border-radius:2px;
}
.dvd-bar::-webkit-scrollbar-thumb{
  background-color: #3094d5;
  transition: background-color .3s;
}
.dvd-bar::-moz-scrollbar{
  width: 5px;
  height: 6px;
}
.dvd-bar::-moz-scrollbar-track{
  background: #FFFFFF;
  border-radius:2px;
}
.dvd-bar::-moz-scrollbar-thumb{
  background-color: #3094d5;
  transition: background-color .3s;
}
.dvd-bar::-o-scrollbar{
  width: 5px;
  height: 6px;
}
.dvd-bar::-o-scrollbar-track{
  background: #FFFFFF;
  border-radius:2px;
}
.dvd-bar::-o-scrollbar-thumb{
  background-color: #3094d5;
}
</style>

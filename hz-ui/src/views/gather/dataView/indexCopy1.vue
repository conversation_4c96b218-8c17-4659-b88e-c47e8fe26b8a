<template>
<!--  <div class="sjcj-wrapper flex wh-full" style="box-sizing: content-box;">-->

<!--    <div class="w-445px h-full " style="padding: 160px 10px 0 30px;">-->

<!--      <div class="h-220px w-full ">-->
<!--        <smokeSelect title="品规整体统计" />-->
<!--        <div class="h-188px br-3">-->
<!--          <div ref="echartLj1Comp" class="wh-full"></div>-->
<!--        </div>-->
<!--      </div>-->

<!--      <div class="h-220px w-full ">-->
<!--        <smokeSelect title="品规档位统计" wd />-->
<!--        <div class="h-188px br-3">-->
<!--          <div ref="echartLj2Comp" class="wh-full"></div>-->
<!--        </div>-->
<!--      </div>-->

<!--      <div class="h-220px w-full ">-->
<!--        <smokeSelect title="品规城镇/乡村统计" czxc/>-->
<!--        <div class="h-188px br-3">-->
<!--          <div ref="echartLj3Comp" class="wh-full"></div>-->
<!--        </div>-->
<!--      </div>-->

<!--      <div class="h-220px w-full ">-->
<!--        <smokeSelect title="品规业态统计" yt/>-->
<!--        <div class="h-188px br-3">-->
<!--          <div ref="echartLj4Comp" class="wh-full"></div>-->
<!--        </div>-->
<!--      </div>-->

<!--    </div>-->

<!--    <div class="flex-grow h-full flex flex-col">-->

<!--      <div class="w-full h-825px flex flex-row">-->

<!--        <div class="w-1005px h-full flex flex-col">-->
<!--          <div class="h-160px flex items-end justify-center" style="position: relative;">-->
<!--            <div class="ml-37px">-->
<!--              <img src="@/assets/images/sjcj_kc_press.png" class="cursor-pointer" alt="jt" />-->
<!--              <img src="@/assets/images/sjcj_jg_normal.png" class="cursor-pointer ml-10px" alt="jt" />-->
<!--            </div>-->
<!--            <smokeSelect  style="position: absolute; right: 10px" />-->
<!--          </div>-->
<!--          <div class="flex-grow" style="padding: 0 10px 5px 15px">-->
<!--            <div ref="echartDtComp" class="wh-full"></div>-->
<!--          </div>-->
<!--        </div>-->

<!--        <div class="flex-grow " style="padding: 145px 32px 0 9px;">-->

<!--          <div class="h-216px w-full ">-->
<!--            <smokeSelect title="品规档位统计" />-->
<!--            <div class="h-184px w-full br-3">-->
<!--              <div ref="echartGc1Comp" class="wh-full"></div>-->
<!--            </div>-->
<!--          </div>-->

<!--          <div class="h-216px w-full ">-->
<!--            <smokeSelect title="品规城镇/乡村统计" />-->
<!--            <div class="h-184px w-full br-3">-->
<!--              <div ref="echartGc2Comp" class="wh-full"></div>-->
<!--            </div>-->
<!--          </div>-->

<!--          <div class="h-216px w-full ">-->
<!--            <smokeSelect title="品规业态统计" />-->
<!--            <div class="h-184px w-full br-3">-->
<!--              <div ref="echartGc3Comp" class="wh-full"></div>-->
<!--            </div>-->
<!--          </div>-->

<!--        </div>-->

<!--      </div>-->

<!--      <div class="w-full flex-grow flex flex-row ">-->

<!--        <div class="w-1170px" style="padding: 40px 9px 0 25px;">-->
<!--          <div class="wh-full ">-->
<!--            <smokeSelect title="品规业态统计" />-->
<!--            <div class="h-144px br-3">-->
<!--              <div ref="echartZztComp" class="wh-full"></div>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->

<!--        <div class="flex-grow" style="padding: 40px 40px 0 27px;">-->
<!--          <div class="flex w-full h-30px mt-10px bg-[#709fff]">-->
<!--            <div class="text-center fs-15 flex items-center justify-center" style="flex: 3;">提交时间</div>-->
<!--            <div class="text-center fs-15 flex items-center justify-center" style="flex: 2;">提交人</div>-->
<!--            <div class="text-center fs-15 flex items-center justify-center" style="flex: 2;">详情</div>-->
<!--          </div>-->
<!--          <div class="flex w-full h-44px text-[#FFFFFF]" v-for="(item) in syldListData">-->
<!--            <div class="text-center fs-13 flex items-center justify-center" style="flex: 3; border-right: 1px solid #51acbc; border-bottom: 1px solid #51acbc; border-left: 1px solid #51acbc; ">{{ item.tjsj }}</div>-->
<!--            <div class="text-center fs-13 flex items-center justify-center" style="flex: 2; border-right: 1px solid #51acbc; border-bottom: 1px solid #51acbc;">{{ item.tjr }}</div>-->
<!--            <div class="text-center fs-13 flex items-center justify-center" style="flex: 2; border-right: 1px solid #51acbc; border-bottom: 1px solid #51acbc;">{{ item.content }}</div>-->
<!--          </div>-->
<!--        </div>-->

<!--      </div>-->

<!--    </div>-->

<!--  </div>-->
</template>

<!--<script setup>-->
<!--import * as echarts from 'echarts';-->
<!--import puyang from './puyang.json';-->
<!--import china from './china.json';-->
<!--import smokeSelect from './components/smokeSelect.vue'-->
<!--import { requestFullscreen } from '@/utils/hz.js'-->
<!--import { gaicApi } from '@/api/gather/dataView.js'-->

<!--// 参考-->
<!--// https://datav.aliyun.com/portal/school/atlas/area_selector-->

<!--//-->
<!--const classify = ref('kc')-->

<!--//-->
<!--const syldListData = ref([])-->

<!--// 图表组件-->
<!--const echartDtComp = ref()-->
<!--const echartLj1Comp = ref()-->
<!--const echartLj2Comp = ref()-->
<!--const echartLj3Comp = ref()-->
<!--const echartLj4Comp = ref()-->
<!--const echartGc1Comp = ref()-->
<!--const echartGc2Comp = ref()-->
<!--const echartGc3Comp = ref()-->
<!--const echartZztComp = ref()-->

<!--//-&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45; 方法管理 -&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;-->

<!--// 地图-->
<!--const dtOption = (obj) => {-->
<!--  return {-->
<!--    visualMap: {-->
<!--      type: 'piecewise',-->
<!--      min: 0,-->
<!--      max: 100,-->
<!--      inRange: { color: ['#38d799', '#ffc35e', '#fab726', '#cf4813', '#ff0014'] },-->
<!--      textStyle: { color: '#FFFFFF' },-->
<!--      bottom: 20,-->
<!--      right: 20-->
<!--    },-->
<!--    geo: [-->
<!--      {-->
<!--        map: 'puyang',-->
<!--        roam: true,-->
<!--        zoom: 1.2,-->
<!--        zlevel: 5,-->
<!--        label: {show: true, color: '#FFFFFF'},-->
<!--        itemStyle: {-->
<!--          color: '#01294e', // 背景-->
<!--          borderWidth: '1', // 边框宽度-->
<!--          borderColor: '#38abf1', // 边框颜色-->
<!--        }-->
<!--      },-->
<!--      {-->
<!--        map: 'puyang',-->
<!--        roam: true,-->
<!--        zoom: 1.2,-->
<!--        top: '11%',-->
<!--        zlevel: 4,-->
<!--        itemStyle: {-->
<!--          color: '#38abf1', // 背景-->
<!--          borderWidth: '1', // 边框宽度-->
<!--          borderColor: '#38abf1', // 边框颜色-->
<!--        }-->
<!--      },-->
<!--      {-->
<!--        map: 'puyang',-->
<!--        roam: true,-->
<!--        zoom: 1.2,-->
<!--        top: '13%',-->
<!--        zlevel: 3,-->
<!--        itemStyle: {-->
<!--          color: '#2f738e', // 背景-->
<!--          borderWidth: '1', // 边框宽度-->
<!--          borderColor: '#2f738e', // 边框颜色-->
<!--        }-->
<!--      },-->
<!--    ],-->
<!--    series: [{-->
<!--      zlevel: 6,-->
<!--      name: '城市热力',-->
<!--      type: 'heatmap',-->
<!--      coordinateSystem: 'geo',-->
<!--      data: obj.data,-->
<!--      pointSize: 15,-->
<!--      blurSize: 20,-->
<!--      minOpacity: 0.3,-->
<!--      maxOpacity: 0.8-->
<!--    }]-->
<!--  };-->
<!--}-->

<!--// 箱线图配置-->
<!--const xxtOption = (obj) => {-->
<!--  return {-->
<!--    grid: {-->
<!--      left: '2%',  // 左边距-->
<!--      right: '3%', // 右边距-->
<!--      top: '12%',-->
<!--      bottom: '10%', // 下边距-->
<!--      containLabel: true-->
<!--    },-->
<!--    xAxis: {-->
<!--      type: 'category',-->
<!--      splitLine: {show: false},-->
<!--      data: obj.xData,-->
<!--      axisLabel:{-->
<!--        color: '#FFFFFF'-->
<!--      },-->
<!--      axisTick: { show: false }-->
<!--    },-->
<!--    yAxis: {-->
<!--      type: 'value',-->
<!--      splitLine: {-->
<!--        lineStyle: {-->
<!--          color: '#888888',-->
<!--          type: 'dashed'-->
<!--        }-->
<!--      },-->
<!--      max: obj.yMax * 1.2-->
<!--    },-->
<!--    series: [-->
<!--      {-->
<!--        type: 'boxplot',-->
<!--        data: obj.sData,-->
<!--        boxWidth: 21,-->
<!--      },-->
<!--      {-->
<!--        type: 'line',-->
<!--        data: obj.zxData1,-->
<!--        itemStyle: { color: '#2acaf8' }-->
<!--      },-->
<!--      {-->
<!--        type: 'line',-->
<!--        markLine: {-->
<!--          symbol:"none",-->
<!--          animation: false,-->
<!--          data: [ { yAxis: obj.lsj } ],-->
<!--          label: { show: true, formatter: '建议零售价', position:"insideEndTop", color: '#FFFFFF', fontSize: 10 },-->
<!--          lineStyle: { color: '#1b5657', width: 1, type: 'solid' }-->
<!--        }-->
<!--      },-->
<!--      {-->
<!--        type: 'line',-->
<!--        markLine: {-->
<!--          symbol:"none",-->
<!--          animation: false,-->
<!--          data: [ { yAxis: obj.pfj } ],-->
<!--          label: { show: true, formatter: '批发价', position:"insideEndTop", color: '#FFFFFF', fontSize: 10 },-->
<!--          lineStyle: { color: '#0d57c6', width: 1, type: 'solid' }-->
<!--        }-->
<!--      },-->
<!--    ],-->
<!--  };-->
<!--}-->

<!--// 柱状图配置-->
<!--const zztOption = (obj) => {-->
<!--  return {-->
<!--    grid: {-->
<!--      left: '1%',  // 左边距-->
<!--      right: '1.5%', // 右边距-->
<!--      top: '10%',-->
<!--      bottom: '4%', // 下边距-->
<!--      containLabel: true-->
<!--    },-->
<!--    dataset: {-->
<!--      dimensions: obj.dimensions,-->
<!--      source: obj.source-->
<!--    },-->
<!--    xAxis: {-->
<!--      type: 'category',-->
<!--      axisLine: {-->
<!--        lineStyle: {-->
<!--          color: '#888888'-->
<!--        }-->
<!--      },-->
<!--      axisLabel:{-->
<!--        color: '#FFFFFF'-->
<!--      },-->
<!--      axisTick: { show: false }-->
<!--    },-->
<!--    yAxis: {-->
<!--      splitLine: {-->
<!--        lineStyle: {-->
<!--          color: '#888888',-->
<!--          type: 'dashed'-->
<!--        }-->
<!--      }-->
<!--    },-->
<!--    series: obj.series,-->
<!--  };-->
<!--}-->

<!--// 箱线图颜色设置-->
<!--const setXxtColorAndYMax = (obj) => {-->
<!--  let temp = []-->
<!--  let max = []-->
<!--  obj.sData.forEach((item, index) => {-->
<!--    let itemStyle = {-->
<!--      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0baefd' }, { offset: 1, color: '#116cfd' }]),-->
<!--      borderWidth: 1,-->
<!--      borderColor: '#15ecfd',-->
<!--    }-->
<!--    let emphasis = {-->
<!--      itemStyle: {-->
<!--        borderWidth: 1,-->
<!--        borderColor: '#15ecfd',-->
<!--      }-->
<!--    }-->
<!--    if(index % 2 === 1){-->
<!--      itemStyle = {-->
<!--        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#51ffd0' }, { offset: 1, color: '#51c4ff' }]),-->
<!--        borderWidth: 1,-->
<!--        borderColor: '#0d97fd',-->
<!--      }-->
<!--      emphasis = {-->
<!--        itemStyle: {-->
<!--          borderWidth: 1,-->
<!--          borderColor: '#0d97fd',-->
<!--        }-->
<!--      }-->
<!--    }-->
<!--    temp.push({ value: item, itemStyle: itemStyle, emphasis: emphasis })-->
<!--    max.push(Math.max(...item))-->
<!--  })-->
<!--  obj.sData = temp-->
<!--  obj.yMax = Math.max(...max)-->
<!--}-->

<!--//-&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45; 数据请求 -&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;-->

<!--// 地图-->
<!--const dtRequest = (classify, pg) => {-->
<!--  let obj = {-->
<!--    data: [-->
<!--      { "name": "南乐县", "value": [ 115.2, 36.0, 97 ] },-->
<!--      { "name": "清丰县", "value": [ 115.1, 35.9, 70 ] },-->
<!--      { "name": "华龙区", "value": [ 115.03, 35.76, 50 ] },-->
<!--      { "name": "濮阳县", "value": [ 115.02, 35.71, 20 ] },-->
<!--      { "name": "范县", "value": [ 115.50, 35.85, 60 ] },-->
<!--      { "name": "台前县", "value": [ 115.85, 35.99, 33 ] },-->
<!--    ]-->
<!--  }-->
<!--  echarts.registerMap('puyang', puyang);-->
<!--  const echartDt = echarts.init(echartDtComp.value);-->
<!--  echartDt.setOption(dtOption(obj));-->
<!--}-->

<!--// 累计-->
<!--const lgRequest = (classify, pg, tjlx, tjlxz) => {-->
<!--  let obj = {-->
<!--    xData: ['5月第1次', '5月第2次', '5月第3次', '5月第4次', '5月第5次'],-->
<!--    sData: [-->
<!--      [40, 50, 90, 90, 100],-->
<!--      [50, 60, 90, 90, 100],-->
<!--      [40, 50, 90, 90, 100],-->
<!--      [50, 60, 90, 90, 100],-->
<!--      [30, 40, 80, 80, 100],-->
<!--    ],-->
<!--    zxData1: [60, 70, 80, 70, 60],-->
<!--    lsj: 110,-->
<!--    pfj: 25,-->
<!--  }-->
<!--  setXxtColorAndYMax(obj)-->
<!--  const echartLj1 = echarts.init(echartLj1Comp.value);-->
<!--  echartLj1.setOption(xxtOption(obj));-->

<!--  obj = {-->
<!--    xData: ['5月第1次', '5月第2次', '5月第3次', '5月第4次', '5月第5次'],-->
<!--    sData: [-->
<!--      [40, 50, 90, 90, 100],-->
<!--      [50, 60, 90, 90, 100],-->
<!--      [40, 50, 90, 90, 100],-->
<!--      [50, 60, 90, 90, 100],-->
<!--      [30, 40, 80, 80, 100],-->
<!--    ],-->
<!--    zxData1: [60, 70, 80, 70, 60],-->
<!--    lsj: 110,-->
<!--    pfj: 25,-->
<!--  }-->
<!--  setXxtColorAndYMax(obj)-->
<!--  const echartLj2 = echarts.init(echartLj2Comp.value);-->
<!--  echartLj2.setOption(xxtOption(obj));-->

<!--  obj = {-->
<!--    xData: ['5月第1次', '5月第2次', '5月第3次', '5月第4次', '5月第5次'],-->
<!--    sData: [-->
<!--      [40, 50, 90, 90, 100],-->
<!--      [50, 60, 90, 90, 100],-->
<!--      [40, 50, 90, 90, 100],-->
<!--      [50, 60, 90, 90, 100],-->
<!--      [30, 40, 80, 80, 100],-->
<!--    ],-->
<!--    zxData1: [60, 70, 80, 70, 60],-->
<!--    lsj: 110,-->
<!--    pfj: 25,-->
<!--  }-->
<!--  setXxtColorAndYMax(obj)-->
<!--  const echartLj3 = echarts.init(echartLj3Comp.value);-->
<!--  echartLj3.setOption(xxtOption(obj));-->

<!--  obj = {-->
<!--    xData: ['5月第1次', '5月第2次', '5月第3次', '5月第4次', '5月第5次'],-->
<!--    sData: [-->
<!--      [40, 50, 90, 90, 100],-->
<!--      [50, 60, 90, 90, 100],-->
<!--      [40, 50, 90, 90, 100],-->
<!--      [50, 60, 90, 90, 100],-->
<!--      [30, 40, 80, 80, 100],-->
<!--    ],-->
<!--    zxData1: [60, 70, 80, 70, 60],-->
<!--    lsj: 110,-->
<!--    pfj: 25,-->
<!--  }-->
<!--  setXxtColorAndYMax(obj)-->
<!--  const echartLj4 = echarts.init(echartLj4Comp.value);-->
<!--  echartLj4.setOption(xxtOption(obj));-->
<!--}-->

<!--// 该次-->
<!--const gcRequest = (classify, pg) => {-->
<!--  let obj = {-->
<!--    xData: ['1-5档', '6-10档', '11-15档', '16-20档', '21-25档', '26-30档'],-->
<!--    sData: [-->
<!--      [40, 50, 90, 90, 100],-->
<!--      [50, 60, 90, 90, 100],-->
<!--      [40, 50, 90, 90, 100],-->
<!--      [50, 60, 90, 90, 100],-->
<!--      [30, 40, 80, 80, 100],-->
<!--      [30, 40, 80, 80, 100],-->
<!--    ],-->
<!--    zxData1: [60, 70, 80, 70, 60, 60],-->
<!--    lsj: 110,-->
<!--    pfj: 25,-->
<!--  }-->
<!--  setXxtColorAndYMax(obj)-->
<!--  const echartGc1 = echarts.init(echartGc1Comp.value);-->
<!--  echartGc1.setOption(xxtOption(obj));-->

<!--  obj = {-->
<!--    xData: ['城镇', '乡村'],-->
<!--    sData: [-->
<!--      [40, 50, 90, 90, 100],-->
<!--      [50, 60, 90, 90, 100],-->
<!--    ],-->
<!--    zxData1: [60, 70],-->
<!--    lsj: 110,-->
<!--    pfj: 25,-->
<!--  }-->
<!--  setXxtColorAndYMax(obj)-->
<!--  const echartGc2 = echarts.init(echartGc2Comp.value);-->
<!--  echartGc2.setOption(xxtOption(obj));-->

<!--  obj = {-->
<!--    xData: ['便利店', '烟草专卖店', '超市', '商场', '娱乐服务类', '其他'],-->
<!--    sData: [-->
<!--      [40, 50, 90, 90, 100],-->
<!--      [40, 50, 90, 90, 100],-->
<!--      [40, 50, 90, 90, 100],-->
<!--      [50, 60, 90, 90, 100],-->
<!--      [30, 40, 80, 80, 100],-->
<!--      [30, 40, 80, 80, 100],-->
<!--    ],-->
<!--    zxData1: [60, 70, 80, 70, 60, 60],-->
<!--    lsj: 110,-->
<!--    pfj: 25,-->
<!--  }-->
<!--  setXxtColorAndYMax(obj)-->
<!--  const echartGc3 = echarts.init(echartGc3Comp.value);-->
<!--  echartGc3.setOption(xxtOption(obj));-->
<!--}-->

<!--// 柱状图-->
<!--const zztRequest = (classify, tjlx) => {-->
<!--  let obj = {}-->
<!--  if(tjlx === '1'){-->
<!--    obj = {-->
<!--      dimensions: ['product', '城镇', '乡村'],-->
<!--      source: [-->
<!--        { product: '南京（细支九五）',      '城镇': 43.3, '乡村': 85.8 },-->
<!--        { product: '白沙（硬细支和天下）',  '城镇': 83.1, '乡村': 73.4 },-->
<!--        { product: '中华（软）',           '城镇': 86.4, '乡村': 65.2 },-->
<!--        { product: '中华（硬）',           '城镇': 72.4, '乡村': 53.9 }-->
<!--      ],-->
<!--      series: [-->
<!--        { type: 'bar', barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#E4B90C' }]), borderWidth: 1, borderColor: '#FFD735' } },-->
<!--        { type: 'bar', barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#00A8FF' }]), borderWidth: 1, borderColor: '#3681FF' } },-->
<!--      ]-->
<!--    }-->
<!--  }-->
<!--  if(tjlx === '2'){-->
<!--    obj = {-->
<!--      dimensions: ['product', '便利店', '烟草专卖店', '超市', '商场', '娱乐服务类', '其他'],-->
<!--      source: [-->
<!--        { product: '南京（细支九五）',      '便利店': 43.3, '烟草专卖店': 85.8, '超市': 43.3, '商场': 43.3, '娱乐服务类': 43.3, '其他': 43.3 },-->
<!--        { product: '白沙（硬细支和天下）',  '便利店': 83.1, '烟草专卖店': 73.4, '超市': 83.1, '商场': 83.1, '娱乐服务类': 83.1, '其他': 83.1 },-->
<!--        { product: '中华（软）',           '便利店': 86.4, '烟草专卖店': 65.2, '超市': 86.4, '商场': 86.4, '娱乐服务类': 86.4, '其他': 86.4 },-->
<!--        { product: '中华（硬）',           '便利店': 72.4, '烟草专卖店': 53.9, '超市': 72.4, '商场': 72.4, '娱乐服务类': 72.4, '其他': 72.4 }-->
<!--      ],-->
<!--      series: [-->
<!--        { type: 'bar', barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#E4B90C' }]), borderWidth: 1, borderColor: '#FFD735' } },-->
<!--        { type: 'bar', barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#00A8FF' }]), borderWidth: 1, borderColor: '#3681FF' } },-->
<!--        { type: 'bar', barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#6FFFFF' }]), borderWidth: 1, borderColor: '#13FAFE' } },-->
<!--        { type: 'bar', barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#E73341' }]), borderWidth: 1, borderColor: '#FF616E' } },-->
<!--        { type: 'bar', barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#5FDA33' }]), borderWidth: 1, borderColor: '#6DFF38' } },-->
<!--        { type: 'bar', barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#D76AFF' }]), borderWidth: 1, borderColor: '#DF8AFF' } },-->
<!--      ]-->
<!--    }-->
<!--  }-->
<!--  if(tjlx === '3'){-->
<!--    obj = {-->
<!--      dimensions: ['product', '1-5', '6-10', '11-15', '16-20', '21-25', '26-30'],-->
<!--      source: [-->
<!--        { product: '南京（细支九五）',      '1-5': 43.3, '6-10': 85.8, '11-15': 43.3, '16-20': 43.3, '21-25': 43.3, '26-30': 43.3 },-->
<!--        { product: '白沙（硬细支和天下）',  '1-5': 83.1, '6-10': 73.4, '11-15': 83.1, '16-20': 83.1, '21-25': 83.1, '26-30': 83.1 },-->
<!--        { product: '中华（软）',           '1-5': 86.4, '6-10': 65.2, '11-15': 86.4, '16-20': 86.4, '21-25': 86.4, '26-30': 86.4 },-->
<!--        { product: '中华（硬）',           '1-5': 72.4, '6-10': 53.9, '11-15': 72.4, '16-20': 72.4, '21-25': 72.4, '26-30': 72.4 }-->
<!--      ],-->
<!--      series: [-->
<!--        { type: 'bar', barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#E4B90C' }]), borderWidth: 1, borderColor: '#FFD735' } },-->
<!--        { type: 'bar', barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#00A8FF' }]), borderWidth: 1, borderColor: '#3681FF' } },-->
<!--        { type: 'bar', barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#6FFFFF' }]), borderWidth: 1, borderColor: '#13FAFE' } },-->
<!--        { type: 'bar', barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#E73341' }]), borderWidth: 1, borderColor: '#FF616E' } },-->
<!--        { type: 'bar', barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#5FDA33' }]), borderWidth: 1, borderColor: '#6DFF38' } },-->
<!--        { type: 'bar', barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#D76AFF' }]), borderWidth: 1, borderColor: '#DF8AFF' } },-->
<!--      ]-->
<!--    }-->
<!--  }-->

<!--  const echartZzt = echarts.init(echartZztComp.value);-->
<!--  echartZzt.setOption(zztOption(obj));-->
<!--}-->

<!--// 该次的-->
<!--const gaicRequest = () => {-->
<!--  const params = {-->
<!--    spId: 1,-->
<!--    xxtDwLoad: '1',-->
<!--    xxtCzxcLoad: '1',-->
<!--    xxtYtLoad: '1',-->
<!--    zztLoad: '1',-->
<!--    zztType: 'dw',-->
<!--    syl: '1'-->
<!--  }-->
<!--  gaicApi(params).then(res => {-->
<!--    console.log(res)-->
<!--    if(params.xxtDwLoad && params.xxtDwLoad === '1'){-->
<!--      const obj = {-->
<!--        xData: ['1-5档', '6-10档', '11-15档', '16-20档', '21-25档', '26-30档'],-->
<!--        sData: [-->
<!--          [40, 50, 90, 90, 100],-->
<!--          [50, 60, 90, 90, 100],-->
<!--          [40, 50, 90, 90, 100],-->
<!--          [50, 60, 90, 90, 100],-->
<!--          [30, 40, 80, 80, 100],-->
<!--          [30, 40, 80, 80, 100],-->
<!--        ],-->
<!--        zxData1: [60, 70, 80, 70, 60, 60],-->
<!--        lsj: 110,-->
<!--        pfj: 25,-->
<!--      }-->
<!--    }-->

<!--    // 箱线图：档位统计（某品规）-->

<!--    // 箱线图：城镇/乡村统计（某品规）-->

<!--    // 箱线图：业态统计（某品规）-->

<!--    // 柱状图：档位、城镇/乡村、业态（所有品规）-->

<!--    // 四员联动-->

<!--  })-->
<!--}-->

<!--//-&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45; 事件管理 -&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;-->

<!--const handleKeyDown = (event) => {-->
<!--  if (event.keyCode === 116) {-->
<!--    event.preventDefault();-->
<!--  }-->
<!--}-->

<!--//-&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45; vue相关 -&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;-->

<!--onMounted(() => {-->
<!--  // 地图-->
<!--  dtRequest()-->

<!--  // 累计-->
<!--  lgRequest()-->

<!--  // 该次-->
<!--  // gcRequest()-->
<!--  gaicRequest()-->

<!--  // 柱状图-->
<!--  zztRequest(classify.value, '1')-->

<!--  // 四员联动-->
<!--  syldListData.value = [-->
<!--    { tjr: '李小虎', tjsj: '2025-06-09 09:07:11', content: '四员联动详情' },-->
<!--    { tjr: '李小虎', tjsj: '2025-06-09 09:07:11', content: '四员联动详情' },-->
<!--    { tjr: '李小虎', tjsj: '2025-06-09 09:07:11', content: '四员联动详情' },-->
<!--  ]-->

<!--  // F5事件监听-->
<!--  //window.addEventListener('keydown', handleKeyDown);-->
<!--})-->

<!--onUnmounted(() => {-->
<!--  //window.removeEventListener('keydown', handleKeyDown);-->
<!--})-->
<!--</script>-->

<!--<style lang='scss' scoped>-->
<!--* {-->
<!--  box-sizing: border-box;-->
<!--}-->

<!--.sjcj-wrapper{-->
<!--  width: 100%;-->
<!--  height: 100%;-->
<!--  background-image: url("../../../assets/images/sjcj_bg.png");-->
<!--  background-size: cover;-->
<!--}-->

<!--.select-bg{-->
<!--  max-width: 150px;-->
<!--  background-image: url("../../../assets/images/sjcj_select.png");-->
<!--  background-size: cover;-->
<!--}-->
<!--</style>-->
<template>
  <div :class="[classify === 'price' ? 'sjcj-wrapper-jg' : 'sjcj-wrapper-kc']" class="sjcj-wrapper-kc flex wh-full" style="box-sizing: content-box; position: relative;">
    <div style="position: absolute; top: 60px; left: 15px;">
      <img @click="back" src="@/assets/images/sjcj_dv_back.png" alt="back" class="mr-10px cursor-pointer" />
    </div>
    <div class="w-445px h-full " style="padding: 160px 10px 0 30px;">

      <div class="h-220px w-full ">
        <condition ref="condLjComp1" title="品规整体统计" :smoke="false" />
        <div class="h-188px br-3" style="padding: 0px 5px 0px 3px; box-sizing: inherit;">
          <div ref="echartLj1Comp" class="wh-full"></div>
        </div>
      </div>

      <div class="h-220px w-full ">
        <condition ref="condLjComp2" title="品规档位统计" :smoke="false" dw :dwChange="dwChange" />
        <div class="h-188px br-3" style="padding: 0px 5px 0px 3px; box-sizing: inherit;">
          <div ref="echartLj2Comp" class="wh-full"></div>
        </div>
      </div>

      <div class="h-220px w-full ">
        <condition ref="condLjComp3" title="品规城镇/乡村统计" :smoke="false" czxc :czxcChange="czxcChange" />
        <div class="h-188px br-3" style="padding: 0px 5px 0px 3px; box-sizing: inherit;">
          <div ref="echartLj3Comp" class="wh-full"></div>
        </div>
      </div>

      <div class="h-220px w-full ">
        <condition ref="condLjComp4" title="品规业态统计" :smoke="false" yt :ytChange="ytChange" />
        <div class="h-188px br-3" style="padding: 0px 5px 0px 3px; box-sizing: inherit;">
          <div ref="echartLj4Comp" class="wh-full"></div>
        </div>
      </div>

    </div>

    <div class="flex-grow h-full flex flex-col">

      <div class="w-full h-825px flex flex-row">

        <div class="w-1005px h-full flex flex-col">
          <div class="h-160px flex items-end justify-center" style="position: relative;">
            <div class="ml-37px">
              <img v-if="classify === 'price'" src="@/assets/images/sjcj_kc_normal.png" class="cursor-pointer" alt="jt" @click="classifyChange('inventory')"/>
              <img v-if="classify === 'inventory'" src="@/assets/images/sjcj_kc_press.png" class="cursor-pointer" alt="jt" @click="classifyChange('inventory')"/>
              <img v-if="classify === 'price'" src="@/assets/images/sjcj_jg_press.png" class="cursor-pointer ml-10px" alt="jt" @click="classifyChange('price')"/>
              <img v-if="classify === 'inventory'" src="@/assets/images/sjcj_jg_normal.png" class="cursor-pointer ml-10px" alt="jt" @click="classifyChange('price')"/>
            </div>
            <condition ref="condGcDtComp" style="position: absolute; right: 10px" :smokeChange="smokeChangeByGcDt" />
          </div>
          <div class="flex-grow" style="padding: 0 10px 5px 15px">
            <div ref="echartDtComp" class="wh-full"></div>
          </div>
        </div>

        <div class="flex-grow " style="padding: 145px 32px 0 9px;">

          <div class="h-216px w-full ">
            <condition ref="condGcXxtComp1"  title="品规档位统计" :smoke="false" />
            <div class="h-184px w-full br-3" style="padding: 0px 10px 0px 5px; box-sizing: inherit;">
              <div ref="echartGc1Comp" class="wh-full"></div>
            </div>
          </div>

          <div class="h-216px w-full ">
            <condition ref="condGcXxtComp2"  title="品规城镇/乡村统计" :smoke="false" />
            <div class="h-174px w-full br-3 mt-10px" style="padding: 0px 10px 0px 5px; box-sizing: inherit;">
              <div ref="echartGc2Comp" class="wh-full"></div>
            </div>
          </div>

          <div class="h-216px w-full ">
            <condition ref="condGcXxtComp3"  title="品规业态统计" :smoke="false" />
            <div class="h-169px w-full br-3 mt-10px" style="padding: 0px 10px 5px 5px; box-sizing: inherit;">
              <div ref="echartGc3Comp" class="wh-full"></div>
            </div>
          </div>

        </div>

      </div>

      <div class="w-full flex-grow flex flex-row ">

        <div class="w-1170px" style="padding: 40px 9px 0 25px;">
          <div class="wh-full ">
            <condition ref="condGcZztComp" title="品规多维度统计" :smoke="false" :dim="true" tl :zztDimChange="zztDimChange" />
            <div class="dv-bar" style="overflow-x: auto; box-sizing: inherit;">
              <div class="h-134px br-3 mt-10px" :style="{ width: zztWidth + 'px' }" >
                <div ref="echartZztComp" class="wh-full"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="flex-grow cursor-pointer" style="padding: 40px 40px 0 27px;" @click="handleSyldClick">
          <div class="flex w-full h-30px mt-10px bg-[#709fff]">
            <div class="text-center fs-15 flex items-center justify-center" style="flex: 3;">提交时间</div>
            <div class="text-center fs-15 flex items-center justify-center" style="flex: 2;">提交人</div>
            <div class="text-center fs-15 flex items-center justify-center" style="flex: 2;">详情</div>
          </div>
          <div class="w-full h-132px dv-bar" style="overflow-y: auto;">
            <div class="flex h-44px text-[#FFFFFF]" v-for="(item) in syldListData">
              <div class="text-center fs-13 flex items-center justify-center" style="flex: 3; border-right: 1px solid #51acbc; border-bottom: 1px solid #51acbc; border-left: 1px solid #51acbc; ">{{ item.tjsj }}</div>
              <div class="text-center fs-13 flex items-center justify-center" style="flex: 2; border-right: 1px solid #51acbc; border-bottom: 1px solid #51acbc;">{{ item.tjr }}</div>
              <div class="text-center fs-13 flex items-center justify-center" style="flex: 2; border-right: 1px solid #51acbc; border-bottom: 1px solid #51acbc;">{{ item.content }}</div>
            </div>
          </div>
        </div>

      </div>

    </div>

    <!-- 该次：柱状图详情（放大） -->
    <gcZzt ref="gcZztComp"/>

    <!-- 该次：箱线图档位详情（放大） -->
    <gcXxt1 ref="gcXxtDialog1"/>

    <!-- 该次：箱线图城镇乡村详情（放大） -->
    <gcXxt2 ref="gcXxtDialog2"/>

    <!-- 该次：箱线图业态详情（放大） -->
    <gcXxt3 ref="gcXxtDialog3"/>

    <!-- 该次：四员联动 -->
    <gcSyld ref="gcSyldDialog"/>

    <!-- 累计：箱线图整体（放大） -->
    <lxXxt1 ref="ljXxtDialog1"/>

    <!-- 累计：箱线图档位（放大） -->
    <lxXxt2 ref="ljXxtDialog2"/>

    <!-- 累计：箱线图城镇乡村（放大） -->
    <lxXxt3 ref="ljXxtDialog3"/>

    <!-- 累计：箱线图业态（放大） -->
    <lxXxt4 ref="ljXxtDialog4"/>
  </div>
</template>

<script setup>
import * as echarts from 'echarts';
import puyang from './puyang.json';
import china from './china.json';
import condition from './components/condition.vue'
import {exitFullscreen} from '@/utils/hz.js'
import {gaicApi, gaicZztApi, gaicSmokePrice, leijApi} from '@/api/gather/dataView.js'
import {
  setXxtColorAndYMax,
  getZztColor,
  zztOption,
  xxtOptionByGcJg,
  xxtOptionByGcKc,
  xxtOptionByLjJg,
  xxtOptionByLjKc,
  dtOption
} from '@/utils/dv.js'
import gcZzt from './components/gcZzt.vue'
import gcXxt1 from './components/gcXxt1.vue'
import gcXxt2 from './components/gcXxt2.vue'
import gcXxt3 from './components/gcXxt3.vue'
import gcSyld from './components/gcSyld.vue'
import lxXxt1 from './components/lxXxt1.vue'
import lxXxt2 from './components/lxXxt2.vue'
import lxXxt3 from './components/lxXxt3.vue'
import lxXxt4 from './components/lxXxt4.vue'

// 参考
// https://datav.aliyun.com/portal/school/atlas/area_selector

const {proxy} = getCurrentInstance()

//
const classify = ref('price')
const stId = ref()
const zztDim = ref('dw')
const dwValue = ref('1-5档')
const czxcValue = ref('城镇')
const ytValue = ref('便利店')
const spList = ref([])

//
const syldListData = ref([])

// 图表组件
const echartDtComp = ref()
const echartLj1Comp = ref()
const echartLj2Comp = ref()
const echartLj3Comp = ref()
const echartLj4Comp = ref()
const echartGc1Comp = ref()
const echartGc2Comp = ref()
const echartGc3Comp = ref()
const echartZztComp = ref()

// echart对象
let echartLj1 = null;
let echartLj2 = null;
let echartLj3 = null;
let echartLj4 = null;
let echartDt = null;
let echartGc1 = null;
let echartGc2 = null;
let echartGc3 = null;
let echartZzt = null;

// 条件组件
const condGcDtComp = ref()
const condGcZztComp = ref()

// 品规id
const spId = ref()

// dialog组件
const gcZztComp = ref()
const gcXxtDialog1 = ref()
const gcXxtDialog2 = ref()
const gcXxtDialog3 = ref()
const gcSyldDialog = ref()
const ljXxtDialog1 = ref()
const ljXxtDialog2 = ref()
const ljXxtDialog3 = ref()
const ljXxtDialog4 = ref()

// 柱状图宽度
const zztWidth = ref(1136);

//--------------------------------------------------------------- 数据请求 ---------------------------------------------------------------------

// 卷烟列表
const getGaicSmokePrice = () => {
  gaicSmokePrice({classify: classify.value}).then(res => {
    if (res.data.stId && res.data.spDataList && res.data.spDataList.length > 0) {
      spList.value = res.data.spDataList
      condGcDtComp.value.setSpDataList(res.data.spDataList)
      // 品规id
      spId.value = res.data.spDataList[0].id
      // 任务id
      stId.value = res.data.stId

      // 该次
      let params = { classify: classify.value, stId: stId.value, spId: spId.value, syld: '1' }
      gaicRequest(params)
      // 该次柱状图
      params = { classify: classify.value, stId: stId.value, zztDim: zztDim.value }
      gaicZztRequest(params)
      // 累计
      params = { isAll: "2", classify: classify.value, stId: stId.value, spId: spId.value, xxtZtLoad: '1', xxtDwLoad: '1', xxtCzxcLoad: '1', xxtYtLoad: '1', dwValue: dwValue.value, czxcValue: czxcValue.value, ytValue: czxcValue.value }
      leijRequest(params)
    }
  })
}

// 该次的
const gaicRequest = (params) => {
  gaicApi(params).then(res => {
    // 热力图：品规
    let obj = {data: res.data.gjDt}
    echarts.registerMap('puyang', puyang);
    echartDt.setOption(dtOption(obj));

    // 箱线图：档位统计（某品规）
    obj = {
      xData: res.data.gjXxtDwX,
      sData: res.data.gjXxtDwData,
      zxData1: res.data.gjXxtDwJzzx,
      lsj: res.data.lsj,
      pfj: res.data.pfj,
      thj: res.data.thj,
    }
    setXxtColorAndYMax(obj)
    if (!echartGc1) {
      echartGc1 = echarts.init(echartGc1Comp.value);
    } else {
      echartGc1.dispose()
      echartGc1 = echarts.init(echartGc1Comp.value);
    }
    const sss = classify.value === 'price' ? xxtOptionByGcJg(obj) : xxtOptionByGcKc(obj)
    console.log(sss)
    console.log(obj)
    echartGc1.setOption(sss);
    // 事件
    echartGc1.getZr().on('click', params => {
      gcXxtDialog1.value.show(classify.value, stId.value, spId.value)
    })

    // 箱线图：城镇/乡村统计（某品规）
    obj = {
      xData: res.data.gjXxtCzxcX,
      sData: res.data.gjXxtCzxcData,
      zxData1: res.data.gjXxtCzxcJzzx,
      lsj: res.data.lsj,
      pfj: res.data.pfj,
      thj: res.data.thj,
    }
    setXxtColorAndYMax(obj)
    if (!echartGc2) {
      echartGc2 = echarts.init(echartGc2Comp.value);
    } else {
      echartGc2.dispose()
      echartGc2 = echarts.init(echartGc2Comp.value);
    }
    echartGc2.setOption(classify.value === 'price' ? xxtOptionByGcJg(obj) : xxtOptionByGcKc(obj));
    // 事件
    echartGc2.getZr().on('click', params => {
      gcXxtDialog2.value.show(classify.value, stId.value, spId.value)
    })

    // 箱线图：业态统计（某品规）
    obj = {
      xData: res.data.gjXxtYtX,
      sData: res.data.gjXxtYtData,
      zxData1: res.data.gjXxtYtJzzx,
      lsj: res.data.lsj,
      pfj: res.data.pfj,
      thj: res.data.thj,
    }
    setXxtColorAndYMax(obj)
    if (!echartGc3) {
      echartGc3 = echarts.init(echartGc3Comp.value);
    } else {
      echartGc3.dispose()
      echartGc3 = echarts.init(echartGc3Comp.value);
    }
    echartGc3.setOption(classify.value === 'price' ? xxtOptionByGcJg(obj) : xxtOptionByGcKc(obj));
    // 事件
    echartGc3.getZr().on('click', params => {
      gcXxtDialog3.value.show(classify.value, stId.value, spId.value)
    })

    // 四员联动
    if (params.syld && params.syld === '1') {
      syldListData.value = res.data.links
    }
  })
}

// 该次的(柱状图)
const gaicZztRequest = (params) => {
  gaicZztApi(params).then(res => {
    const obj = {
      dimensions: res.data.gjZztDimensions,
      source: res.data.gjZztSource,
      series: getZztColor(params.zztDim)
    }
    zztWidth.value = res.data.gjZztSource.length * 150
    nextTick(() => {
      condGcZztComp.value.setTlList(obj)
      if (echartZzt) {
        echartZzt.dispose()
        echartZzt = echarts.init(echartZztComp.value);
      } else {
        echartZzt = echarts.init(echartZztComp.value);
      }
      echartZzt.setOption(zztOption(obj));
      // 事件
      echartZzt.getZr().on('click', params => {
        gcZztComp.value.show(classify.value, stId.value)
      })
    })
  })
}

// 累计
const leijRequest = (params) => {
  leijApi(params).then(res => {
    // 箱线图：整体
    if(params.xxtZtLoad && params.xxtZtLoad === '1'){
      const obj = {
        xData1: res.data.xData1,
        xData2: res.data.xData2,
        sData: res.data.sData1,
        zxData1: res.data.zxData1_1,
        zxData2: res.data.zxData2_1,
        lsj: res.data.lsj,
        pfj: res.data.pfj,
      }
      setXxtColorAndYMax(obj)
      if (!echartLj1) {
        echartLj1 = echarts.init(echartLj1Comp.value);
      } else {
        echartLj1.dispose()
        echartLj1 = echarts.init(echartLj1Comp.value);
      }
      echartLj1.setOption(classify.value === 'price' ? xxtOptionByLjJg('1', obj) : xxtOptionByLjKc('1', obj));
      // 事件
      echartLj1.getZr().on('click', params => {
        ljXxtDialog1.value.show(classify.value, spList.value, spId.value)
      })
    }

    // 箱线图：档位
    if(params.xxtDwLoad && params.xxtDwLoad === '1'){
      const obj = {
        xData1: res.data.xData1,
        xData2: res.data.xData2,
        sData: res.data.sData2,
        zxData1: res.data.zxData1_2,
        zxData2: res.data.zxData2_2,
        lsj: res.data.lsj,
        pfj: res.data.pfj,
      }
      setXxtColorAndYMax(obj)
      if (!echartLj2) {
        echartLj2 = echarts.init(echartLj2Comp.value);
      } else {
        echartLj2.dispose()
        echartLj2 = echarts.init(echartLj2Comp.value);
      }
      echartLj2.setOption(classify.value === 'price' ? xxtOptionByLjJg('1', obj) : xxtOptionByLjKc('1', obj));
      // 事件
      echartLj2.getZr().on('click', params => {
        ljXxtDialog2.value.show(classify.value, spList.value, spId.value, dwValue.value)
      })
    }

    // 箱线图：城镇乡村
    if(params.xxtCzxcLoad && params.xxtCzxcLoad === '1'){
      const obj = {
        xData1: res.data.xData1,
        xData2: res.data.xData2,
        sData: res.data.sData3,
        zxData1: res.data.zxData1_3,
        zxData2: res.data.zxData2_3,
        lsj: res.data.lsj,
        pfj: res.data.pfj,
      }
      setXxtColorAndYMax(obj)
      if (!echartLj3) {
        echartLj3 = echarts.init(echartLj3Comp.value);
      } else {
        echartLj3.dispose()
        echartLj3 = echarts.init(echartLj3Comp.value);
      }
      echartLj3.setOption(classify.value === 'price' ? xxtOptionByLjJg('1', obj) : xxtOptionByLjKc('1', obj));
      // 事件
      echartLj3.getZr().on('click', params => {
        ljXxtDialog3.value.show(classify.value, spList.value, spId.value, czxcValue.value)
      })
    }

    // 箱线图：业态
    if(params.xxtYtLoad && params.xxtYtLoad === '1'){
      const obj = {
        xData1: res.data.xData1,
        xData2: res.data.xData2,
        sData: res.data.sData4,
        zxData1: res.data.zxData1_4,
        zxData2: res.data.zxData2_4,
        lsj: res.data.lsj,
        pfj: res.data.pfj,
      }
      setXxtColorAndYMax(obj)
      if (!echartLj4) {
        echartLj4 = echarts.init(echartLj4Comp.value);
      } else {
        echartLj4.dispose()
        echartLj4 = echarts.init(echartLj4Comp.value);
      }
      echartLj4.setOption(classify.value === 'price' ? xxtOptionByLjJg('1', obj) : xxtOptionByLjKc('1', obj));
      // 事件
      echartLj4.getZr().on('click', params => {
        ljXxtDialog4.value.show(classify.value, spList.value, spId.value, ytValue.value)
      })
    }
  })
}

//--------------------------------------------------------------- 事件管理 ---------------------------------------------------------------------

// 库存价格切换
const classifyChange = (cs) => {
  if (cs === classify.value) {
    return
  }
  classify.value = cs

  // 该次
  let params = { classify: classify.value, stId: stId.value, spId: spId.value, syld: '2' }
  gaicRequest(params)
  // 该次柱状图
  params = { classify: classify.value, stId: stId.value, zztDim: zztDim.value }
  gaicZztRequest(params)
  // 累计
  params = { isAll: "2", classify: classify.value, stId: stId.value, spId: spId.value, xxtZtLoad: '1', xxtDwLoad: '1', xxtCzxcLoad: '1', xxtYtLoad: '1', dwValue: dwValue.value, czxcValue: czxcValue.value, ytValue: ytValue.value }
  leijRequest(params)
}

// 品规切换
const smokeChangeByGcDt = (_spId) => {
  spId.value = _spId
  // 该次
  let params = {classify: classify.value, stId: stId.value, spId: spId.value, syld: '2'}
  gaicRequest(params)
  // 累计
  params = { isAll: "2", classify: classify.value, stId: stId.value, spId: spId.value, xxtZtLoad: '1', xxtDwLoad: '1', xxtCzxcLoad: '1', xxtYtLoad: '1', dwValue: dwValue.value, czxcValue: czxcValue.value, ytValue: ytValue.value }
  leijRequest(params)
}

// 柱状图：维度切换
const zztDimChange = (_zztDim) => {
  zztDim.value = _zztDim

  const params = {classify: classify.value, stId: stId.value, zztDim: zztDim.value}
  gaicZztRequest(params)
}

// 箱线图（累计）：档位切换
const dwChange = (dw) => {
  dwValue.value = dw

  const params = {isAll: "2", classify: classify.value, spId: spId.value, xxtDwLoad: '1', dwValue: dwValue.value}
  leijRequest(params)
}
// 箱线图（累计）：城镇/乡村切换
const czxcChange = (czxc) => {
  czxcValue.value = czxc

  const params = {isAll: "2", classify: classify.value, spId: spId.value, xxtCzxcLoad: '1', czxcValue: czxcValue.value}
  leijRequest(params)
}
// 箱线图（累计）：业态切换
const ytChange = (yt) => {
  ytValue.value = yt

  const params = {isAll: "2", classify: classify.value, spId: spId.value, xxtYtLoad: '1', ytValue: ytValue.value}
  leijRequest(params)
}

// 四员联动
const handleSyldClick = () => {
  gcSyldDialog.value.show(syldListData.value)
}

// 返回
const back = () => {
  if (document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement) {
    exitFullscreen();
  }
  const obj = {path: "/gather/surveyTask"}
  proxy.$tab.closeOpenPage(obj)
}

//--------------------------------------------------------------- vue相关 ---------------------------------------------------------------------

onMounted(() => {
  // echart对象
  echartDt = echarts.init(echartDtComp.value);
  // 卷烟价格（该次）
  getGaicSmokePrice()
})

</script>

<style lang='scss' scoped>
* {
  box-sizing: border-box;
}

.sjcj-wrapper-kc {
  width: 100%;
  height: 100%;
  background-image: url("../../../assets/images/sjcj_bg_kc.png");
  background-size: cover;
}

.sjcj-wrapper-jg {
  width: 100%;
  height: 100%;
  background-image: url("../../../assets/images/sjcj_bg_jg.png");
  background-size: cover;
}

.select-bg {
  max-width: 150px;
  background-image: url("../../../assets/images/sjcj_select.png");
  background-size: cover;
}

/** 滚动条 */
.dv-bar::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

.dv-bar::-webkit-scrollbar-track {
  background: #061526;
  border-radius: 2px;
}

.dv-bar::-webkit-scrollbar-thumb {
  background-color: #3094d5;
  transition: background-color .3s;
}

.dv-bar::-moz-scrollbar {
  width: 5px;
  height: 5px;
}

.dv-bar::-moz-scrollbar-track {
  background: #FFFFFF;
  border-radius: 2px;
}

.dv-bar::-moz-scrollbar-thumb {
  background-color: #3094d5;
  transition: background-color .3s;
}

.dv-bar::-o-scrollbar {
  width: 5px;
  height: 5px;
}

.dv-bar::-o-scrollbar-track {
  background: #FFFFFF;
  border-radius: 2px;
}

.dv-bar::-o-scrollbar-thumb {
  background-color: #3094d5;
}
</style>
<template>
    <div class="app-container">
        <div class="from_header">
            <div class="from_header_title">消息列表</div>
            <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" style="margin-top: 20px;">
                <el-form-item label="许可证号">
                    <el-input v-model="queryParams.licence" placeholder="请输入许可证号" clearable style="width: 200px"
                        @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="商户名称">
                    <el-input v-model="queryParams.merchantName" placeholder="请输入商户名称" clearable style="width: 200px"
                        @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="状态">
                    <el-select v-model="queryParams.status" placeholder="状态" clearable style="width: 200px">
                        <el-option v-for="dict in message_status" :key="dict.value" :label="dict.label"
                            :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="日期">
                    <el-date-picker v-model="queryParams.createTimeStr" type="date" value-format="YYYY-MM-DD"
                        placeholder="请选择开始时间" />
                </el-form-item>
                <el-form-item>
                    <el-button class="btn-search" icon="Search" @click="handleQuery">查询</el-button>
                    <el-button class="btn-refresh" icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="from_container">
            <el-row :gutter="10" class="mb8">
                <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>
            <el-table v-loading="loading" :data="postList" :row-class-name="tableRowClassName">
                <el-table-column label="序号" align="center" width="100">
                    <template #default="scope">
                        <span>{{ scope.$index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="许可证号" align="center" prop="licence" width="180" />
                <el-table-column label="内容" align="center" prop="msgContent" />
                <el-table-column label="状态" align="center" prop="status" width="100">
                    <template #default="scope">
                        <div class="status_text" :style="{ color: getStatus(scope.row.status, 'color') }">{{
                            getStatus(scope.row.status, 'text') }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="发送人" align="center" prop="county" />
                <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
                <el-table-column label="操作" width="180" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <div class="table-btn">
                            <el-button class="table-btn-edit" link type="primary"
                                @click="handleUpdate(scope.row, 'check')"
                                v-hasPermi="['system:post:edit']">查看</el-button>
                            <div class="btn-line"
                                v-if="userStore.id !== scope.row.createId && scope.row.status !== '3'"></div>
                            <el-button v-if="userStore.id !== scope.row.createId && scope.row.status !== '3'"
                                class="table-btn-edit" link type="primary" @click="handleUpdate(scope.row, 'handle')"
                                v-hasPermi="['system:post:edit']">处理</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize" @pagination="getList" />
        </div>
        <!-- 添加或修改处室对话框 -->
        <el-dialog v-model="open" width="800px" append-to-body class="dialog_component">
            <div class="dialog_header">
                <div class="header_left">
                    <img src="@/assets/images/dialog-icon.png" alt="">
                    <div class="dialog_title">
                        消息提醒
                    </div>
                </div>
                <div class="dialog_close" @click="cancel">
                    <el-icon>
                        <CloseBold />
                    </el-icon>
                </div>
            </div>
            <div class="dialog_content">
                <el-form :inline="true" label-position="right" label-width="80px">
                    <el-form-item label="考核信息" style="width: 100%;">
                        {{ replyItem.paperName }}
                    </el-form-item>
                    <el-form-item label="发送人" style="width: 100%;">
                        {{ replyItem.county }}
                    </el-form-item>
                    <el-form-item label="申诉店铺" style="width: 100%;">
                        {{ replyItem.merchantName }}
                    </el-form-item>
                    <el-form-item label="许可证号" style="width: 50%;">
                        {{ replyItem.licence }}
                    </el-form-item>
                    <el-form-item label="状态">
                        <div class="status_text" :style="{ color: getStatus(replyItem.status, 'color') }">{{
                            getStatus(replyItem.status, 'text') }}
                        </div>
                    </el-form-item>
                    <el-form-item label="内容" style="width: 100%;">
                        {{ replyItem.msgContent }}
                    </el-form-item>
                    <el-form-item label="图片" style="width: 100%;">
                        <el-image :src="baseUrl + replyItem.msgImage" fit="contain" class="dialog_image">
                            <template #error>
                                <div>
                                    <el-icon>
                                        <Picture />
                                    </el-icon>
                                </div>
                            </template>
                        </el-image>
                    </el-form-item>
                    <el-form-item label="处理回复" style="width: 100%;" v-if="replyType === 'handle'">
                        <el-input v-model="replyMessage" :autosize="{ minRows: 4 }" type="textarea"
                            placeholder="请输入处理的回复信息" />
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button class="dialog_submit" @click="submitForm" v-if="replyType === 'handle'">确 定</el-button>
                    <el-button class="dialog_cancel" @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Post">
import { messageList, messageStatus, messageReply } from "@/api/system/user"
import { onMounted } from "vue"
import { ElMessage } from 'element-plus'
import useUserStore from "@/store/modules/user"

const userStore = useUserStore()
const baseUrl = import.meta.env.VITE_APP_BASE_API
const { proxy } = getCurrentInstance()
const { message_status } = proxy.useDict("message_status")
const postList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)
const replyMessage = ref('')
const replyItem = ref({})
const replyType = ref('')
const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        merchantName: undefined,
        createTimeStr: undefined,
        status: undefined
    }
})

const { queryParams } = toRefs(data)

onMounted(() => {
    getList()
})

const getStatus = (status, type) => {
    if (type === 'color') {
        const colorMap = {
            "1": "#FF9B00",
            "2": "#FF0000",
            "3": "#049B01"
        }
        return colorMap[status]
    } else {
        for (let i = 0; i < message_status.value.length; i++) {
            if (message_status.value[i].value === status) {
                return message_status.value[i].label
            }
        }
    }
}

const tableRowClassName = ({ row, rowIndex }) => {
    if ((rowIndex + 1) % 2 === 0) {
        return 'table-row'
    }
    return ''
}

/** 查询消息列表 */
function getList() {
    loading.value = true
    messageList(queryParams.value).then(response => {
        postList.value = response.rows
        total.value = response.total
        loading.value = false
    })
}

/** 取消按钮 */
function cancel() {
    open.value = false
    replyMessage.value = ''
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef")
    handleQuery()
}

/** 修改按钮操作 */
function handleUpdate(row, type) {
    replyType.value = type
    if (row.createId !== userStore.id && row.status == '1') {
        messageStatus(row.msgId).then(response => {
            if (response.code === 200) {
                open.value = true
                replyItem.value = row
                replyMessage.value = ''
                getList()
            }
        })
    } else {
        open.value = true
        replyItem.value = row
        replyMessage.value = ''
    }
}

/** 提交按钮 */
function submitForm() {
    if (!replyMessage.value) {
        ElMessage.warning("请输入处理回复信息")
        return
    }
    messageReply(replyItem.value.msgId, { msgReply: replyMessage.value }).then(res => {
        if (res.code === 200) {
            ElMessage.success("处理成功")
            open.value = false
            getList()
        }
    })
}
</script>

<style scoped lang="scss">
.dialog_image {
    width: 120px;
    height: 120px;
    background: #F5F5F5;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 25px;
}
</style>

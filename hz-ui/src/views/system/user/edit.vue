<template>
    <div class="app-container">
        <div class="store_header">
            <div class="store_header_title">
                <div>{{ editType == 'add' ? '新增用户' : editType == 'edit' ? '编辑用户' : '查看用户' }}</div>
            </div>
        </div>
        <div class="form_content" v-if="editType == 'edit' || editType == 'add'">
            <el-form ref="assetRef" inline :model="form" :rules="rules" class="basic_from" label-width="auto">
                <div class="form_content_header">
                    <div class="form_content_input">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="用户昵称" prop="nickName">
                                    <el-input style="width: 320px;" v-model="form.nickName" placeholder="请输入用户昵称" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="手机号码" prop="phonenumber">
                                    <el-input style="width: 320px;" v-model="form.phonenumber" placeholder="请输入手机号" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="归属地区" prop="deptId">
                                    <el-tree-select v-model="form.deptId" :data="deptOptions" style="width: 320px;"
                                        :props="{ value: 'id', label: 'label', children: 'children' }" value-key="id"
                                        placeholder="请选择归属地区" check-strictly />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="用户名称" prop="userName">
                                    <el-input :disabled="editType !== 'add'" style="width: 320px;"
                                        v-model="form.userName" placeholder="请输入用户名称" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="邮箱" prop="email">
                                    <el-input style="width: 320px;" v-model="form.email" placeholder="请输入邮箱" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="所属角色" prop="roleId">
                                    <el-select style="width: 320px" v-model="form.roleId" placeholder="请选择所属角色">
                                        <el-option v-for="item in roleOptions" :key="item.roleId" :label="item.roleName"
                                            :value="item.roleId" :disabled="item.status == 1"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="用户密码" prop="password">
                                    <el-input :disabled="editType !== 'add'" style="width: 320px;"
                                        v-model="form.password" placeholder="请输入密码" maxlength="20" show-password />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="状态" prop="status">
                                    <el-switch v-model="form.status" active-value="0" inactive-value="1" class="ml-2"
                                        style="--el-switch-on-color: #21A042;" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="处室">
                                    <el-select v-model="form.postId" placeholder="请选择处室" style="width: 320px">
                                        <el-option v-for="item in postOptions" :key="item.postId" :label="item.postName"
                                            :value="item.postId" :disabled="item.status == 1"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="用户性别">
                                    <el-select v-model="form.sex" placeholder="请选择" style="width: 320px">
                                        <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label"
                                            :value="dict.value"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="电子签名" prop="signPath">
                                    <image-upload v-model="form.signPath" width="200" height="80" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-form-item label="备注" prop="remark" style="width: 100%;">
                                <el-input type="textarea" style="width: 94%;" v-model="form.remark" placeholder="请输入备注"
                                    :autosize="{ minRows: 2 }" />
                            </el-form-item>
                        </el-row>
                    </div>
                </div>
            </el-form>
        </div>
        <div class="form_content" v-else>
            <el-form ref="assetRef" inline :model="form" :rules="rules" class="basic_from" label-width="auto">
                <p class="photo">门店照片</p>
                <div class="form_content_header">
                    <img class="form_content_image" :src="form.photo" alt="">
                    <div class="form_content_input">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="店铺名称" prop="merchantName">
                                    <div>{{ form.merchantName }}</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="手机号" prop="phoneNumber">
                                    <div>{{ form.phoneNumber }}</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="状态" prop="merchantStatue">
                                    <div>{{ form.merchantStatue }}</div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="许可证号" prop="licence">
                                    <div>{{ form.licence }}</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="坐标" prop="businessAddress">
                                    <div>{{ form.originalLongitude }}</div>
                                </el-form-item>
                                <el-form-item prop="businessAddress">
                                    <div>{{ form.originalLatitude }}</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="偏移坐标" prop="businessAddress">
                                    <div>{{ form.longitudeAfterOffset }}</div>
                                </el-form-item>
                                <el-form-item prop="businessAddress">
                                    <div>{{ form.latitudeAfterOffset }}</div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="商户负责人" prop="legalName">
                                    <div>{{ form.legalName }}</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="16">
                                <el-form-item label="所属地区" prop="countyId">
                                    <div>{{ form.county }}</div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="诚信等级" prop="chengxindengji">
                                    <div>{{ form.chengxindengji }}</div>
                                    <div style="margin-left: 10px;">级</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="经营规模" prop="jingyingguimo">
                                    <div>{{ form.jingyingguimo }}</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="终端类型" prop="zhongduancengji">
                                    <div>{{ form.zhongduancengji }}</div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </div>
                <el-row>
                    <el-form-item label="详细地址" prop="address" style="width: 100%;">
                        <div>{{ form.address }}</div>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="备注" prop="remark" style="width: 100%;">
                        <div>{{ form.remark }}</div>
                    </el-form-item>
                </el-row>
            </el-form>
        </div>
        <div class="dialog-footer">
            <el-button class="upload_submit" @click="submitForm">确 定</el-button>
            <el-button class="upload_cancel" @click="cancel">取 消</el-button>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { getUser, deptTreeSelect, updateUser, addUser } from "@/api/system/user"
import ImageUpload from "@/components/ImageUpload"
const { proxy } = getCurrentInstance()
const { sys_user_sex } = proxy.useDict("sys_user_sex")
const route = useRoute();
const editType = route.query.type
const isId = route.query.userId || null;
const deptOptions = ref([])
const postOptions = ref([])
const roleOptions = ref([])
const form = ref({})
const rules = ref({
    nickName: [{ required: true, message: "用户昵称不能为空", trigger: "blur" }],
    userName: [{ required: true, message: "用户名称不能为空", trigger: "blur" }],
    password: [{ required: true, message: "用户密码不能为空", trigger: "blur" }],
    deptId: [{ required: true, message: "归属地区不能为空", trigger: "select" }],
    roleId: [{ required: true, message: "所属角色不能为空", trigger: "select" }]
})

onMounted(() => {
    getDeptTree()
})
/** 查询地区下拉树结构 */
function getDeptTree() {
    deptTreeSelect().then(response => {
        deptOptions.value = filterDisabledDept(JSON.parse(JSON.stringify(response.data)))
        if (editType === 'edit' || editType === 'check') handleView(isId)
        else addView()
    })
}
/** 过滤禁用的地区 */
function filterDisabledDept(deptList) {
    return deptList.filter(dept => {
        if (dept.disabled) {
            return false
        }
        if (dept.children && dept.children.length) {
            dept.children = filterDisabledDept(dept.children)
        }
        return true
    })
}
// 新增 
function addView() {
    getUser().then(response => {
        postOptions.value = response.posts
        roleOptions.value = response.roles
        form.value.deptId = deptOptions.value.length ? deptOptions.value[0].id : ''
    })
}
// 编辑
function handleView(id) {
    getUser(id).then(response => {
        form.value = response.data
        postOptions.value = response.posts
        roleOptions.value = response.roles
        form.value.postId = response.postIds[0] || ''
        form.value.roleId = response.roleIds[0] || ''
    })
}
const submitForm = () => {
    proxy.$refs["assetRef"].validate(valid => {
        if (valid) {
            form.value.postIds = [form.value.postId]
            form.value.roleIds = [form.value.roleId]
            if (editType == 'edit' || editType == 'check') {
                updateUser(form.value).then(response => {
                    proxy.$modal.msgSuccess("修改成功")
                    cancel()
                })
            } else {
                addUser(form.value).then(response => {
                    proxy.$modal.msgSuccess("新增成功")
                    cancel()
                })
            }
        }
    })
}

const cancel = () => {
    const obj = { path: "/manage/user" }
    proxy.$tab.closeOpenPage(obj)
}
</script>

<style lang="scss" scoped>
.app-container {
    background-color: #f4f8ff;
    color: #333333;
}

.store_header {
    box-sizing: border-box;
    padding: 20px 30px;
    background-color: #fff;
    border-radius: 5px;

    .store_header_title {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        display: flex;
        align-items: center;
    }
}

.form_content {
    margin-top: 10px;
    padding: 20px 30px;
    background-color: #fff;
    border-radius: 5px;

    .form_content_header {
        padding: 15px;

        .form_content_image {
            width: 175px;
            height: 177px;
            background: #F5F5F5;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
    }

    .photo {
        font-size: 14px;
        color: #333333;
    }

    :deep(.el-form) {
        .el-form-item {
            margin-bottom: 23px;
        }
    }

    .form_content_input {
        margin-left: 30px;
        flex: 1;
    }
}

.upload_submit,
.upload_cancel {
    box-sizing: border-box;
    width: 140px;
    height: 45px;
    font-size: 16px;
    border: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
}

.dialog-footer {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 20px 30px;
    background-color: #fff;
    margin-top: 10px;

    .upload_submit {
        color: #fff;
        background-color: #21A042;
    }

    .upload_cancel {
        border: 1px solid #21A042;
        color: #21A042;
    }
}
</style>
<template>
    <div class="app-container">
        <div class="store_header">
            <div class="store_header_title">
                <div>{{ name }}</div>
            </div>
        </div>
        <div class="store_content">
            <div class="store_content_header">
                <div class="title_line"></div>
                <div class="title">考核时间</div>
            </div>
            <el-form class="store_content_form" ref="quarterlyassessmentsRef" label-width="68">
                <el-row>
                    <el-col :span="10">
                        <el-form-item label="时间" prop="leader">
                            <el-date-picker v-model="assessmentsDate" type="date" placeholder="选择考核时间"
                                style="width: 326px" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div class="store_content" style="padding: 0;">
            <div class="store_content_header" style="padding: 20px 30px 0 30px;">
                <div class="title_line"></div>
                <div class="title">选择考核商户</div>
            </div>
            <div class="from_header" style="padding: 20px 30px;">
                <el-form ref="queryRef" :inline="true">
                    <el-form-item :label="new_merchant_zdlx.name.join(':')">
                        <div v-for="(item, index) in new_merchant_zdlx.list" :key="index">
                            <el-input-number v-model="item.value" :min="0" style="width: 50px" :controls="false" />
                            <span style="margin: 0 10px;" v-if="index !== new_merchant_zdlx.list.length - 1">:</span>
                        </div>
                    </el-form-item>
                    <el-form-item :label="new_merchant_sclx.name.join(':')">
                        <div v-for="(item, index) in new_merchant_sclx.list" :key="index">
                            <el-input-number v-model="item.value" :min="0" style="width: 50px" :controls="false" />
                            <span style="margin: 0 10px;" v-if="index !== new_merchant_sclx.list.length - 1">:</span>
                        </div>
                    </el-form-item>
                    <el-form-item :label="new_merchant_cj.name.join(':')">
                        <div v-for="(item, index) in new_merchant_cj.list" :key="index">
                            <el-input-number v-model="item.value" :min="0" style="width: 50px" :controls="false" />
                            <span style="margin: 0 10px;" v-if="index !== new_merchant_cj.list.length - 1">:</span>
                        </div>
                    </el-form-item>
                    <el-form-item label="抽取数量" prop="num">
                        <el-input-number v-model="num" :min="0"
                            :max="Math.max(0, inspectedHouseholds - newMerchantsByConditions.length)"
                            style="width: 160px" :controls="false" placeholder="请输入抽取数量" />
                    </el-form-item>
                    <el-form-item>
                        <el-button class="btn-search" @click="handleQuery">立即抽取</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="merchants">
                <div class="merchants_left">
                    <div class="merchants_left_header">
                        <div class="header__left">
                            <div class="store_content_header">
                                <div class="title_line"></div>
                                <div class="title">考核商户</div>
                            </div>
                        </div>
                    </div>
                    <div class="from_container" style="padding: 0;">
                        <el-table :data="merchantsByConditions" border style="width: 100%" max-height="550"
                            :row-class-name="tableRowClassName">
                            <el-table-column label="店铺名" prop="merchantName" align="center" />
                            <el-table-column label="终端类型" align="center">
                                <template #default="scope">
                                    {{ scope.row.zhongduancengji }}
                                </template>
                            </el-table-column>
                            <el-table-column label="市场类型" align="center">
                                <template #default="scope">
                                    {{ scope.row.marketType }}
                                </template>
                            </el-table-column>
                            <el-table-column label="采集类型" align="center">
                                <template #default="scope">
                                    {{ getCj(scope.row.collectionType) }}
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
                <div class="merchants_right">
                    <div class="store_content_header">
                        <div class="title_line"></div>
                        <div class="title">路线规划</div>
                    </div>
                    <div class="map_img">
                        <div :id="'container' + monthlyId" style="height: 100%;"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="dialog-footer">
            <el-button class="upload_submit" @click="submitForm">确 定</el-button>
            <el-button class="upload_cancel" @click="cancel">取 消</el-button>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue'
import cache from '@/plugins/cache'
import { saveSelectedStaffAndMerchants, listMerchantsByMonthlyId, getQuarterlyassessments } from "@/api/quarterlyassessments/quarterlyassessments"
import { selectMerchants } from "@/api/merchant/info"
import { ElLoading, ElMessage } from 'element-plus'
import mapStart from '@/assets/images/map/new-start.png'
import mapMid from '@/assets/images/map/new-mid.png'

const { proxy } = getCurrentInstance()
const { merchant_zdlx, merchant_sclx, merchant_cj } = proxy.useDict("merchant_zdlx", "merchant_sclx", "merchant_cj")
const data = cache.session.getJSON('configData')
const monthlyId = data.id;
const name = data.name;
const quarterId = data.quarterId;
const inspectedHouseholds = data.inspectedHouseholds || 0;
const jsonData = ref(data.jsonData);
const num = ref(0)
const merchantsByConditions = ref([])
const newMerchantsByConditions = ref([])
const pointList = ref([])
const assessmentsDate = ref('')
const new_merchant_zdlx = ref({
    name: [],
    list: []
})
const new_merchant_sclx = ref({
    name: [],
    list: []
})
const new_merchant_cj = ref({
    name: [],
    list: []
})

onMounted(() => {
    // 加载已保存的月度考核数据（人员配置和商户数据）
    loadMonthlyAssessmentData()
    getMerchant()
})

const getMerchant = () => {
    new_merchant_zdlx.value = {
        name: [],
        list: []
    }
    new_merchant_sclx.value = {
        name: [],
        list: []
    }
    new_merchant_cj.value = {
        name: [],
        list: []
    }
    merchant_zdlx.value.forEach(el => {
        new_merchant_zdlx.value.name.push(el.label)
        new_merchant_zdlx.value.list.push({ value: 0 })
    })
    merchant_sclx.value.forEach(el => {
        new_merchant_sclx.value.name.push(el.label)
        new_merchant_sclx.value.list.push({ value: 0 })
    })
    merchant_cj.value.forEach(el => {
        new_merchant_cj.value.name.push(el.label)
        new_merchant_cj.value.list.push({ value: 0 })
    })
}

const getCj = (type) => {
    const item = merchant_cj.value.find(el => el.value == type)
    return item.label
}

const initMap = () => {
    const map = new AMap.Map('container' + monthlyId, {
        resizeEnable: true,
        center: [115.042297, 35.759642] // 初始中心点坐标
    });
    map.on('complete', async function () {
        if (!merchantsByConditions.value.length) return
        let lineList = []
        let list = []
        if (jsonData.value) {
            lineList = JSON.parse(jsonData.value);
            list = JSON.parse(JSON.stringify(merchantsByConditions.value))
            list.unshift({ longitudeAfterOffset: '115.042297', latitudeAfterOffset: '35.759642' })
            list.push({ longitudeAfterOffset: '115.042297', latitudeAfterOffset: '35.759642' })
        } else {
            const options = JSON.parse(JSON.stringify(merchantsByConditions.value))
            options.unshift({ longitudeAfterOffset: '115.042297', latitudeAfterOffset: '35.759642' })
            options.push({ longitudeAfterOffset: '115.042297', latitudeAfterOffset: '35.759642' })
            const arr = splitPointsIntoSegments(options)
            const arr2 = []
            for (let i = 0; i < arr.length; i++) {
                const path = await getDrivingPath(arr[i])
                arr2.push(path)
            }
            lineList = arr2.flat();
            list = arr.flat();
        }
        usePolyline(map, lineList, list)
    });
}

function splitPointsIntoSegments(points, segmentSize = 15) {
    if (!Array.isArray(points) || points.length === 0) {
        return [];
    }
    const segments = [];
    let startIndex = 0;
    while (startIndex < points.length) {
        let endIndex = startIndex + segmentSize;
        if (endIndex > points.length) {
            endIndex = points.length;
        }
        const segment = points.slice(startIndex, endIndex);
        segments.push(segment);
        startIndex = endIndex - 1;
        if (startIndex >= points.length - 1) {
            break;
        }
    }
    return segments;
}

const usePolyline = (map, path, list) => {
    const arrowSymbol = {
        path: 'M 0,-2 L 4,0 L 0,2 Z',  // SVG路径定义箭头形状
        fillColor: 'white',            // 填充色
        fillOpacity: 1,                // 不透明度
        scale: 1.5,                    // 缩放比例
        strokeColor: 'white',          // 描边色
        strokeWeight: 1                // 描边宽度
    };
    const polyline = new AMap.Polyline({
        path,              // 设置路径点
        strokeColor: "#0091ff", // 自定义颜色
        strokeWeight: 6,        // 宽度
        strokeOpacity: 1,     // 透明度
        strokeStyle: "solid",   // 实线
        lineJoin: "round",      // 折线拐点圆角
        icons: [{
            icon: arrowSymbol,
            offset: '10%',    // 起始位置
            repeat: '50px'    // 每50像素重复一个箭头
        }],
        dirColor: "white",       // 方向箭头颜色
        showDir: true            // 显示方向箭头
    });
    const len = list.length - 1;
    const startIcon = new AMap.Icon({
        size: new AMap.Size(35, 40),
        image: mapStart,
        imageSize: new AMap.Size(35, 40),
    });
    const startMarker = new AMap.Marker({
        position: new AMap.LngLat(115.042297, 35.759642),
        icon: startIcon,
        offset: new AMap.Pixel(-13, -30)
    });
    const endIcon = new AMap.Icon({
        size: new AMap.Size(35, 40),
        image: mapStart,
        imageSize: new AMap.Size(35, 40),
    });
    const endMarker = new AMap.Marker({
        position: new AMap.LngLat(list[len].longitudeAfterOffset, list[len].latitudeAfterOffset),
        icon: endIcon,
        offset: new AMap.Pixel(-13, -30)
    });
    let markers = [];
    const marIcon = new AMap.Icon({
        size: new AMap.Size(35, 40),
        image: mapMid,
        imageSize: new AMap.Size(35, 40),
    });
    for (let i = 0; i < list.length; i++) {
        markers.push({ position: [list[i].longitudeAfterOffset, list[i].latitudeAfterOffset] })
    }
    markers = markers.slice(1, -1)
    markers.forEach(function (marker, index) {
        new AMap.Marker({
            map: map,
            content: `<div style="position: relative;">
            <img style="width: 35px;height: 40px" src="${mapMid}" alt="">
            <div style="position: absolute;left: 0;top: 8px;color: #fff;display: flex;
        justify-content: center;width:100%;height:100%;font-size:12px">${index + 1}</div>
            </div>`,
            position: [marker.position[0], marker.position[1]],
            offset: new AMap.Pixel(-13, -30)
        });
    });
    map.add([startMarker, endMarker]);
    pointList.value = path
    polyline.setMap(map);
    map.setFitView([polyline]);
}

const getDrivingPath = (list) => {
    return new Promise((resolve, reject) => {
        const driving = new AMap.Driving({
            policy: AMap.DrivingPolicy.LEAST_TIME, // 策略设为最少时间
            showTraffic: false, // 关闭交通路况显示
        });
        const start = [list[0].longitudeAfterOffset, list[0].latitudeAfterOffset]; // 起点坐标
        const waypoints = [];// 途经点坐标数组
        const len = list.length - 1;
        for (let i = 0; i < len; i++) {
            waypoints.push([list[i].longitudeAfterOffset, list[i].latitudeAfterOffset])
        }
        const end = [list[len].longitudeAfterOffset, list[len].latitudeAfterOffset]; // 终点坐标
        driving.search(start, end, {
            waypoints: waypoints, // 设置途经点
            policy: AMap.DrivingPolicy.LEAST_TIME // 设置策略为最少时间
        }, function (status, result) {
            if (status === 'complete') {
                const route = result.routes[0];
                let path = [];
                route.steps.forEach(step => {
                    path = path.concat(step.path);
                });
                resolve(path)
            } else {
                console.log('获取驾车数据失败：' + result)
            }
        });
    })
}
/** 加载已保存的月度考核数据 */
async function loadMonthlyAssessmentData() {
    try {
        const quarterlyResponse = await getQuarterlyassessments(quarterId)
        if (quarterlyResponse.code === 200 && quarterlyResponse.data && quarterlyResponse.data.monthlyAssessmentsList) {
            // 找到当前月度考核的数据
            const currentMonthlyAssessment = quarterlyResponse.data.monthlyAssessmentsList[0]
            if (currentMonthlyAssessment && currentMonthlyAssessment.staffMap) {
                assessmentsDate.value = currentMonthlyAssessment.kaoheDate
            }
        }
        // 获取已配置的商户列表
        const merchantResponse = await listMerchantsByMonthlyId(monthlyId)
        if (merchantResponse.code === 200 && merchantResponse.data) {
            // 转换商户数据格式以适配现有的表格显示
            const merchants = merchantResponse.data.map(item => ({
                id: item.merchantInfo.id,
                merchantName: item.merchantInfo.merchantName,
                county: item.merchantInfo.county,
                countyId: item.merchantInfo.countyId,
                chengxindengji: item.merchantInfo.chengxindengji,
                zhongduancengji: item.merchantInfo.zhongduancengji,
                jingyingguimo: item.merchantInfo.jingyingguimo,
                longitudeAfterOffset: item.merchantInfo.longitudeAfterOffset,
                latitudeAfterOffset: item.merchantInfo.latitudeAfterOffset,
                marketType: item.merchantInfo.marketType,
                collectionType: item.merchantInfo.collectionType
                // 其他商户信息字段...
            }))
            merchantsByConditions.value = merchants
            newMerchantsByConditions.value = JSON.parse(JSON.stringify(merchants))
            initMap()
        }
    } catch (error) {
        console.error('加载月度考核数据失败:', error)
        // 不显示错误提示，因为可能是首次创建，没有保存的数据
    }
}
/** 过滤禁用的地区 */
const filterDisabledDept = (deptList) => {
    return deptList.filter(dept => {
        if (dept.disabled) {
            return false
        }
        if (dept.children && dept.children.length) {
            dept.children = filterDisabledDept(dept.children)
        }
        return true
    })
}
// table样式
const tableRowClassName = ({ row, rowIndex }) => {
    if ((rowIndex + 1) % 2 === 0) {
        return 'table-row'
    }
    return ''
}
const handleQuery = () => {
    const loading = ElLoading.service({
        lock: true,
        text: '正在抽取中，请稍后...',
        background: 'rgba(0, 0, 0, 0.7)',
    })
    const arr = []
    const arr1 = []
    const arr2 = []
    new_merchant_zdlx.value.list.forEach(el => {
        arr.push(el.value)
    })
    new_merchant_sclx.value.list.forEach(el => {
        arr1.push(el.value)
    })
    new_merchant_cj.value.list.forEach(el => {
        arr2.push(el.value)
    })
    const params = {
        areaRatio: arr1.join(':'),
        terminalRatio: arr.join(':'),
        collectionRatio: arr2.join(':'),
        startLongitude: 115.042297,
        startLatitude: 35.759642,
        num: num.value
    }
    selectMerchants(params).then(res => {
        if (res.code === 200) {
            merchantsByConditions.value = res.data.merchants || []
            newMerchantsByConditions.value = JSON.parse(JSON.stringify(merchantsByConditions.value))
            jsonData.value = '';
            initMap()
            loading.close()
        }
    })
}

const submitForm = () => {
    if (inspectedHouseholds > 0 && merchantsByConditions.value.length < inspectedHouseholds) {
        return ElMessage({
            message: `考核商户不得少于${inspectedHouseholds}条!`,
            type: 'warning',
        })
    }
    const merchantData = {
        monthlyAssessmentId: parseInt(monthlyId),
        selectedMerchants: merchantsByConditions.value,
        selectionType: "0"  // 使用字典值：0-考核商户，1-自主添加商户，2-自主添加无证户
    }

    const submitData = {
        merchantData
    }
    if (assessmentsDate.value) {
        submitData.assessmentsDate = assessmentsDate.value
        submitData.assessmentsId = parseInt(monthlyId)
    }
    if (pointList.value.length) submitData.jsonData = JSON.stringify(pointList.value)
    saveSelectedStaffAndMerchants(submitData).then(response => {
        if (response.code === 200) {
            proxy.$modal.msgSuccess("保存成功")
            cancel()
        } else {
            proxy.$modal.msgError(response.msg || "保存失败")
        }
    }).catch(error => {
        console.error('保存失败:', error)
        proxy.$modal.msgError("保存失败，请重试")
    })
}

const cancel = () => {
    const params = {
        path: "/assessment/quarterlyassessments/childs/configuration",
        query: {
            id: quarterId
        }
    }
    proxy.$tab.closeOpenPage(params)
}
</script>

<style lang="scss" scoped>
.app-container {
    background-color: #f4f8ff;
    color: #333333;
}

.store_header {
    box-sizing: border-box;
    padding: 20px 30px;
    background-color: #fff;
    border-radius: 5px;

    .store_header_title {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        display: flex;
        align-items: center;
    }
}

.store_content {
    box-sizing: border-box;
    padding: 20px 30px;
    background-color: #fff;
    border-radius: 5px;
    margin-top: 10px;

    .store_content_header {
        display: flex;
        align-items: center;

        .title_line {
            width: 3px;
            height: 19px;
            background: #21A042;
            border-radius: 2px;
        }

        .title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-left: 10px;
        }
    }

    .store_content_form {
        margin-top: 25px;
    }
}

.dialog-footer {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 20px 30px;
    background-color: #fff;
    margin-top: 10px;

    .upload_submit,
    .upload_cancel {
        box-sizing: border-box;
        width: 140px;
        height: 45px;
        font-size: 16px;
        border: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
    }

    .upload_submit {
        color: #fff;
        background-color: #21A042;
    }

    .upload_cancel {
        border: 1px solid #21A042;
        color: #21A042;
    }
}

.from_container {
    min-height: 120px;
}

.from_header {
    padding-left: 0;
}

.btn-search {
    width: 126px;
}

.merchants {
    display: flex;
    border-top: 1px solid #f5f5f5;

    .merchants_left {
        width: 55%;
        padding: 30px;
        border-right: 1px solid #f5f5f5;

        .merchants_left_header {
            display: flex;
            align-items: center;
            justify-content: space-between;

            :deep(.el-select) {
                width: 220px;

                .el-select__wrapper {
                    box-shadow: 0 0 0 0 !important;
                    background-color: #f7f8fa;

                    .el-icon {
                        color: #00b578;
                    }
                }
            }

            .header__left {
                display: flex;
                align-items: center;
            }
        }
    }

    .merchants_right {
        padding: 30px;

        .map_img {
            width: 686px;
            height: 554px;
            margin-top: 20px;
        }
    }
}
</style>
<template>
    <div class="main">
        <el-table :data="monthlyAssessmentsList" border style="width: 100%" max-height="300"
            :row-class-name="tableRowClassName">
            <el-table-column label="名称" prop="name" width="300" align="center" />
            <el-table-column label="考核类型" prop="type" width="150" align="center" />
            <el-table-column label="总题数" prop="num" width="150" align="center" />
            <el-table-column label="综合得分" prop="score" width="150" align="center" />
            <el-table-column label="创建人" prop="createBy" align="center" />
            <el-table-column label="状态" align="center">
                <template #default="scope">
                    <div class="status_text">
                        <div class="icon"></div>
                        <div>{{ scope.row.status }}</div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="createTime" align="center" width="180" />
            <el-table-column label="操作" width="200" align="center">
                <template #default="scope">
                    <div class="table-btn">
                        <el-button class="table-btn-edit" link @click="handleUpdate(scope.row)">查看</el-button>
                        <div class="btn-line"></div>
                        <el-button class="table-btn-edit" link @click="handleUpdate(scope.row)">修改</el-button>
                        <div class="btn-line"></div>
                        <el-button class="table-btn-edit" link @click="handleUpdate(scope.row)">发布</el-button>
                        <div class="btn-line"></div>
                        <el-button class="table-btn-delete" link @click="handleUpdate(scope.row)">禁用</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup>
const props = defineProps({
    monthlyAssessmentsList: {
        type: Array,
        default: [{
            name: "第1季度物流配送工作考核",
            type: "目标",
            num: "23",
            score: "-",
            createBy: "曹雪阳",
            status: "未发布",
            createTime: "2025-03-16 10:37:40",
        },
        {
            name: "第1季度机关各部门考核",
            type: "目标",
            num: "23",
            score: "-",
            createBy: "曹雪阳",
            status: "未发布",
            createTime: "2025-03-16 10:37:40",
        },
        {
            name: "第1季度卷烟销售主要指标",
            type: "目标",
            num: "23",
            score: "-",
            createBy: "曹雪阳",
            status: "未发布",
            createTime: "2025-03-16 10:37:40",
        },
        {
            name: "第1季度考核情况汇总",
            type: "目标",
            num: "23",
            score: "-",
            createBy: "曹雪阳",
            status: "未发布",
            createTime: "2025-03-16 10:37:40",
        }]
    }
})
const emits = defineEmits(['handleUpdate'])
// table样式
const tableRowClassName = ({ row, rowIndex }) => {
    if ((rowIndex + 1) % 2 === 0) {
        return 'table-row'
    }
    return ''
}

const handleUpdate = (row) => {
    emits('handleUpdate', row)
}
</script>

<style lang="scss" scoped>
.status_text {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FF3B3B;

    .icon {
        width: 8px;
        height: 8px;
        background: #FE2121;
        border-radius: 50%;
        margin-right: 5px;
    }
}
</style>
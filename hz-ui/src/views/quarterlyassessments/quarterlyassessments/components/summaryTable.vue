<template>
    <div class="main">
        <el-table :data="monthlyAssessmentsList" border style="width: 100%" max-height="300"
            :row-class-name="tableRowClassName" show-summary :summary-method="getSummaries">
            <el-table-column label="考核项" prop="name" align="center" />
            <el-table-column label="得分系数" prop="get" align="center" />
            <el-table-column label="赋分系数" prop="score" align="center" />
            <el-table-column label="状态" align="center" width="150">
                <template #default="scope">
                    <div :style="{ color: scope.row.status === '正常' ? '#049B01' : '#FF0000' }">{{ scope.row.status }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <div class="table-btn">
                        <el-button v-if="scope.row.status !== '正常'" class="table-btn-edit" link
                            @click="handleUpdate(scope.row)">启用</el-button>
                        <el-button v-else class="table-btn-delete" link @click="handleUpdate(scope.row)">禁用</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup>
const props = defineProps({
    monthlyAssessmentsList: {
        type: Array,
        default: [{
            name: "7月份现场考核",
            get: "0.57",
            score: "0.57",
            status: "正常"
        },
        {
            name: "8月份现场考核",
            get: "0.36",
            score: "0.36",
            status: "正常"
        },
        {
            name: "9月份现场考核",
            get: "0.81",
            score: "0.81",
            status: "正常"
        },
        {
            name: "第1季度物流配送工作考核",
            get: "0.47",
            score: "0.47",
            status: "不汇总"
        },
        {
            name: "第1季度机关各部门考核",
            get: "0.10",
            score: "0.10",
            status: "正常"
        },
        {
            name: "第1季度卷烟销售主要指标",
            get: "0.70",
            score: "0.70",
            status: "正常"
        }]
    }
})
const emits = defineEmits(['handleUpdate'])
// table样式
const tableRowClassName = ({ row, rowIndex }) => {
    if ((rowIndex + 1) % 2 === 0) {
        return 'table-row'
    }
    return ''
}

const getSummaries = (param) => {
    const { columns, data } = param
    const sums = []
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = h('div', { style: { color: '#333' } }, [
                '汇总得分',
            ])
            return
        }
        const values = data.map((item) => Number(item[column.property]))
        if (!values.every((value) => Number.isNaN(value))) {
            sums[index] = `${values.reduce((prev, curr) => {
                const value = Number(curr)
                if (!Number.isNaN(value)) {
                    return prev + curr
                } else {
                    return prev
                }
            }, 0)}`
        }
        if (index === columns.length - 1) {
            sums[index] = h('div', { style: { color: '#049B01', cursor: 'pointer' } }, [
                '查看得分',
            ])
            return
        }
    })

    return sums
}


const handleUpdate = (row) => {
    emits('handleUpdate', row)
}
</script>

<style scoped lang="scss">
:deep(.el-table) {
    .el-table__footer {
        td {
            background-color: #E8FFEE !important;
            border: 0;
        }
    }
}
</style>
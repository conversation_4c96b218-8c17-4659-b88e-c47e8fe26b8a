<template>
    <div class="box">
        <div class="content" v-for="(item, index) in selectList" :key="index">
            <div class="title">
                <el-icon>
                    <CaretBottom />
                </el-icon>
                <div class="label">{{ item.label }}</div>
            </div>
            <div class="list">
                <div class="list_item" v-for="(chir, chirIndex) in item.list" :key="chirIndex"
                    @click="chooseQuestion(item, chir)">
                    <div class="icon"></div>
                    <div class="name">{{ chir.label }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
const { proxy } = getCurrentInstance()
const { topic_select, topic_blanks, topic_images } = proxy.useDict("topic_select", "topic_blanks", "topic_images")
const emits = defineEmits(['chooseQuestion'])

const selectList = ref([
    {
        label: '选择题',
        type: 'topic_select',
        list: topic_select
    },
    {
        label: '填空题',
        type: 'topic_blanks',
        list: topic_blanks
    },
    {
        label: '图片上传',
        type: 'topic_images',
        list: topic_images
    }
])
// 选择题型
const chooseQuestion = (item, chir) => {
    emits('chooseQuestion', item, chir)
}
</script>

<style scoped lang="scss">
.box {
    .content {
        margin-top: 30px;
        color: #333333;

        .title {
            display: flex;
            align-items: center;
            font-size: 18px;
            font-weight: bold;

            .label {
                margin-left: 5px;
            }
        }

        .list {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;

            .list_item {
                width: 50%;
                display: flex;
                align-items: center;
                margin-top: 20px;
                color: #333333;
                cursor: pointer;

                .icon {
                    width: 12px;
                    height: 12px;
                    background: #049B01;
                    border: 1.5px solid rgba(255, 253, 253, 0.57);
                    border-radius: 50%;
                    margin-right: 5px;
                }
            }
        }
    }
}
</style>
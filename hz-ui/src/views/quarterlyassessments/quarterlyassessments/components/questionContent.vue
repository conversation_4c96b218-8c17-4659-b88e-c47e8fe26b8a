<template>
    <div class="question">
        <div class="question_list" :class="{ active: item.active }" v-for="(item, index) in questionOptions"
            :key="index" @click="chooseQuestionItem(index)">
            <div class="question_list_item">
                <div class="label">{{ item.title }} <span v-if="item.isRequired == 1" style="color: #FE2121;">*</span>
                </div>
                <div class="message" v-if="item.prompt">{{ item.prompt }}</div>
                <div class="options" :class="'options' + questionClassMap[item.smallType]">
                    <div class="item" v-for="(chir, chirIndex) in item.examinationReplyList" :key="chirIndex">
                        <div class="select"
                            v-if="questionClassMap[item.smallType] == 0 || questionClassMap[item.smallType] == 1">
                            <div :class="questionClassMap[item.smallType] == 0 ? 'single_icon' : 'multiple_icon'"></div>
                            <div class="single_name">
                                {{ chir.name }}
                                <span class="score" v-if="chir.score">(扣{{ chir.score }}分)</span>
                            </div>
                        </div>
                        <div class="blank" :style="{ marginTop: chirIndex == 0 ? '0' : '20px' }"
                            v-if="questionClassMap[item.smallType] == 2 || questionClassMap[item.smallType] == 3">
                            <div class="single_name">
                                {{ chir.name }}
                                <span class="score" v-if="chir.score">(扣{{ chir.score }}分)</span>
                            </div>
                            <div class="input"></div>
                        </div>
                        <div class="blank" v-if="questionClassMap[item.smallType] == 6">
                            <div class="textarea"></div>
                        </div>
                        <div class="image"
                            v-if="questionClassMap[item.smallType] == 4 || questionClassMap[item.smallType] == 5">
                            <el-icon>
                                <PictureFilled />
                            </el-icon>
                            <div class="single_name">
                                {{ chir.name }}
                                <span class="score" v-if="chir.score">(扣{{ chir.score }}分)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { watch } from 'vue';

const props = defineProps({
    questionList: {
        type: Array,
        default: () => []
    }
})

const emits = defineEmits(['chooseQuestionItem'])

const questionClassMap = ref({
    'single_select': 0,
    'multiple_select': 1,
    'single_blank': 2,
    'multiple_blank': 3,
    'single_image': 4,
    'multiple_image': 5,
    'notes_blank': 6,
})
const questionOptions = ref([])

const chooseQuestionItem = (index) => {
    emits('chooseQuestionItem', index)
}

watch(() => props.questionList, () => {
    questionOptions.value = JSON.parse(JSON.stringify(props.questionList))
}, {
    deep: true,
    immediate: true
})
</script>

<style lang="scss" scoped>
.question {
    .question_list {
        padding: 15px 50px;
        border-top: 1px solid transparent;
        border-bottom: 1px solid transparent;

        .question_list_item {

            .label {
                color: #333333;
                font-weight: 600;
            }

            .message {
                margin-top: 15px;
                font-size: 14px;
                color: #333333;
            }

            .options {
                margin-top: 20px;

                .item {
                    .select {
                        display: flex;
                        align-items: center;
                        margin: 0 45px 10px 0;
                    }

                    .blank {
                        display: flex;
                        align-items: center;

                        .input {
                            flex: 1;
                            height: 40px;
                            background: #9999990d;
                            border-radius: 5px;
                            margin-left: 20px;
                        }

                        .textarea {
                            flex: 1;
                            height: 80px;
                            background: #9999990d;
                            border-radius: 5px;
                        }
                    }

                    .image {
                        display: flex;
                        align-items: center;
                        margin-right: 45px;

                        .el-icon {
                            margin-right: 5px;
                        }
                    }

                    .single_icon {
                        width: 15px;
                        height: 15px;
                        background: #FFFFFF;
                        border: 1px solid #DEDEDE;
                        border-radius: 50%;
                        margin-right: 5px;
                    }

                    .multiple_icon {
                        width: 15px;
                        height: 15px;
                        background: #FFFFFF;
                        border: 1px solid #DEDEDE;
                        border-radius: 3px;
                        margin-right: 5px;
                    }

                    .single_name {
                        font-size: 14px;
                        color: #333333;

                        .score {
                            margin-left: 5px;
                        }
                    }
                }
            }

            .options0,
            .options1,
            .options4,
            .options5 {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
            }

        }
    }

    .active {
        border-color: #049B01;
        background: rgba(4, 155, 1, 0.1);
    }
}
</style>
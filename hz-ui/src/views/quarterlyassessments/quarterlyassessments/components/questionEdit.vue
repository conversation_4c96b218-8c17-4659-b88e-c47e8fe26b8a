<template>
    <div class="box">
        <div class="header">
            模块编辑
        </div>
        <div class="content" style="margin-bottom: 10px;">
            <div class="title">
                <div class="line"></div>
                标题名称：
            </div>
            <el-input v-model="questionData.title" style="width: 100%;margin-top: 8px;" :autosize="{ minRows: 5 }"
                type="textarea" placeholder="请输入标题名称" />
        </div>
        <div class="is_required">
            <el-checkbox v-model="questionData.isRequired" label="必填" size="large" :true-value="1" :false-value="0" />
        </div>
        <div class="content">
            <div class="title">
                <div class="line"></div>
                填写提示：
            </div>
            <el-input v-model="questionData.prompt" style="width: 100%;margin-top: 8px;" :autosize="{ minRows: 2 }"
                type="textarea" placeholder="请输入提示信息" />
        </div>
        <div class="content" v-if="questionData.smallType !== 'notes_blank'">
            <div class="content_header">
                <div class="title">
                    <div class="line"></div>
                    选项
                </div>
                <div class="add" @click="add">
                    <el-icon>
                        <CirclePlus />
                    </el-icon>
                    <span>添加选项</span>
                </div>
            </div>
            <div class="choose">
                <div class="choose_item" v-for="(item, index) in questionData.examinationReplyList" :key="index">
                    <div class="item_label">选项{{ index + 1 }}：</div>
                    <div class="item_input">
                        <el-input v-model="item.name" style="width: 100%;height: 40px;" placeholder="请输入名称" />
                        <el-icon @click="deleteSelect(item, index)">
                            <Remove />
                        </el-icon>
                    </div>
                    <div class="score"
                        v-if="questionData.majorType !== 'majorType' && questionData.majorType !== 'topic_blanks' && questionData.majorType !== 'topic_images'">
                        <div class="score_label">
                            分值：
                        </div>
                        <div class="score_value">
                            <el-input-number v-model="item.score" :min="0" style="width: 130px;height: 40px;"
                                :controls="false" />
                            <span class="score_text">分</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer_btn">
            <div class="complete_edit" @click="comoleteEdit">完成编辑</div>
            <div class="delete_select" @click="editDelete">删除</div>
        </div>
    </div>
</template>

<script setup>
import { watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus'
const emits = defineEmits(['comoleteEdit', 'editDelete'])

const props = defineProps({
    question: {
        type: Object,
        default: () => { }
    }
})

const questionData = ref({})

// 添加选项
const add = () => {
    questionData.value.examinationReplyList.push({
        name: '',
        score: 0
    })
}
// 删除选项
const deleteSelect = (item, index) => {
    ElMessageBox.confirm(
        '是否确认删除选项' + (index + 1) + '?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(() => {
            questionData.value.examinationReplyList.splice(index, 1)
            ElMessage({
                type: 'success',
                message: '删除成功',
            })
        })
}
// 保存编辑
const comoleteEdit = () => {
    const len = questionData.value.examinationReplyList.length;
    const typeList = ["single_blank", "single_image"]
    if (!typeList.includes(questionData.value.smallType) && len < 2) {
        return ElMessage({
            type: 'warning',
            message: '请至少添加两个选项！',
        })
    }
    emits('comoleteEdit', questionData.value)
}

// 删除当前题
const editDelete = () => {
    emits('editDelete', questionData.value)
}

watch(() => props.question, () => {
    console.log(props.question)
    questionData.value = JSON.parse(JSON.stringify(props.question))
}, {
    immediate: true,
    deep: true
})
</script>

<style lang="scss" scoped>
.header {
    font-weight: 600;
    font-size: 18px;
    color: #333333;
    text-align: center;
}

.content {
    margin-bottom: 50px;

    .content_header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .add {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #21A042;
            cursor: pointer;

            span {
                margin-left: 4px;
            }
        }
    }

    .title {
        display: flex;
        align-items: center;
        color: #333333;
        font-weight: 600;

        .line {
            width: 4px;
            height: 16px;
            background: #049B01;
            border-radius: 2px;
            margin-right: 10px;
        }
    }

    .choose {
        padding: 15px 15px 0 15px;
        margin-top: 30px;
        background: #F5F5F5;
        color: #333333;

        .choose_item {
            padding-bottom: 20px;

            .item_label {
                font-weight: 600;
            }

            .item_input {
                display: flex;
                align-items: center;
                margin-top: 10px;

                .el-icon {
                    font-size: 18px;
                    color: #FF0000;
                    margin-left: 10px;
                    cursor: pointer;
                }
            }

            .score {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 10px;
                font-size: 14px;

                .score_text {
                    margin-left: 14px;
                }
            }
        }
    }
}

.is_required {
    margin-bottom: 20px;

    :deep(.el-checkbox) {
        .el-checkbox__inner {
            width: 16px;
            height: 16px;
        }

        .el-checkbox__inner:after {
            top: 2px;
            left: 5px;
        }

        .el-checkbox__label {
            font-size: 16px;
            color: #333;
            font-weight: 600;
        }
    }
}

.footer_btn {
    padding: 35px;

    .complete_edit,
    .delete_select {
        line-height: 42px;
        text-align: center;
        color: #FFFFFF;
        border-radius: 6px;
        font-size: 14px;
        letter-spacing: 1px;
        cursor: pointer;
    }

    .complete_edit {
        background: #21A042;
    }

    .delete_select {
        background: #EA0012;
        margin-top: 25px;
    }
}
</style>
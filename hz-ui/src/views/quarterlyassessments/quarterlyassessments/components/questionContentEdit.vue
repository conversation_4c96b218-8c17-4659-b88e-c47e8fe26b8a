<template>
    <div class="question">
        <div class="question_list" :class="{ active: item.active }" v-for="(item, index) in questionOptions"
            :key="index" @click="chooseQuestionItem(index)">
            <div class="question_list_item">
                <div class="label">{{ item.title }} <span v-if="item.isRequired == 1" style="color: #FE2121;">*</span>
                </div>
                <div class="message" v-if="item.prompt">{{ item.prompt }}</div>
                <div class="options" :class="'options' + questionClassMap[item.smallType]"
                    v-if="item.examinationReplyMerchantList.length">
                    <div class="item">
                        <div class="select" v-if="questionClassMap[item.smallType] == 0">
                            <div class="single_name">
                                <el-radio-group v-model="item.isAnswer">
                                    <el-radio v-for="(chir, chirIndex) in item.examinationReplyMerchantList" :key="chirIndex"
                                        :value="chir.id" :label="chir.name + ' (扣' + chir.score + '分)'" />
                                </el-radio-group>
                            </div>
                        </div>
                        <div class="select" v-if="questionClassMap[item.smallType] == 1">
                            <div class="single_name">
                                <el-checkbox-group v-model="item.isAnswer">
                                    <el-checkbox v-for="(chir, chirIndex) in item.examinationReplyMerchantList" :key="chirIndex"
                                        :value="chir.id" :label="chir.name + ' (扣' + chir.score + '分)'" />
                                </el-checkbox-group>
                            </div>
                        </div>
                        <div class="blank_edit"
                            v-if="questionClassMap[item.smallType] == 2 || questionClassMap[item.smallType] == 3">
                            <div class="blank_edit_name" :style="{ marginTop: chirIndex == 0 ? '0' : '20px' }"
                                v-for="(chir, chirIndex) in item.examinationReplyMerchantList" :key="chirIndex">
                                <div class="blank_edit_label">{{ chir.name }}</div>
                                <el-input v-model="chir.blanks" placeholder="请输入数量" />
                            </div>
                        </div>
                        <div class="blank_edit" v-if="questionClassMap[item.smallType] == 6">
                            <div class="blank_edit_name" :style="{ marginTop: chirIndex == 0 ? '0' : '20px' }"
                                v-for="(chir, chirIndex) in item.examinationReplyMerchantList" :key="chirIndex">
                                <div class="blank_edit_label">{{ chir.name }}</div>
                                <el-input v-model="chir.blanks" placeholder="请输入备注" />
                            </div>
                        </div>
                        <div class="image_edit"
                            v-if="questionClassMap[item.smallType] == 4 || questionClassMap[item.smallType] == 5">
                            <div class="image_edit_item" v-for="(chir, chirIndex) in item.examinationReplyMerchantList"
                                :key="chirIndex">
                                <div class="image_edit_item_name">
                                    {{ chir.name }}
                                    <image-upload v-model="chir.blanks" width="80" height="80"
                                        style="margin-top: 10px;" />
                                    <span class="image_edit_item_score" v-if="chir.score">(扣{{ chir.score }}分)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="options" :class="'options' + questionClassMap[item.smallType]" v-else>
                    <div class="item" v-for="(chir, chirIndex) in item.examinationReplyList" :key="chirIndex">
                        <div class="select"
                            v-if="questionClassMap[item.smallType] == 0 || questionClassMap[item.smallType] == 1">
                            <div :class="questionClassMap[item.smallType] == 0 ? 'single_icon' : 'multiple_icon'"></div>
                            <div class="single_name">
                                {{ chir.name }}
                                <span class="score" v-if="chir.score">(扣{{ chir.score }}分)</span>
                            </div>
                        </div>
                        <div class="blank" :style="{ marginTop: chirIndex == 0 ? '0' : '20px' }"
                            v-if="questionClassMap[item.smallType] == 2 || questionClassMap[item.smallType] == 3">
                            <div class="single_name">
                                {{ chir.name }}
                                <span class="score" v-if="chir.score">(扣{{ chir.score }}分)</span>
                            </div>
                            <div class="input"></div>
                        </div>
                        <div class="blank" v-if="questionClassMap[item.smallType] == 6">
                            <div class="textarea"></div>
                        </div>
                        <div class="image"
                            v-if="questionClassMap[item.smallType] == 4 || questionClassMap[item.smallType] == 5">
                            <el-icon>
                                <PictureFilled />
                            </el-icon>
                            <div class="single_name">
                                {{ chir.name }}
                                <span class="score" v-if="chir.score">(扣{{ chir.score }}分)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { watch } from 'vue';
import ImageUpload from "@/components/ImageUpload"

const props = defineProps({
    questionList: {
        type: Array,
        default: () => []
    }
})

const emits = defineEmits(['chooseQuestionItem', 'answerQuestions'])

const questionClassMap = ref({
    'single_select': 0,
    'multiple_select': 1,
    'single_blank': 2,
    'multiple_blank': 3,
    'single_image': 4,
    'multiple_image': 5,
    'notes_blank': 6,
})
const questionOptions = ref([])

const chooseQuestionItem = (index) => {
    emits('chooseQuestionItem', index)
}

watch(() => props.questionList, () => {
    questionOptions.value = JSON.parse(JSON.stringify(props.questionList))
}, {
    deep: true,
    immediate: true
})

watch(() => questionOptions.value, () => {
    const str = JSON.stringify(questionOptions.value)
    const str2 = JSON.stringify(props.questionList)
    if(str !== str2) {
        emits('answerQuestions', questionOptions.value)
    }
}, {
    deep: true
})
</script>

<style lang="scss" scoped>
.question {
    .question_list {
        padding: 15px 50px;
        border-top: 1px solid transparent;
        border-bottom: 1px solid transparent;

        .question_list_item {

            .label {
                color: #333333;
                font-weight: 600;
            }

            .message {
                margin-top: 15px;
                font-size: 14px;
                color: #333333;
            }

            .options {
                margin-top: 20px;

                .item {
                    .select {
                        display: flex;
                        align-items: center;
                        margin: 0 45px 10px 0;
                    }

                    .image_edit {
                        display: flex;
                        align-items: center;
                        font-size: 14px;
                        color: #333;

                        .image_edit_item {
                            margin-right: 45px;

                            .image_edit_item_name {
                                text-align: center;
                            }

                            .image_edit_item_score {
                                margin-top: 10px;
                            }
                        }
                    }

                    .blank_edit {
                        font-size: 14px;
                        color: #333;

                        .blank_edit_name {
                            display: flex;
                            align-items: center;

                            .el-input {
                                flex: 1;

                                :deep(.el-input__wrapper) {
                                    box-shadow: 0 0 0 0 !important;
                                    background-color: #f7f8fa;
                                }
                            }

                            .blank_edit_label {
                                margin-right: 15px;
                            }
                        }
                    }

                    .blank {
                        display: flex;
                        align-items: center;

                        .input {
                            flex: 1;
                            height: 40px;
                            background: #9999990d;
                            border-radius: 5px;
                            margin-left: 20px;
                        }

                        .textarea {
                            flex: 1;
                            height: 80px;
                            background: #9999990d;
                            border-radius: 5px;
                        }
                    }

                    .image {
                        display: flex;
                        align-items: center;
                        margin-right: 45px;

                        .el-icon {
                            margin-right: 5px;
                        }
                    }

                    .single_icon {
                        width: 15px;
                        height: 15px;
                        background: #FFFFFF;
                        border: 1px solid #DEDEDE;
                        border-radius: 50%;
                        margin-right: 5px;
                    }

                    .multiple_icon {
                        width: 15px;
                        height: 15px;
                        background: #FFFFFF;
                        border: 1px solid #DEDEDE;
                        border-radius: 3px;
                        margin-right: 5px;
                    }

                    .single_name {
                        font-size: 14px;
                        color: #333333;

                        .score {
                            margin-left: 5px;
                        }
                    }
                }
            }

            .options0,
            .options1,
            .options4,
            .options5 {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
            }

        }
    }

    .active {
        border-color: #049B01;
        background: rgba(4, 155, 1, 0.1);
    }
}
</style>
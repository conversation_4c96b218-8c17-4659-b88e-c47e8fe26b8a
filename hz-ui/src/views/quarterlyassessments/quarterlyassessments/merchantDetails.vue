<template>
    <div class="app-container">
        <div class="store_header">
            <div class="store_header_title">
                <img src="../../../assets/images/back.png" alt="" @click="cancel">
                <div>商户现场考核详情</div>
            </div>
        </div>
        <div class="store_content">
            <div class="store_content_header">
                <div class="title_line"></div>
                <div class="title">基本信息</div>
            </div>
            <div class="store_content_cont">
                <div class="store_content_image">
                    <el-image style="width: 100%; height: 100%" :src="merchantData.photo" fit="fill">
                        <template #error>
                            <div class="image-slot">
                                <el-icon><el-icon>
                                        <Picture />
                                    </el-icon></el-icon>
                            </div>
                        </template>
                    </el-image>
                </div>
                <div class="store_content_info">
                    <div class="store_content_left">
                        <div class="store_content_name">
                            {{ merchantData.merchantName }}
                            <span class="status" :class="'status' + merchantData.selectionType">{{
                                getSelectionTypeLabel() }}</span>
                        </div>
                        <div class="store_content_main">
                            <div>考核时间：{{ data.createTime }}</div>
                            <div class="county">商户负责人：{{ merchantData.legalName }}</div>
                            <div class="num">商户地址：{{ merchantData.businessAddress }}</div>
                        </div>
                        <div class="store_content_text">
                            考核人员：<span v-for="(item, index) in staffList" :key="index">{{ item.name }} ({{ item.type
                                }}) <span v-if="index !== staffList.length - 1">、</span> </span>
                        </div>
                    </div>
                    <div class="store_content_right">
                        <div class="content_num">
                            <div class="content_num_header">
                                {{ merchantData.zhuanmaiScore }}
                            </div>
                            <div class="content_num_footer">专卖现场商户综合得分</div>
                        </div>
                        <div class="line"></div>
                        <div class="content_num">
                            <div class="content_num_header">
                                {{ merchantData.yingxiaoScore }}
                            </div>
                            <div class="content_num_footer">营销现场商户综合得分</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="merchan_main">
            <div class="store_content">
                <div class="store_content_ps">
                    <div class="store_content_header">
                        <div class="title_line"></div>
                        <div class="title">商户列表</div>
                    </div>
                </div>
                <div class="from_container" style="padding: 0;">
                    <el-table :data="monthlyAssessmentsList" border style="width: 100%;height: 700px;"
                        :row-class-name="tableRowClassName" @row-click="chooseMerchant">
                        <el-table-column label="店铺名" prop="merchantName" align="center" width="300" />
                        <el-table-column label="状态" align="center">
                            <template #default="scope">
                                <div class="status_text"
                                    :style="{ color: getStatusText(scope.row, merchantData.type, 'color') }">
                                    <div>{{ getStatusText(scope.row, merchantData.type) }}</div>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
            <div class="merchan_main_right"
                v-if="Object.keys(merchantData).length && merchantData['examinationPaperList'].length && questionList.length">
                <div class="merchan_main_right_header">
                    <div class="header_item" :class="{ 'header_item_active': item.active }"
                        v-for="(item, index) in merchantData.examinationPaperList" :key="index"
                        @click="chooseExamine(item)">
                        {{ item.name }}
                        <div class="header_item_line" v-if="item.active"></div>
                    </div>
                    <el-button class="upload_submit" @click="submitForm" v-if="newAnswer.length">保 存</el-button>
                </div>
                <div class="merchan_main_right_content" v-if="questionList.length">
                    <div class="check" v-if="isCheck"></div>
                    <div class="store_content_header" style="padding: 30px 50px;">
                        <div class="title_line"></div>
                        <div class="title">商户列表</div>
                    </div>
                    <questionContentEdit :questionList="questionList" @answerQuestions="handleAnswerQuestions">
                    </questionContentEdit>
                </div>
            </div>
            <el-empty style="flex: 1;" v-else description="暂无数据" />
        </div>
    </div>
</template>
<script setup>
import cache from '@/plugins/cache'
import questionContentEdit from './components/questionContentEdit.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { summaryByMonthlyId, ReplyMerchantByPaperId, paperBatch } from "@/api/quarterlyassessments/quarterlyassessments"
const { proxy } = getCurrentInstance()
const route = useRoute()
const data = cache.session.getJSON('configData')
const id = route.query.id;
const isCheck = route.query.type === 'check' ? true : false
const monthlyAssessmentsList = ref([])
const staffList = ref([])
const merchantData = ref({})
const examineData = ref({})
const questionList = ref([])
const newAnswer = ref([])

onMounted(() => {
    staffList.value = []
    if (data.staffMap) {
        for (let i in data.staffMap) {
            staffList.value.push({
                name: data.staffMap[i],
                type: i
            })
        }
    }
    getSummaryByMonthlyId()
})

// 查询商户成绩列表
const getSummaryByMonthlyId = () => {
    const params = {
        monthlyAssessmentId: data.id
    }
    summaryByMonthlyId(params).then(res => {
        if (res.code === 200) {
            monthlyAssessmentsList.value = res.rows
            for (let i = 0; i < monthlyAssessmentsList.value.length; i++) {
                if (monthlyAssessmentsList.value[i].selectionType == '2') {
                    const data = JSON.parse(monthlyAssessmentsList.value[i].remark)
                    monthlyAssessmentsList.value[i].merchantName = data.merchantName || ''
                    monthlyAssessmentsList.value[i].legalName = data.legalName || ''
                    monthlyAssessmentsList.value[i].county = data.county || ''
                    monthlyAssessmentsList.value[i].photo = data.photo || ''
                    monthlyAssessmentsList.value[i].businessAddress = data.businessAddress || ''
                }
                monthlyAssessmentsList.value[i].zhuanmaiScore = 0
                monthlyAssessmentsList.value[i].yingxiaoScore = 0
                if (monthlyAssessmentsList.value[i].examinationPaperList.length) {
                    const map = {
                        '专卖': 0,
                        '营销': 0
                    }
                    const list = monthlyAssessmentsList.value[i].examinationPaperList
                    for (let k = 0; k < list.length; k++) {
                        list[k].active = false
                        map[list[k].type] += list[k].topicCount
                    }
                    monthlyAssessmentsList.value[i].zhuanmaiScore = map['专卖']
                    monthlyAssessmentsList.value[i].yingxiaoScore = map['营销']
                }
            }
            monthlyAssessmentsList.value.forEach(el => {
                if (el.merchantId == id) merchantData.value = el
            })
            chooseMerchant(merchantData.value)
        }
    })
}

const chooseExamine = (item) => {
    if (newAnswer.value.length) {
        ElMessageBox.confirm('当前有未保存的考核结果，是否保存？', '提示', {
            confirmButtonText: '保存',
            cancelButtonText: '不保存',
            type: 'warning',
        }).then(() => {
            submitForm()
            chooseExamineItem(item)
        }).catch(() => {
            chooseExamineItem(item)
        })
    } else {
        chooseExamineItem(item)
    }
}

const chooseExamineItem = (item) => {
    merchantData.value.examinationPaperList.forEach(el => {
        el.active = false
    })
    item.active = true
    examineData.value = item
    getReplyMerchantByPaperId(item.id, merchantData.value.merchantId)
}

const getReplyMerchantByPaperId = (paperId, merchantId) => {
    const params = {
        paperId,
        merchantId
    }
    ReplyMerchantByPaperId(params).then(res => {
        if (res.code === 200) {
            questionList.value = []
            newAnswer.value = []
            if (res.data.length) {
                for (let i = 0; i < res.data.length; i++) {
                    const list = res.data[i].examinationReplyMerchantList.length ? res.data[i]
                        .examinationReplyMerchantList : res.data[i].examinationReplyList
                    let isAnswer = null
                    if (res.data[i].smallType == 'single_select' || res.data[i].smallType ==
                        'multiple_select') {
                        let arr = []
                        for (let k = 0; k < list.length; k++) {
                            if (list[k].isAnswer) arr.push(list[k].id)
                        }
                        isAnswer = res.data[i].smallType == 'single_select' ? parseFloat(arr.join()) : arr
                    }
                    const params = {
                        title: res.data[i].title,
                        isRequired: res.data[i].isRequired,
                        prompt: res.data[i].prompt,
                        majorType: res.data[i].majorType,
                        majorTypeName: res.data[i].majorTypeName,
                        smallType: res.data[i].smallType,
                        isAnswer,
                        smallTypeName: res.data[i].smallTypeName,
                        examinationReplyMerchantList: list
                    }
                    questionList.value.push(params)
                }
            }
        }
    })
}

// table样式
const tableRowClassName = ({ row, rowIndex }) => {
    if (merchantData.value.merchantId == row.merchantId) {
        return 'table-active'
    }
    if ((rowIndex + 1) % 2 === 0) {
        return 'table-row'
    }
    return ''
}

const chooseMerchant = (row) => {
    if (newAnswer.value.length) {
        ElMessageBox.confirm('当前有未保存的考核结果，是否保存？', '提示', {
            confirmButtonText: '保存',
            cancelButtonText: '不保存',
            type: 'warning',
        }).then(() => {
            submitForm()
            chooseMerchantItem(row)
        }).catch(() => { chooseMerchantItem(row) })
    } else chooseMerchantItem(row)
}

const chooseMerchantItem = (row) => {
    merchantData.value = row
    if (row.examinationPaperList.length) {
        row.examinationPaperList.forEach(el => {
            el.active = false
        })
        row.examinationPaperList[0].active = true
        examineData.value = row.examinationPaperList[0]
        getReplyMerchantByPaperId(examineData.value.id, merchantData.value.merchantId)
    } else {
        examineData.value = {}
        questionList.value = []
    }
}

// 获取商户类型标签
const getSelectionTypeLabel = () => {
    switch (merchantData.value.selectionType) {
        case '0':
            return '考核商户'
        case '1':
            return '自主添加商户'
        case '2':
            return '自主添加无证户'
    }
}

const handleAnswerQuestions = (list) => {
    newAnswer.value = list
}

const getStatusText = (item, type, ms) => {
    let msg = ''
    let color = ''
    if (item.assessmentStatus == 1) {
        msg = '已闭店'
        color = '#FE2121'
        if (ms == 'color') return color
        else return msg
    }
    const status = type == '专卖' ? item.zhuanmaiCheckStatus : item.yingxiaoCheckStatus
    switch (status) {
        case '0':
            msg = '未考核'
            color = '#0083EA'
            break
        case '1':
            msg = '考核中'
            color = '#FF6D00'
            break
        case '2':
            msg = '考核中'
            color = '#FF6D00'
            break
        case '3':
            msg = '已考核'
            color = '#21A042'
            break
    }
    if (merchantData.value.merchantId == item.merchantId) color = '#ffffff'
    if (ms == 'color') return color
    else return msg
}

const submitForm = () => {
    for (let i = 0; i < newAnswer.value.length; i++) {
        for (let k = 0; k < newAnswer.value[i].examinationReplyMerchantList.length; k++) {
            if (newAnswer.value[i].isAnswer) {
                if (newAnswer.value[i].smallType == 'single_select') {
                    if (newAnswer.value[i].isAnswer == newAnswer.value[i].examinationReplyMerchantList[
                        k].id) {
                        newAnswer.value[i].examinationReplyMerchantList[k].isAnswer = 1
                    } else {
                        newAnswer.value[i].examinationReplyMerchantList[k].isAnswer = null
                    }
                }
                if (newAnswer.value[i].smallType == 'multiple_select') {
                    if (newAnswer.value[i].isAnswer.includes(newAnswer.value[i]
                        .examinationReplyMerchantList[k].id)) {
                        newAnswer.value[i].examinationReplyMerchantList[k].isAnswer = 1
                    } else {
                        newAnswer.value[i].examinationReplyMerchantList[k].isAnswer = null
                    }
                }
            }
        }
    }
    paperBatch(newAnswer.value, examineData.value.id, merchantData.value.merchantId).then(res => {
        if (res.code === 200) {
            ElMessage.success('保存成功！')
        }
    })
}

const cancel = () => {
    const params = {
        path: "/assessment/quarterlyassessments/childs/merchantPerformance"
    }
    proxy.$tab.closeOpenPage(params)
}
</script>
<style lang="scss" scoped>
.app-container {
    background-color: #f4f8ff;
    color: #333333;
}

.store_header {
    box-sizing: border-box;
    padding: 20px 30px;
    background-color: #fff;
    border-radius: 5px;

    .store_header_title {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        display: flex;
        align-items: center;

        img {
            cursor: pointer;
            margin-right: 12px;
        }
    }
}

.store_content_header {
    display: flex;
    align-items: center;

    .title_line {
        width: 3px;
        height: 19px;
        background: #21A042;
        border-radius: 2px;
    }

    .title {
        font-size: 20px;
        font-weight: bold;
        margin-left: 10px;
    }
}

.status_text {
    display: flex;
    align-items: center;
    justify-content: center;
}

.store_content {
    box-sizing: border-box;
    padding: 35px 30px;
    background-color: #fff;
    border-radius: 5px;
    margin-top: 10px;
    color: #333333;

    .store_content_ps {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .store_content_cont {
        display: flex;
        align-items: center;
        color: #333;
        margin-top: 20px;

        .store_content_image {
            width: 110px;
            height: 110px;
            border-radius: 5px;
            margin-right: 20px;
            background-color: #f5f5f5;

            .el-image {
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 20px;
            }
        }

        .store_content_info {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .store_content_right {
            display: flex;
            align-items: center;

            .content_num_header {
                font-weight: 600;
                font-size: 50px;
                text-align: center;
            }

            .content_num_footer {
                margin-top: 10px;
                text-align: center;
            }

            .line {
                width: 4px;
                height: 45px;
                background: #A5FFBD;
                margin: 0 40px;
            }
        }
    }

    .store_content_name {
        font-weight: 600;
        font-size: 20px;

        .status {
            font-size: 14px;
            border: 1px solid transparent;
            padding: 2px 5px;
            border-radius: 5px;
            margin-left: 15px;
        }

        .status0 {
            color: #21A042;
            background: #21a0421a;
            border-color: #21A042;
        }

        .status1 {
            color: #165DFF;
            background: #165dff1a;
            border-color: #165DFF;
        }

        .status2 {
            color: #FF1616;
            background: #ff16161a;
            border-color: #FF1616;
        }
    }

    .store_content_main {
        display: flex;
        margin-top: 18px;
        font-size: 14px;

        .county,
        .num {
            max-width: 500px;
            margin-left: 70px;
        }
    }

    .store_content_text {
        margin-top: 18px;
        font-size: 14px;
    }
}

.merchan_main {
    display: flex;
    justify-content: space-between;

    .store_content {
        width: 30%;
        margin: 20px 20px 0 0;
    }

    .merchan_main_right {
        flex: 1;
        margin-top: 20px;

        .merchan_main_right_header {
            position: relative;
            display: flex;
            align-items: center;
            padding: 25px 0;
            background: #FFFFFF;

            .header_item {
                position: relative;
                font-weight: 600;
                font-size: 22px;
                color: #333;
                margin-left: 60px;
                cursor: pointer;
            }

            .header_item_line {
                position: absolute;
                width: 80px;
                height: 4px;
                bottom: -15px;
                left: calc(50% - 40px);
                background-color: #049B01;
                border-radius: 5px;
            }

            .header_item_active {
                color: #049B01;
            }
        }

        .merchan_main_right_content {
            position: relative;
            margin-top: 20px;
            border-radius: 5px;
            background-color: #fff;

            .check {
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                z-index: 999;
            }
        }
    }
}

.el-table {
    :deep(.el-table__row) {
        cursor: pointer;
    }
}

.upload_submit {
    position: absolute;
    box-sizing: border-box;
    right: 20px;
    width: 140px;
    height: 45px;
    font-size: 16px;
    border: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    color: #fff;
    background-color: #21A042;
}
</style>
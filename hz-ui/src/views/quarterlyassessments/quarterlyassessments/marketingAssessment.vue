<template>
    <div class="app-container">
        <div class="store_header">
            <div class="store_header_title">
                <div>{{ name }}</div>
            </div>
        </div>
        <div class="store_content">
            <div class="store_content_header">
                <div class="title_line"></div>
                <div class="title">基本信息</div>
            </div>
            <div class="store_content_main">
                <div>考核类型：{{ data.typeName }}</div>
                <div class="county">创建人：{{ data.createBy }}</div>
                <div class="num">创建时间：{{ data.createTime }}</div>
                <div class="num">考核题数：{{ data.topicCount }}</div>
                <div class="num">终端类型：{{ data.brandName }}</div>
            </div>
        </div>
        <div class="store_main">
            <div class="store_main_left">
                <div class="tabs">
                    <div class="tabs_item" :class="{ active: item.active }" v-for="(item, index) in tabs" :key="index"
                        @click="chooseTab(item, index)">{{
                        item.label }}</div>
                </div>
                <div class="store_main_content">
                    <questionType v-if="tabsValue == 0" @chooseQuestion="chooseQuestion"></questionType>
                    <div class="model" v-else>
                        <div class="model_item" v-for="(item, index) in modelList" :key="index"
                            @click="chooseModel(item)">
                            <el-icon>
                                <Document />
                            </el-icon>
                            {{ item.name }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="store_main_center">
                <questionContent :questionList="questionList" @chooseQuestionItem="chooseQuestionItem">
                </questionContent>
            </div>
            <div class="store_main_right" v-if="question.active">
                <questionEdit :question="question" @comoleteEdit="comoleteEdit" @editDelete="editDelete">
                </questionEdit>
            </div>
        </div>
        <div class="dialog-footer">
            <el-button class="upload_submit" @click="submitForm">确 定</el-button>
            <el-button class="upload_cancel" @click="cancel">取 消</el-button>
        </div>
    </div>
</template>
<script setup>
import cache from '@/plugins/cache'
import { topicBatch, replyByPaperId, listByTypeAndBrand } from "@/api/quarterlyassessments/quarterlyassessments"
import { ElMessage, ElMessageBox } from 'element-plus'
import questionType from './components/questionType.vue'
import questionContent from './components/questionContent.vue'
import questionEdit from './components/questionEdit.vue'
import { onMounted } from "vue"

const { proxy } = getCurrentInstance()
const data = cache.session.getJSON('onSiteData')
const id = data.id;
const name = data.name;
const type = data.type;
const brand = data.brand;
const tabs = ref([
    {
        label: '题型',
        value: 0,
        active: true
    },
    {
        label: '模板',
        value: 1,
        active: false
    }
])
const tabsValue = ref(0)
const questionList = ref([])
const modelList = ref([])
const question = ref({})
const questionIndex = ref(0)

onMounted(() => {
    getReplyByPaperId(id)
    getListByTypeAndBrand()
})
// 查询题型
const getReplyByPaperId = (id) => {
    const params = {
        paperId: id
    }
    replyByPaperId(params).then(res => {
        if (res.code == 200) {
            questionList.value = res.data
        }
    })
}
// 查询模板
const getListByTypeAndBrand = () => {
    const params = {
        type,
        brand
    }
    listByTypeAndBrand(params).then(res => {
        if (res.code == 200) {
            modelList.value = res.data.filter(el => el.id !== id)
        }
    })
}
// 题型
const chooseQuestion = (item, chir) => {
    const params = {
        active: false,
        title: chir.label,
        isRequired: 0,
        prompt: '',
        majorType: item.type,
        majorTypeName: item.label,
        smallType: chir.value,
        smallTypeName: chir.label,
        examinationReplyList: []
    }
    switch (chir.value) {
        case 'single_select':
            params.examinationReplyList = [
                { name: '选项一', score: 0 },
                { name: '选项二', score: 0 }
            ]
            break
        case 'multiple_select':
            params.examinationReplyList = [
                { name: '选项一', score: 0 },
                { name: '选项二', score: 0 },
                { name: '选项三', score: 0 },
                { name: '选项四', score: 0 }
            ]
            break
        case 'single_blank':
            params.examinationReplyList = [
                { name: '单项填空', score: 0 }
            ]
            break
        case 'multiple_blank':
            params.examinationReplyList = [
                { name: '多项填空1', score: 0 },
                { name: '多项填空2', score: 0 },
                { name: '多项填空3', score: 0 },
            ]
            break
        case 'notes_blank':
            params.examinationReplyList = [
                { name: '备注', score: 0 }
            ]
            break
        case 'single_image':
            params.examinationReplyList = [
                { name: '图片一', score: 0 }
            ]
            break
        case 'multiple_image':
            params.examinationReplyList = [
                { name: '图片一', score: 0 },
                { name: '图片二', score: 0 },
                { name: '图片三', score: 0 }
            ]
            break
    }
    questionList.value.push(params)
}
// 编辑题
const chooseQuestionItem = (index) => {
    questionList.value.forEach(el => {
        el.active = false
    });
    questionList.value[index].active = true
    question.value = questionList.value[index];
    questionIndex.value = index;
}
// 选择模板
const chooseModel = (item) => {
    ElMessageBox.confirm(
        '是否选择' + item.name + '模板？',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(() => {
            getReplyByPaperId(item.id)
            ElMessage({
                type: 'success',
                message: '成功',
            })
        })
}
// 选择题型模板
const chooseTab = (item, index) => {
    tabs.value.forEach(el => {
        el.active = false
    });
    item.active = true
    tabsValue.value = item.value
}
// 保存当前题
const comoleteEdit = (item) => {
    ElMessageBox.confirm(
        '是否确认保存这道题?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(() => {
            const index = questionIndex.value;
            questionList.value[index] = item;
            ElMessage({
                type: 'success',
                message: '保存成功',
            })
        })
}
// 删除当前题
const editDelete = (item) => {
    ElMessageBox.confirm(
        '是否确认删除 ' + (item.title) + ' 这道题?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(() => {
            const index = questionIndex.value;
            questionList.value.splice(index, 1)
            question.value = {};
            ElMessage({
                type: 'success',
                message: '删除成功',
            })
        })
}

const submitForm = () => {
    if (!questionList.value.length) {
        return ElMessage({
            type: 'warning',
            message: '请先添加题型！',
        })
    }
    for (let i = 0; i < questionList.value.length; i++) {
        questionList.value[i].num = i + 1
        questionList.value[i].sort = i + 1
        for (let k = 0; k < questionList.value[i].examinationReplyList.length; k++) {
            questionList.value[i].examinationReplyList[k].topicNum = k + 1
            questionList.value[i].examinationReplyList[k].sort = k + 1
        }
    }
    topicBatch(id, questionList.value).then(res => {
        if (res.code == 200) {
            ElMessage({
                type: 'success',
                message: '保存成功！',
            })
            cancel()
        }
    })
}

const cancel = () => {
    const params = {
        path: '/assessment/quarterlyassessments/childs/onSiteAssessment'
    }
    proxy.$tab.closeOpenPage(params)
}
</script>


<style lang="scss" scoped>
.app-container {
    background-color: #f4f8ff;
    color: #333333;
}

.store_header {
    box-sizing: border-box;
    padding: 20px 30px;
    background-color: #fff;
    border-radius: 5px;

    .store_header_title {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        display: flex;
        align-items: center;
    }
}

.store_content {
    box-sizing: border-box;
    padding: 30px;
    background-color: #fff;
    border-radius: 5px;
    margin-top: 10px;
    color: #333333;

    .store_content_header {
        display: flex;
        align-items: center;

        .title_line {
            width: 3px;
            height: 19px;
            background: #21A042;
            border-radius: 2px;
        }

        .title {
            font-size: 20px;
            font-weight: bold;
            margin-left: 10px;
        }
    }

    .store_content_name {
        font-weight: 600;
        font-size: 20px;
        margin-top: 20px;
    }

    .store_content_main {
        display: flex;
        align-items: center;
        margin-top: 18px;

        .county,
        .num {
            margin-left: 70px;
        }
    }

    .store_content_text {
        margin-top: 18px;
    }
}

.status_text {
    display: flex;
    align-items: center;
    justify-content: center;

    .icon {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
    }
}

.store_main {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;

    .store_main_left {
        width: 19%;
        padding: 20px 15px;
        background-color: #fff;

        .tabs {
            display: flex;
            justify-content: space-between;
            padding: 6px 40px;
            background: #FAFAFA;
            border: 1px solid #EEEEEE;
            border-radius: 5px;

            .tabs_item {
                padding: 4px 20px;
                font-weight: 600;
                font-size: 22px;
                color: #333;
                border-radius: 5px;
                letter-spacing: 1px;
                cursor: pointer;
            }

            .active {
                color: #FFFFFF;
                background-color: #049B01;
            }
        }
    }

    .store_main_center {
        flex: 1;
        margin-left: 15px;
        background-color: #fff;
    }

    .store_main_right {
        width: 23%;
        padding: 20px 25px;
        margin-left: 15px;
        background-color: #fff;
    }
}

.dialog-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 30px;
    background-color: #fff;
    margin-top: 10px;

    .upload_submit,
    .upload_cancel {
        box-sizing: border-box;
        width: 140px;
        height: 45px;
        font-size: 16px;
        border: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
    }

    .upload_submit {
        color: #fff;
        background-color: #21A042;
        margin-right: 50px;
    }

    .upload_cancel {
        border: 1px solid #21A042;
        color: #21A042;
    }
}

.model {
    .model_item {
        display: flex;
        align-items: center;
        padding: 20px 0;
        color: #333333;
        font-weight: 600;
        cursor: pointer;
        border-bottom: 1px solid #F5F5F5;

        .el-icon {
            color: #049B01;
            margin-right: 8px;
        }
    }
}
</style>
<template>
    <div class="app-container">
        <div class="store_header">
            <div class="store_header_title">
                <div>{{ name }}</div>
            </div>
        </div>
        <div class="store_content">
            <div class="store_content_header">
                <div class="title_line"></div>
                <div class="title">考核人员</div>
            </div>
            <el-form class="store_content_form" ref="quarterlyassessmentsRef" :model="form" label-width="auto"
                :rules="rules">
                <el-row>
                    <el-col :span="10">
                        <el-form-item label="组长" prop="leader">
                            <el-select v-model="form.leader" placeholder="请选择组长" style="width: 326px">
                                <el-option v-for="user in leaderUserList" :key="user.id"
                                    :label="user.nickName ? `${user.name}(${user.nickName})` : user.name"
                                    :value="user.id" />
                            </el-select>
                            <div class="extract" @click="submitSelectLeader">抽取</div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="14">
                        <el-form-item label="监督人员" prop="quarter">
                            <el-select v-model="form.supervisorStaff" placeholder="请选择监督人员" multiple :multiple-limit="2"
                                style="width: 326px">
                                <el-option v-for="user in supervisorUserList" :key="user.id"
                                    :label="user.nickName ? `${user.name}(${user.nickName})` : user.name"
                                    :value="user.id" />
                            </el-select>
                            <div class="extract" @click="submitSelectSupervisor">抽取</div>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div class="store_content">
            <div class="store_content_header">
                <div class="title_line"></div>
                <div class="title">现场考核项</div>
            </div>
            <div class="from_container" style="padding: 0;">
                <el-table :data="monthlyAssessmentsList" border style="width: 100%" max-height="300"
                    :row-class-name="tableRowClassName">
                    <el-table-column label="名称" prop="name" width="250" align="center" />
                    <el-table-column label="考核类型" prop="type" width="100" align="center">
                        <template #default="scope">
                            <el-tag type="primary" size="small">{{ scope.row.typeName }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="终端类型" prop="brandName" align="center" />
                    <el-table-column label="总题数" prop="topicCount" align="center" />
                    <el-table-column label="创建人" prop="createBy" align="center" />
                    <el-table-column label="创建时间" prop="createTime" align="center" />
                    <el-table-column label="操作" prop="assessmentType" width="300" align="center">
                        <template #default="scope">
                            <div class="table-btn">
                                <el-button class="table-btn-edit" link @click="handleUpdate(scope.row)">修改</el-button>
                                <div class="btn-line"></div>
                                <el-button class="table-btn-delete" link @click="handleDelete(scope.row)">禁用</el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <div class="dialog-footer">
            <el-button class="upload_submit" @click="submitForm">确 定</el-button>
            <el-button class="upload_cancel" @click="cancel">取 消</el-button>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue'
import cache from '@/plugins/cache'
import { listUser } from "@/api/system/user"
import { paperMonthlyId, saveSelectedStaffAndMerchants, getQuarterlyassessments } from "@/api/quarterlyassessments/quarterlyassessments"
import { allocatedUserList } from "@/api/system/role"
import { selectRandomLeaders, selectRandomSupervisors } from "@/api/quarterlyassessments/monthlyStaff"

const router = useRouter()
const { proxy } = getCurrentInstance()
const data = cache.session.getJSON('configData')
const monthlyId = data.id;
const year = data.year;
const month = data.month;
const name = data.name;
const quarterId = data.quarterId;
const form = ref({})
const rules = ref({})
const userList = ref([])
const leaderUserList = ref([])
const supervisorUserList = ref([])
const monthlyAssessmentsList = ref([])

onMounted(() => {
    paperMonthlyId(monthlyId).then(response => {
        monthlyAssessmentsList.value = response.data
    })
    loadMonthlyAssessmentData()
    loadUserLists()
})


/** 异步加载用户列表 */
async function loadUserLists() {
    try {
        // 先获取所有用户列表作为兜底
        await getUserList()
        // 然后获取各角色的用户列表
        await getAllRoleUserLists()
    } catch (error) {
        console.error('加载用户列表失败:', error)
        proxy.$modal.msgError("加载用户列表失败，请刷新重试")
    }
}

/** 获取用户列表 */
function getUserList() {
    // 获取所有用户作为兜底
    const queryParams = {
        pageNum: 1,
        pageSize: 1000,
        status: '0'
    }
    return listUser(queryParams).then(response => {
        userList.value = response.rows.map(user => ({
            id: user.userId,
            name: user.userName,
            nickName: user.nickName
        }))
        return userList.value
    }).catch(error => {
        console.error('获取用户列表失败:', error)
        proxy.$modal.msgError("获取用户列表失败")
        return []
    })
}
/** 加载已保存的月度考核数据 */
async function loadMonthlyAssessmentData() {
    try {
        // 从返回的数据中获取当前月度考核的staffMap信息
        // 由于configuration页面已经显示了staffMap数据，我们可以从configuration API获取
        const quarterlyResponse = await getQuarterlyassessments(quarterId)
        if (quarterlyResponse.code === 200 && quarterlyResponse.data && quarterlyResponse.data.monthlyAssessmentsList) {
            // 找到当前月度考核的数据
            const currentMonthlyAssessment = quarterlyResponse.data.monthlyAssessmentsList[0]
            if (currentMonthlyAssessment && currentMonthlyAssessment.staffMap) {
                const staffMapId = currentMonthlyAssessment.staffMapId
                form.value.leader = parseFloat(staffMapId['组长']) || ''
                const arr = staffMapId['监督人员'].split(",") || []
                form.value.supervisorStaff = arr.map(Number)
            }
        }
    } catch (error) {
        console.error('加载月度考核数据失败:', error)
        // 不显示错误提示，因为可能是首次创建，没有保存的数据
    }
}
/** 获取所有角色的用户列表 */
async function getAllRoleUserLists() {
    try {
        // 根据系统中定义的角色ID获取用户列表
        // 这些ID需要根据实际系统中的角色ID调整
        const [leaders, supervisors] = await Promise.all([
            getUserListByRole(102), // 组长角色ID
            getUserListByRole(103), // 监督人员角色ID  
        ])

        leaderUserList.value = leaders
        supervisorUserList.value = supervisors

        // 如果某个角色没有用户，则使用所有用户作为兜底
        if (leaderUserList.value.length === 0) leaderUserList.value = userList.value
        if (supervisorUserList.value.length === 0) supervisorUserList.value = userList.value

    } catch (error) {
        console.error('获取角色用户列表失败:', error)
        // 出错时使用所有用户作为兜底
        leaderUserList.value = userList.value
        supervisorUserList.value = userList.value
    }
}
/** 根据角色获取用户列表 */
function getUserListByRole(roleId) {
    return allocatedUserList({
        roleId: roleId,
        pageNum: 1,
        pageSize: 1000
    }).then(response => {
        return response.rows.map(user => ({
            id: user.userId,
            name: user.userName,
            nickName: user.nickName
        }))
    }).catch(error => {
        console.error(`获取角色${roleId}的用户列表失败:`, error)
        return []
    })
}
// table样式
const tableRowClassName = ({ row, rowIndex }) => {
    if ((rowIndex + 1) % 2 === 0) {
        return 'table-row'
    }
    return ''
}
/** 提交抽取组长表单 */
function submitSelectLeader() {
    const data = {
        monthlyAssessmentId: monthlyId, // 确保是数字类型
        year: year, // 确保只是年份字符串
        count: 1 // 固定抽取1人
    }
    selectRandomLeaders(data).then(response => {
        if (response.code === 200 && response.data && response.data.leaders && response.data.leaders.length > 0) {
            // 获取抽取的组长
            const leader = response.data.leaders[0]
            // 更新表单中的组长
            form.value.leader = leader.userName

            proxy.$modal.msgSuccess(`成功抽取组长: ${leader.userName}${leader.nickName ? '(' + leader.nickName + ')' : ''}`)
        } else {
            proxy.$modal.msgWarning("未能抽取到符合条件的组长，请手动选择")
        }
    })
}
/** 提交抽取监督人员表单 */
function submitSelectSupervisor() {
    const data = {
        monthlyAssessmentId: monthlyId, // 确保是数字类型
        year: year, // 确保只是年份字符串
        month: month,
        count: 2 // 固定抽取1人
    }
    selectRandomSupervisors(data).then(response => {
        if (response.code === 200 && response.data && response.data.supervisors && response.data.supervisors.length > 0) {
            // 获取抽取的监督人员
            const list = response.data.supervisors
            form.value.supervisorStaff = []
            list.forEach(el => {
                form.value.supervisorStaff.push(el.userName)
            })
            proxy.$modal.msgSuccess('成功抽取监督人员')
        } else {
            proxy.$modal.msgWarning("未能抽取到符合条件的监督人员，请手动选择")
        }
    })
}
// 跳转题库
const handleUpdate = (row) => {
    cache.session.setJSON('onSiteData', row)
    router.push({
        path: "/assessment/quarterlyassessments/childs/marketingAssessment"
    })
}
const submitForm = () => {
    // 准备人员数据 - 需要转换为后端期望的格式
    const leaders = []
    const supervisors = []

    // 处理组长
    if (form.value.leader) {
        const leaderUser = leaderUserList.value.find(user => user.id === form.value.leader)
        if (leaderUser) {
            leaders.push({
                userId: leaderUser.id,
                userName: leaderUser.name,
                nickName: leaderUser.nickName
            })
        }
    }

    // 处理监督人员
    if (form.value.supervisorStaff.length) {
        const list = form.value.supervisorStaff
        supervisorUserList.value.forEach(el => {
            list.forEach(chir => {
                if (el.id === chir) {
                    supervisors.push({
                        userId: el.id,
                        userName: el.name,
                        nickName: el.nickName
                    })
                }
            })
        })
    }

    const staffData = {
        monthlyAssessmentId: parseInt(monthlyId),
        year: year,
        month: parseInt(month),
        leaders: leaders,
        supervisors: supervisors
    }

    const submitData = {
        staffData
    }
    saveSelectedStaffAndMerchants(submitData).then(response => {
        if (response.code === 200) {
            proxy.$modal.msgSuccess("保存成功")
            cancel()
        } else {
            proxy.$modal.msgError(response.msg || "保存失败")
        }
    }).catch(error => {
        console.error('保存失败:', error)
        proxy.$modal.msgError("保存失败，请重试")
    })
}

const cancel = () => {
    const params = {
        path: "/assessment/quarterlyassessments/childs/configuration",
        query: {
            id: quarterId
        }
    }
    proxy.$tab.closeOpenPage(params)
}
</script>

<style lang="scss" scoped>
.app-container {
    background-color: #f4f8ff;
    color: #333333;
}

.store_header {
    box-sizing: border-box;
    padding: 20px 30px;
    background-color: #fff;
    border-radius: 5px;

    .store_header_title {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        display: flex;
        align-items: center;
    }
}

.store_content {
    box-sizing: border-box;
    padding: 20px 30px;
    background-color: #fff;
    border-radius: 5px;
    margin-top: 10px;

    .store_content_header {
        display: flex;
        align-items: center;

        .title_line {
            width: 3px;
            height: 19px;
            background: #21A042;
            border-radius: 2px;
        }

        .title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-left: 10px;
        }
    }

    .store_content_form {
        margin-top: 25px;
    }
}

.extract {
    width: 82px;
    line-height: 33px;
    color: #fff;
    font-size: 16px;
    text-align: center;
    background: #21A042;
    border-radius: 5px;
    margin-left: 10px;
    cursor: pointer;
}

.dialog-footer {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 20px 30px;
    background-color: #fff;
    margin-top: 10px;

    .upload_submit,
    .upload_cancel {
        box-sizing: border-box;
        width: 140px;
        height: 45px;
        font-size: 16px;
        border: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
    }

    .upload_submit {
        color: #fff;
        background-color: #21A042;
    }

    .upload_cancel {
        border: 1px solid #21A042;
        color: #21A042;
    }
}

.from_container {
    min-height: 120px;
}
</style>
<template>
    <div class="app-container">
        <div class="store_header">
            <div class="store_header_title">
                <div>{{ name }}</div>
            </div>
        </div>
        <div class="store_content">
            <div class="store_content_header">
                <div class="title_line"></div>
                <div class="title">基本信息</div>
            </div>
            <div class="store_content_name">
                2025年7月份县域现场考核详情
            </div>
            <div class="store_content_main">
                <div>考核时间：2025.03.15 15:32:21</div>
                <div class="county">考核县域：全县区</div>
                <div class="num">检查户数：32</div>
            </div>
            <div class="store_content_text">
                考核人员：曹雪阳（组长）、李承恩（专卖人员）、曲云（监督人员）
            </div>
        </div>
        <div class="store_content">
            <div class="store_content_header">
                <div class="title_line"></div>
                <div class="title">考核信息</div>
            </div>
            <div class="from_container" style="min-height: 100%;padding: 0;">
                <el-table :data="monthlyAssessmentsList" border style="width: 100%" max-height="400"
                    :row-class-name="tableRowClassName">
                    <el-table-column label="县域名称" prop="name" align="center" />
                    <el-table-column label="考核商户数量" prop="num" align="center" />
                    <el-table-column label="专卖现场考核综合评分" prop="score" align="center" />
                    <el-table-column label="营销现场考核综合评分" prop="score1" align="center" />
                    <el-table-column label="状态" align="center">
                        <template #default="scope">
                            <div class="status_text"
                                :style="{ color: scope.row.status == '未考核' ? '#0083EA' : '#049B01' }">
                                <div class="icon"
                                    :style="{ background: scope.row.status == '未考核' ? '#0083EA' : '#049B01' }">
                                </div>
                                <div>{{ scope.row.status }}</div>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <div class="dialog-footer">
            <el-button class="upload_submit" @click="submitForm">确 定</el-button>
            <el-button class="upload_cancel" @click="cancel">取 消</el-button>
        </div>
    </div>
</template>
<script setup>
import cache from '@/plugins/cache'
const { proxy } = getCurrentInstance()
const data = cache.session.getJSON('configData')
const name = data.name;
const id = data.id;
const quarterId = data.quarterId;
const monthlyAssessmentsList = ref([
    {
        name: '华龙区',
        num: '30',
        score: '0',
        score1: '0',
        status: '未考核',
    },
    {
        name: '清丰县',
        num: '30',
        score: '0',
        score1: '0',
        status: '未考核',
    },
    {
        name: '南乐县',
        num: '30',
        score: '10',
        score1: '-10',
        status: '已考核',
    },
    {
        name: '范县',
        num: '30',
        score: '12',
        score1: '-10',
        status: '已考核',
    },
    {
        name: '濮阳县',
        num: '30',
        score: '0',
        score1: '0',
        status: '未考核',
    }
])
// table样式
const tableRowClassName = ({ row, rowIndex }) => {
    if ((rowIndex + 1) % 2 === 0) {
        return 'table-row'
    }
    return ''
}

const submitForm = () => {
    cancel()
}

const cancel = () => {
    const params = {
        path: "/assessment/quarterlyassessments/childs/configuration",
        query: {
            id: quarterId
        }
    }
    proxy.$tab.closeOpenPage(params)
}
</script>
<style lang="scss" scoped>
.app-container {
    background-color: #f4f8ff;
    color: #333333;
}

.store_header {
    box-sizing: border-box;
    padding: 20px 30px;
    background-color: #fff;
    border-radius: 5px;

    .store_header_title {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        display: flex;
        align-items: center;
    }
}

.status_text {
    display: flex;
    align-items: center;
    justify-content: center;

    .icon {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
    }
}

.store_content {
    box-sizing: border-box;
    padding: 35px 30px;
    background-color: #fff;
    border-radius: 5px;
    margin-top: 10px;
    color: #333333;

    .store_content_header {
        display: flex;
        align-items: center;

        .title_line {
            width: 3px;
            height: 19px;
            background: #21A042;
            border-radius: 2px;
        }

        .title {
            font-size: 20px;
            font-weight: bold;
            margin-left: 10px;
        }
    }

    .store_content_name {
        font-weight: 600;
        font-size: 20px;
        margin-top: 20px;
    }

    .store_content_main {
        display: flex;
        align-items: center;
        margin-top: 18px;

        .county,
        .num {
            margin-left: 70px;
        }
    }

    .store_content_text {
        margin-top: 18px;
    }
}

.dialog-footer {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 20px 30px;
    background-color: #fff;
    margin-top: 10px;

    .upload_submit,
    .upload_cancel {
        box-sizing: border-box;
        width: 140px;
        height: 45px;
        font-size: 16px;
        border: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
    }

    .upload_submit {
        color: #fff;
        background-color: #21A042;
    }

    .upload_cancel {
        border: 1px solid #21A042;
        color: #21A042;
    }
}
</style>
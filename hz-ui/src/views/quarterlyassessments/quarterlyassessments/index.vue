<template>
  <div class="app-container">
    <div class="from_header">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="考核名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入关键字搜索" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="所属年份" prop="year">
          <el-input v-model="queryParams.year" placeholder="请输入所属年份" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="所属季度" prop="quarter">
          <el-select v-model="queryParams.quarter" placeholder="请选择季度" clearable>
            <el-option label="第一季度" value="1" />
            <el-option label="第二季度" value="2" />
            <el-option label="第三季度" value="3" />
            <el-option label="第四季度" value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="已提交" value="1" />
            <el-option label="进行中" value="2" />
            <el-option label="未完成" value="3" />
            <el-option label="已完成" value="4" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button class="btn-search" icon="Search" @click="handleQuery">查询</el-button>
          <el-button class="btn-refresh" icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="from_container">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button class="btn-add" plain icon="Plus" @click="handleAdd"
            v-hasPermi="['quarterlyassessments:quarterlyassessments:add']">新增</el-button>
        </el-col>
        <!-- <el-col :span="1.5">
          <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
            v-hasPermi="['quarterlyassessments:quarterlyassessments:edit']">修改</el-button>
        </el-col> -->
        <el-col :span="1.5">
          <el-button class="btn-delete" plain icon="Delete" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['quarterlyassessments:quarterlyassessments:remove']">删除</el-button>
        </el-col>
        <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['quarterlyassessments:quarterlyassessments:export']"
        >导出</el-button>
      </el-col> -->
        <right-toolbar :showSearch="showSearch" @queryTable="getList"
          @update:showSearch="showSearch = $event"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="quarterlyassessmentsList" @selection-change="handleSelectionChange"
        :row-class-name="tableRowClassName">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="考核名称" align="center" prop="name" />
        <el-table-column label="所属年份" align="center" prop="year" />
        <el-table-column label="所属季度" align="center" prop="quarter">
          <template #default="scope">
            <span>第{{ scope.row.quarter }}季度</span>
          </template>
        </el-table-column>
        <el-table-column label="考核数量" align="center" prop="assessmentCount" />
        <el-table-column label="状态" align="center" prop="statusText" />
        <el-table-column label="计划指定人" align="center" prop="createBy" />
        <!-- <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag v-if="scope.row.status === '0'" type="info">待开始</el-tag>
          <el-tag v-else-if="scope.row.status === '1'" type="warning">进行中</el-tag>
          <el-tag v-else-if="scope.row.status === '2'" type="success">已完成</el-tag>
          <el-tag v-else type="danger">未知</el-tag>
        </template>
      </el-table-column> -->
        <!-- <el-table-column label="计划指定人" align="center" prop="plannerUserId" /> -->
        <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <div class="table-btn">
              <!-- <el-button class="table-btn-edit" link type="primary" @click="handleView(scope.row)"
                v-hasPermi="['quarterlyassessments:quarterlyassessments:query']">查看</el-button>
              <div class="btn-line"></div> -->
              <el-button class="table-btn-edit" link type="primary" @click="handleUpdate(scope.row)"
                v-hasPermi="['quarterlyassessments:quarterlyassessments:edit']">配置</el-button>
              <div class="btn-line"></div>
              <el-button class="table-btn-delete" link type="primary" @click="handleDelete(scope.row)"
                v-hasPermi="['quarterlyassessments:quarterlyassessments:remove']">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total>0" :total="total" :page="queryParams.pageNum" :limit="queryParams.pageSize"
        @pagination="getList" @update:page="queryParams.pageNum = $event"
        @update:limit="queryParams.pageSize = $event" />
    </div>
    <!-- 添加或修改季度考核对话框 -->
    <el-dialog :title="title" v-model="open" width="1200px" append-to-body>
      <el-form ref="quarterlyassessmentsRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="考核名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入考核名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属年份" prop="year">
              <el-date-picker v-model="form.year" type="year" placeholder="请选择年份" value-format="YYYY"
                style="width: 100%" @change="handleYearQuarterChange" :disabled-date="disabledDate" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属季度" prop="quarter">
              <el-select v-model="form.quarter" placeholder="请选择季度" style="width: 100%"
                @change="handleYearQuarterChange">
                <el-option label="第一季度" :value="1" />
                <el-option label="第二季度" :value="2" />
                <el-option label="第三季度" :value="3" />
                <el-option label="第四季度" :value="4" />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="待开始" value="0" />
                <el-option label="进行中" value="1" />
                <el-option label="已完成" value="2" />
              </el-select>
            </el-form-item>
          </el-col> -->
        </el-row>
        <!-- <el-row>
          <el-col :span="24">
            <el-form-item label="计划指定人" prop="plannerUserId">
              <el-input v-model="form.plannerUserId" placeholder="请输入计划指定人ID" />
            </el-form-item>
          </el-col>
        </el-row> -->

        <!-- 月度考核预览 -->
        <!-- <el-divider content-position="left">月度考核预览</el-divider>
        <el-alert
          v-if="!form.id"
          title="提示"
          type="info"
          :closable="false"
          show-icon
          style="margin-bottom: 15px"
        >
          <template #default>
            选择年份和季度后，系统将自动生成该季度对应的三个月度考核记录预览。您可以在此编辑月度考核信息，点击确定后将一并保存。
          </template>
        </el-alert> -->

        <el-tabs v-model="activeTab" type="border-card" style="margin-bottom: 20px"
          v-if="monthlyAssessmentsList.length > 0">
          <!-- 现场考核页签 -->
          <el-tab-pane label="现场考核" name="onsite">
            <el-table :data="monthlyAssessmentsList" border style="width: 100%" max-height="300">
              <el-table-column label="序号" type="index" width="60" align="center" />
              <el-table-column label="月度考核名称" prop="name" min-width="200">
                <template #default="scope">
                  <el-input v-model="scope.row.name" placeholder="请输入月度考核名称" size="small" />
                </template>
              </el-table-column>
              <el-table-column label="所属月份" prop="month" width="100" align="center">
                <template #default="scope">
                  <span>{{ scope.row.month }}月</span>
                </template>
              </el-table-column>
              <el-table-column label="考核类型" prop="assessmentType" width="120" align="center">
                <template #default>
                  <el-tag type="primary" size="small">现场</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <!-- 目标考核页签 -->
          <el-tab-pane label="目标考核" name="target">
            <el-empty description="目标考核功能开发中，敬请期待" :image-size="100">
              <template #image>
                <el-icon size="100" color="#409EFF">
                  <Document />
                </el-icon>
              </template>
            </el-empty>
          </el-tab-pane>

          <!-- 汇总页签 -->
          <el-tab-pane label="汇总" name="summary">
            <el-empty description="汇总功能开发中，敬请期待" :image-size="100">
              <template #image>
                <el-icon size="100" color="#67C23A">
                  <DataAnalysis />
                </el-icon>
              </template>
            </el-empty>
          </el-tab-pane>
        </el-tabs>

        <el-empty v-if="monthlyAssessmentsList.length === 0 && form.year && form.quarter"
          description="请选择年份和季度以生成月度考核预览" :image-size="80" />
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看季度考核详情对话框 -->
    <el-dialog title="季度考核详情" v-model="viewOpen" width="1400px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="考核名称">{{ viewForm.name }}</el-descriptions-item>
        <el-descriptions-item label="所属年份">{{ viewForm.year }}</el-descriptions-item>
        <el-descriptions-item label="所属季度">第{{ viewForm.quarter }}季度</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag v-if="viewForm.status === '0'" type="primary">已提交</el-tag>
          <el-tag v-else-if="viewForm.status === '1'" type="primary">已提交</el-tag>
          <el-tag v-else-if="viewForm.status === '2'" type="warning">进行中</el-tag>
          <el-tag v-else-if="viewForm.status === '3'" type="danger">未完成</el-tag>
          <el-tag v-else-if="viewForm.status === '4'" type="success">已完成</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="计划指定人">{{ viewForm.plannerUserId }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(viewForm.createdAt, '{y}-{m}-{d} {h}:{i}:{s}')
          }}</el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="left">月度考核信息</el-divider>
      <el-tabs v-model="viewActiveTab" type="border-card"
        v-if="viewForm.monthlyAssessmentsList && viewForm.monthlyAssessmentsList.length > 0">
        <!-- 现场考核页签 -->
        <el-tab-pane label="现场考核" name="onsite">
          <el-table :data="viewForm.monthlyAssessmentsList" border style="width: 100%">
            <el-table-column label="序号" type="index" width="60" align="center" />
            <el-table-column label="月度考核名称" prop="name" min-width="180" />
            <el-table-column label="所属月份" prop="month" width="80" align="center">
              <template #default="scope">
                <span>{{ scope.row.month }}月</span>
              </template>
            </el-table-column>
            <el-table-column label="考核类型" prop="assessmentType" width="80" align="center">
              <template #default="scope">
                <el-tag type="primary" size="small">{{ scope.row.assessmentType || '现场' }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="状态" prop="status" width="80" align="center">
              <template #default="scope">
                <el-tag v-if="scope.row.status === '0'" type="primary" size="small">已提交</el-tag>
                <el-tag v-else-if="scope.row.status === '1'" type="primary" size="small">已提交</el-tag>
                <el-tag v-else-if="scope.row.status === '2'" type="warning" size="small">进行中</el-tag>
                <el-tag v-else-if="scope.row.status === '3'" type="danger" size="small">未完成</el-tag>
                <el-tag v-else-if="scope.row.status === '4'" type="success" size="small">已完成</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="检查户数" prop="inspectedHouseholds" width="80" align="center">
              <template #default="scope">
                <span>{{ scope.row.inspectedHouseholds || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="地区" prop="district" width="100" align="center">
              <template #default="scope">
                <span>{{ scope.row.district || '-' }}</span>
              </template>
            </el-table-column>
            <!-- 人员字段 - 使用中间表数据 -->
            <el-table-column label="组长" width="100" align="center">
              <template #default="scope">
                {{ getStaffByRole(scope.row.staffList, '组长') || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="专卖人员" width="100" align="center">
              <template #default="scope">
                {{ getStaffByRole(scope.row.staffList, '专卖人员') || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="营销人员" width="100" align="center">
              <template #default="scope">
                {{ getStaffByRole(scope.row.staffList, '营销人员') || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="监督人员" width="100" align="center">
              <template #default="scope">
                {{ getStaffByRole(scope.row.staffList, '监督人员') || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="createdAt" width="100" align="center">
              <template #default="scope">
                <span>{{ parseTime(scope.row.createdAt, '{m}-{d}') || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="scope">
                <el-button link type="primary" size="small" @click="handleMonthlyView(scope.row)">查看</el-button>
                <el-button link type="primary" size="small" @click="handleMonthlyEdit(scope.row)">修改</el-button>
                <el-button link type="success" size="small" @click="handleMonthlyPublish(scope.row)">发布</el-button>
                <el-button link type="danger" size="small" @click="handleMonthlyDisable(scope.row)">禁用</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 目标考核页签 -->
        <el-tab-pane label="目标考核" name="target">
          <el-empty description="目标考核功能开发中，敬请期待" :image-size="100">
            <template #image>
              <el-icon size="100" color="#409EFF">
                <Document />
              </el-icon>
            </template>
          </el-empty>
        </el-tab-pane>

        <!-- 汇总页签 -->
        <el-tab-pane label="汇总" name="summary">
          <el-empty description="汇总功能开发中，敬请期待" :image-size="100">
            <template #image>
              <el-icon size="100" color="#67C23A">
                <DataAnalysis />
              </el-icon>
            </template>
          </el-empty>
        </el-tab-pane>
      </el-tabs>

      <el-empty v-if="!viewForm.monthlyAssessmentsList || viewForm.monthlyAssessmentsList.length === 0"
        description="暂无月度考核数据" :image-size="80" />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改月度考核对话框 -->
    <el-dialog title="修改月度考核人员信息" v-model="monthlyEditOpen" width="600px" append-to-body>
      <el-form ref="monthlyEditRef" :model="monthlyEditForm" :rules="monthlyEditRules" label-width="100px">
        <el-form-item label="考核名称">
          <el-input v-model="monthlyEditForm.name" disabled />
        </el-form-item>
        <el-form-item label="所属月份">
          <el-input :value="monthlyEditForm.month + '月'" disabled />
        </el-form-item>
        <el-form-item label="组长" prop="leader">
          <div class="input-with-button">
            <el-select v-model="monthlyEditForm.leader" placeholder="请选择组长" clearable filterable style="width: 100%">
              <el-option v-for="user in leaderUserList" :key="user.id"
                :label="user.nickName ? `${user.name}(${user.nickName})` : user.name" :value="user.name" />
            </el-select>
            <el-button type="primary" @click="handleSelectLeader">抽取组长</el-button>
          </div>
        </el-form-item>
        <el-form-item label="专卖人员" prop="monopolyStaff">
          <el-select v-model="monthlyEditForm.monopolyStaff" placeholder="请选择专卖人员" style="width: 100%" clearable
            filterable>
            <el-option v-for="user in monopolyUserList" :key="user.id"
              :label="user.nickName ? `${user.name}(${user.nickName})` : user.name" :value="user.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="营销人员" prop="marketingStaff">
          <el-select v-model="monthlyEditForm.marketingStaff" placeholder="请选择营销人员" style="width: 100%" clearable
            filterable>
            <el-option v-for="user in marketingUserList" :key="user.id"
              :label="user.nickName ? `${user.name}(${user.nickName})` : user.name" :value="user.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="监督人员" prop="supervisorStaff">
          <div class="input-with-button">
            <el-select v-model="monthlyEditForm.supervisorStaff" placeholder="请选择监督人员" clearable filterable
              style="width: 100%">
              <el-option v-for="user in supervisorUserList" :key="user.id"
                :label="user.nickName ? `${user.name}(${user.nickName})` : user.name" :value="user.name" />
            </el-select>
            <el-button type="primary" @click="handleSelectSupervisor">抽取监督人员</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitMonthlyEditForm">确 定</el-button>
          <el-button @click="monthlyEditOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 抽取组长对话框 -->
    <el-dialog title="抽取组长" v-model="selectLeaderOpen" width="400px" append-to-body>
      <div style="text-align: center; padding: 20px;">
        <p>确认要随机抽取1名组长吗？</p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitSelectLeader">确定抽取</el-button>
          <el-button @click="selectLeaderOpen = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 抽取监督人员对话框 -->
    <el-dialog title="抽取监督人员" v-model="selectSupervisorOpen" width="400px" append-to-body>
      <div style="text-align: center; padding: 20px;">
        <p>确认要随机抽取1名监督人员吗？</p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitSelectSupervisor">确定抽取</el-button>
          <el-button @click="selectSupervisorOpen = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看月度考核对话框 -->
    <el-dialog title="月度考核详情" v-model="monthlyViewOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="考核名称">{{ monthlyViewForm.name }}</el-descriptions-item>
        <el-descriptions-item label="所属月份">{{ monthlyViewForm.month }}月</el-descriptions-item>
        <el-descriptions-item label="地区">{{ monthlyViewForm.district || '-' }}</el-descriptions-item>
        <el-descriptions-item label="检查户数">{{ monthlyViewForm.inspectedHouseholds || '-' }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag v-if="monthlyViewForm.status === '0'" type="info" size="small">待开始</el-tag>
          <el-tag v-else-if="monthlyViewForm.status === '1'" type="warning" size="small">进行中</el-tag>
          <el-tag v-else-if="monthlyViewForm.status === '2'" type="success" size="small">已完成</el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="考核类型">
          <el-tag type="primary" size="small">现场</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="组长">{{ monthlyViewForm.leader || '-' }}</el-descriptions-item>
        <el-descriptions-item label="专卖人员">{{ monthlyViewForm.monopolyStaff || '-' }}</el-descriptions-item>
        <el-descriptions-item label="营销人员">{{ monthlyViewForm.marketingStaff || '-' }}</el-descriptions-item>
        <el-descriptions-item label="监督人员">{{ monthlyViewForm.supervisorStaff || '-' }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="monthlyViewOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Quarterlyassessments">
import { listQuarterlyassessments, getQuarterlyassessments, delQuarterlyassessments, addQuarterlyassessments, updateQuarterlyassessments, updateMonthlyAssessmentStaff } from "@/api/quarterlyassessments/quarterlyassessments"
import { listUser } from "@/api/system/user"
import { allocatedUserList } from "@/api/system/role"
import { Document, DataAnalysis } from '@element-plus/icons-vue'
import { selectRandomLeaders, selectRandomSupervisors, saveSelectedStaff } from "@/api/quarterlyassessments/monthlyStaff"
const router = useRouter()
const { proxy } = getCurrentInstance()

const quarterlyassessmentsList = ref([])
const monthlyAssessmentsList = ref([])
const userList = ref([])
const leaderUserList = ref([])
const monopolyUserList = ref([])
const marketingUserList = ref([])
const supervisorUserList = ref([])
const open = ref(false)
const viewOpen = ref(false)
const monthlyEditOpen = ref(false)
const monthlyViewOpen = ref(false)
const selectLeaderOpen = ref(false)
const selectSupervisorOpen = ref(false)

// 用于跟踪抽取的人员，以便最终保存时记录历史
const selectedStaffFromDraw = ref({
  leaders: [],
  supervisors: []
})
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const activeTab = ref("onsite")
const viewActiveTab = ref("onsite")

const data = reactive({
  form: {},
  viewForm: {},
  monthlyEditForm: {
    id: null,
    name: '',
    month: null,
    leader: '',
    monopolyStaff: '',
    marketingStaff: '',
    supervisorStaff: ''
  },
  monthlyViewForm: {
    id: null,
    name: '',
    month: null,
    leader: '',
    monopolyStaff: '',
    marketingStaff: '',
    supervisorStaff: '',
    district: '',
    inspectedHouseholds: null,
    status: ''
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    year: null,
    quarter: null,
    status: null,
    plannerUserId: null,
  },
  rules: {
    name: [
      { required: true, message: "考核名称不能为空", trigger: "blur" }
    ],
    year: [
      { required: true, message: "所属年份不能为空", trigger: "change" }
    ],
    quarter: [
      { required: true, message: "所属季度不能为空", trigger: "change" }
    ]
  },
  monthlyEditRules: {
    leader: [
      { required: true, message: "组长不能为空", trigger: "blur" }
    ],
    monopolyStaff: [
      { required: true, message: "专卖人员不能为空", trigger: "blur" }
    ],
    marketingStaff: [
      { required: true, message: "营销人员不能为空", trigger: "blur" }
    ],
    supervisorStaff: [
      { required: true, message: "监督人员不能为空", trigger: "blur" }
    ]
  }
})

const { queryParams, form, viewForm, monthlyEditForm, monthlyViewForm, rules, monthlyEditRules } = toRefs(data)

const tableRowClassName = ({ row, rowIndex }) => {
  if ((rowIndex + 1) % 2 === 0) {
    return 'table-row'
  }
  return ''
}

/** 查询季度考核列表 */
function getList() {
  loading.value = true
  listQuarterlyassessments(queryParams.value).then(response => {
    quarterlyassessmentsList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    year: null,
    quarter: null,
    status: "1",
    plannerUserId: null
  }
  monthlyAssessmentsList.value = []
  activeTab.value = "onsite"
  proxy.resetForm("quarterlyassessmentsRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  router.push({
    path: '/assessment/quarterlyassessments/childs/add'
  })
}

/** 查看按钮操作 */
function handleView(row) {
  const _id = row.id
  getQuarterlyassessments(_id).then(response => {
    viewForm.value = response.data
    viewActiveTab.value = "onsite"
    viewOpen.value = true
  })
}

/** 修改按钮操作 */
function handleUpdate(row) {
  router.push({
    path: '/assessment/quarterlyassessments/childs/configuration',
    query: {
      id: row.id || ids.value
    }
  })
  // reset()
  // const _id = row.id || ids.value
  // getQuarterlyassessments(_id).then(response => {
  //   form.value = response.data
  //   // 加载现有的月度考核数据
  //   monthlyAssessmentsList.value = response.data.monthlyAssessmentsList || []
  //   open.value = true
  //   title.value = "修改季度考核"
  // })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["quarterlyassessmentsRef"].validate(valid => {
    if (valid) {
      // 准备提交的数据
      const submitData = { ...form.value }
      
      // 如果是新增，包含月度考核列表
      if (!form.value.id) {
        submitData.monthlyAssessmentsList = monthlyAssessmentsList.value
      }
      
      if (form.value.id != null) {
        updateQuarterlyassessments(submitData).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addQuarterlyassessments(submitData).then(response => {
          proxy.$modal.msgSuccess("新增成功，已自动生成对应的月度考核记录")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除季度考核编号为"' + _ids + '"的数据项？').then(function() {
    return delQuarterlyassessments(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('quarterlyassessments/quarterlyassessments/export', {
    ...queryParams.value
  }, `quarterlyassessments_${new Date().getTime()}.xlsx`)
}

/** 年份和季度变化处理 */
function handleYearQuarterChange() {
  if (form.value.year && form.value.quarter) {
    generateMonthlyAssessmentsList()
  } else {
    monthlyAssessmentsList.value = []
  }
}

/** 生成月度考核列表 */
function generateMonthlyAssessmentsList() {
  const year = form.value.year
  const quarter = form.value.quarter
  
  if (!year || !quarter) {
    monthlyAssessmentsList.value = []
    return
  }
  
  // 根据季度计算起始月份
  const startMonth = (quarter - 1) * 3 + 1
  const monthlyList = []
  
  // 生成三个月的数据
  for (let i = 0; i < 3; i++) {
    const month = startMonth + i
    const monthlyAssessment = {
      name: `${month}月现场考核`,
      month: month,
      assessmentType: '现场', // 现场考核类型
      creatorUserId: form.value.plannerUserId || null,
      district: null,
      inspectedHouseholds: null,
      status: '1' // 默认已提交
    }
    monthlyList.push(monthlyAssessment)
  }
  
  monthlyAssessmentsList.value = monthlyList
}

/** 禁用日期处理 */
function disabledDate(date) {
  const currentDate = new Date()
  const currentYear = currentDate.getFullYear()
  const year = date.getFullYear()
  
  // 只禁用今年之前的年份
  return year < currentYear
}

/** 根据角色名获取人员姓名 */
function getStaffByRole(staffList, roleName) {
  if (!staffList || !Array.isArray(staffList)) return null
  
  // 角色名称到角色ID的映射
  const roleMapping = {
    '组长': 102,
    '专卖人员': 104,
    '营销人员': 105,
    '监督人员': 103
  }
  
  const targetRoleId = roleMapping[roleName]
  if (!targetRoleId) return null
  
  // 通过角色ID匹配（更可靠）
  const staff = staffList.find(item => 
    item.roleId === targetRoleId || 
    item.role?.roleId === targetRoleId ||
    (item.roleName === roleName) // 兼容旧的角色名匹配
  )
  
  return staff ? (staff.userName || staff.user?.userName) : null
}

/** 获取用户列表 */
function getUserList() {
  // 获取所有用户作为兜底
  const queryParams = {
    pageNum: 1,
    pageSize: 1000,
    status: '0'
  }
  return listUser(queryParams).then(response => {
    userList.value = response.rows.map(user => ({
      id: user.userId,
      name: user.userName,
      nickName: user.nickName
    }))
    return userList.value
  }).catch(error => {
    console.error('获取用户列表失败:', error)
    proxy.$modal.msgError("获取用户列表失败")
    return []
  })
}

/** 根据角色获取用户列表 */
function getUserListByRole(roleId) {
  return allocatedUserList({ 
    roleId: roleId,
    pageNum: 1,
    pageSize: 1000
  }).then(response => {
    return response.rows.map(user => ({
      id: user.userId,
      name: user.userName,
      nickName: user.nickName
    }))
  }).catch(error => {
    console.error(`获取角色${roleId}的用户列表失败:`, error)
    return []
  })
}

/** 获取所有角色的用户列表 */
async function getAllRoleUserLists() {
  try {
    // 根据系统中定义的角色ID获取用户列表
    // 这些ID需要根据实际系统中的角色ID调整
    const [leaders, supervisors, monopoly, marketing] = await Promise.all([
      getUserListByRole(102), // 组长角色ID
      getUserListByRole(103), // 监督人员角色ID  
      getUserListByRole(104), // 专卖人员角色ID
      getUserListByRole(105)  // 营销人员角色ID
    ])
    
    leaderUserList.value = leaders
    supervisorUserList.value = supervisors
    monopolyUserList.value = monopoly
    marketingUserList.value = marketing
    
    // 如果某个角色没有用户，则使用所有用户作为兜底
    if (leaderUserList.value.length === 0) leaderUserList.value = userList.value
    if (supervisorUserList.value.length === 0) supervisorUserList.value = userList.value
    if (monopolyUserList.value.length === 0) monopolyUserList.value = userList.value
    if (marketingUserList.value.length === 0) marketingUserList.value = userList.value
    
  } catch (error) {
    console.error('获取角色用户列表失败:', error)
    // 出错时使用所有用户作为兜底
    leaderUserList.value = userList.value
    supervisorUserList.value = userList.value
    monopolyUserList.value = userList.value
    marketingUserList.value = userList.value
  }
}

/** 月度考核查看 */
function handleMonthlyView(row) {
  monthlyViewForm.value = {
    id: row.id,
    name: row.name,
    month: row.month,
    leader: getStaffByRole(row.staffList, '组长') || '',
    monopolyStaff: getStaffByRole(row.staffList, '专卖人员') || '',
    marketingStaff: getStaffByRole(row.staffList, '营销人员') || '',
    supervisorStaff: getStaffByRole(row.staffList, '监督人员') || '',
    district: row.district || '',
    inspectedHouseholds: row.inspectedHouseholds,
    status: row.status || ''
  }
  monthlyViewOpen.value = true
}

/** 月度考核修改 */
function handleMonthlyEdit(row) {
  // 重置表单
  monthlyEditForm.value = {
    id: row.id,
    name: row.name,
    month: row.month,
    leader: getStaffByRole(row.staffList, '组长') || '',
    monopolyStaff: getStaffByRole(row.staffList, '专卖人员') || '',
    marketingStaff: getStaffByRole(row.staffList, '营销人员') || '',
    supervisorStaff: getStaffByRole(row.staffList, '监督人员') || ''
  }
  
  // 清空抽取记录
  selectedStaffFromDraw.value = { leaders: [], supervisors: [] }
  
  // 打开对话框
  monthlyEditOpen.value = true
  
  // 异步获取用户列表，不阻塞对话框打开
  loadUserLists()
}

/** 异步加载用户列表 */
async function loadUserLists() {
  try {
    // 先获取所有用户列表作为兜底
    await getUserList()
    
    // 然后获取各角色的用户列表
    await getAllRoleUserLists()
  } catch (error) {
    console.error('加载用户列表失败:', error)
    proxy.$modal.msgError("加载用户列表失败，请刷新重试")
  }
}

/** 月度考核发布 */
function handleMonthlyPublish(row) {
  proxy.$modal.confirm('是否确认发布"' + row.name + '"？').then(function() {
    // TODO: 调用发布接口
    proxy.$modal.msgSuccess("发布功能待开发")
    console.log("发布月度考核:", row)
  }).catch(() => {})
}

/** 月度考核禁用 */
function handleMonthlyDisable(row) {
  proxy.$modal.confirm('是否确认禁用"' + row.name + '"？').then(function() {
    // TODO: 调用禁用接口
    proxy.$modal.msgSuccess("禁用功能待开发")
    console.log("禁用月度考核:", row)
  }).catch(() => {})
}

/** 提交月度考核修改表单 */
function submitMonthlyEditForm() {
  proxy.$refs["monthlyEditRef"].validate(valid => {
    if (valid) {
      // 统一使用saveSelectedStaff方法，包含完整的人员信息
      let year = viewForm.value.year
      if (year && year.includes('-')) {
        year = year.split('-')[0]
      } else if (!year) {
        year = new Date().getFullYear().toString()
      }
      
      // 获取当前月度考核的现有人员信息，用于判断哪些角色需要更新
      const currentStaffList = viewForm.value.monthlyAssessmentsList?.find(m => m.id == monthlyEditForm.value.id)?.staffList || []

      // 获取当前各角色的人员
      const currentLeader = getStaffByRole(currentStaffList, '组长')
      const currentSupervisor = getStaffByRole(currentStaffList, '监督人员')
      const currentMonopoly = getStaffByRole(currentStaffList, '专卖人员')
      const currentMarketing = getStaffByRole(currentStaffList, '营销人员')

      // 只有当表单值与当前值不同时，才更新对应角色
      // 使用 null 表示不更新，空数组表示清空，非空数组表示更新
      let leaders = null
      let supervisors = null
      let monopolyStaffs = null
      let marketingStaffs = null

      // 处理组长 - 检查是否有变化
      const formLeader = monthlyEditForm.value.leader || ''
      if (formLeader !== (currentLeader || '')) {
        leaders = []
        // 优先使用抽取的，如果没有则从手动选择中构造
        if (selectedStaffFromDraw.value.leaders.length > 0) {
          leaders.push(...selectedStaffFromDraw.value.leaders)
        } else if (formLeader) {
          // 手动选择的组长，需要构造用户对象
          const leaderUser = leaderUserList.value.find(user => user.name === formLeader)
          if (leaderUser) {
            leaders.push({
              userId: leaderUser.id,
              userName: leaderUser.name,
              nickName: leaderUser.nickName
            })
          }
        }
      }

      // 处理监督人员 - 检查是否有变化
      const formSupervisor = monthlyEditForm.value.supervisorStaff || ''
      if (formSupervisor !== (currentSupervisor || '')) {
        supervisors = []
        // 优先使用抽取的，如果没有则从手动选择中构造
        if (selectedStaffFromDraw.value.supervisors.length > 0) {
          supervisors.push(...selectedStaffFromDraw.value.supervisors)
        } else if (formSupervisor) {
          // 手动选择的监督人员，需要构造用户对象
          const supervisorUser = supervisorUserList.value.find(user => user.name === formSupervisor)
          if (supervisorUser) {
            supervisors.push({
              userId: supervisorUser.id,
              userName: supervisorUser.name,
              nickName: supervisorUser.nickName
            })
          }
        }
      }

      // 处理专卖人员 - 检查是否有变化
      const formMonopoly = monthlyEditForm.value.monopolyStaff || ''
      if (formMonopoly !== (currentMonopoly || '')) {
        monopolyStaffs = []
        if (formMonopoly) {
          const monopolyUser = monopolyUserList.value.find(user => user.name === formMonopoly)
          if (monopolyUser) {
            monopolyStaffs.push({
              userId: monopolyUser.id,
              userName: monopolyUser.name,
              nickName: monopolyUser.nickName
            })
          }
        }
      }

      // 处理营销人员 - 检查是否有变化
      const formMarketing = monthlyEditForm.value.marketingStaff || ''
      if (formMarketing !== (currentMarketing || '')) {
        marketingStaffs = []
        if (formMarketing) {
          const marketingUser = marketingUserList.value.find(user => user.name === formMarketing)
          if (marketingUser) {
            marketingStaffs.push({
              userId: marketingUser.id,
              userName: marketingUser.name,
              nickName: marketingUser.nickName
            })
          }
        }
      }
      
      const saveData = {
        monthlyAssessmentId: parseInt(monthlyEditForm.value.id),
        year: year,
        month: monthlyEditForm.value.month || 1,
        leaders: leaders,
        supervisors: supervisors,
        monopolyStaffs: monopolyStaffs,
        marketingStaffs: marketingStaffs
      }
      
      saveSelectedStaff(saveData).then(response => {
        if (response.code === 200) {
          const hasHistoryStaff = (leaders && leaders.length > 0) || (supervisors && supervisors.length > 0)
          const message = hasHistoryStaff ? "保存成功，已记录分配历史" : "保存成功"
          proxy.$modal.msgSuccess(message)
          monthlyEditOpen.value = false
          
          // 清空抽取记录
          selectedStaffFromDraw.value = { leaders: [], supervisors: [] }
          
          // 重新获取详情以刷新数据
          if (viewForm.value.id) {
            getQuarterlyassessments(viewForm.value.id).then(response => {
              viewForm.value = response.data
            })
          }
        } else {
          proxy.$modal.msgError("保存失败")
        }
      }).catch(error => {
        console.error('保存人员分配失败:', error)
        proxy.$modal.msgError("保存失败，请重试")
      })
    }
  })
}

/** 抽取组长按钮操作 */
function handleSelectLeader() {
  selectLeaderOpen.value = true
}

/** 抽取监督人员按钮操作 */
function handleSelectSupervisor() {
  selectSupervisorOpen.value = true
}

/** 提交抽取组长表单 */
function submitSelectLeader() {
  // 获取正确的年份
  let year = viewForm.value.year
  if (year && year.includes('-')) {
    // 如果是日期格式，提取年份
    year = year.split('-')[0]
  } else if (!year) {
    // 如果没有年份，使用当前年份
    year = new Date().getFullYear().toString()
  }
  
  const data = {
    monthlyAssessmentId: parseInt(monthlyEditForm.value.id), // 确保是数字类型
    year: year, // 确保只是年份字符串
    count: 1 // 固定抽取1人
  }
  
  console.log("抽取组长请求参数:", data) // 调试信息
  
  selectRandomLeaders(data).then(response => {
    if (response.code === 200 && response.data && response.data.leaders && response.data.leaders.length > 0) {
      // 获取抽取的组长
      const leader = response.data.leaders[0]
      // 更新表单中的组长
      monthlyEditForm.value.leader = leader.userName
      
      // 记录抽取的组长信息，用于最终保存时记录历史
      selectedStaffFromDraw.value.leaders = [leader]
      
      proxy.$modal.msgSuccess(`成功抽取组长: ${leader.userName}${leader.nickName ? '(' + leader.nickName + ')' : ''}`)
    } else {
      proxy.$modal.msgWarning("未能抽取到符合条件的组长，请手动选择")
    }
    selectLeaderOpen.value = false
  }).catch(error => {
    console.error("抽取组长失败:", error)
    proxy.$modal.msgError("抽取组长失败")
    selectLeaderOpen.value = false
  })
}

/** 提交抽取监督人员表单 */
function submitSelectSupervisor() {
  // 获取正确的年份
  let year = viewForm.value.year
  if (year && year.includes('-')) {
    // 如果是日期格式，提取年份
    year = year.split('-')[0]
  } else if (!year) {
    // 如果没有年份，使用当前年份
    year = new Date().getFullYear().toString()
  }
  
  const data = {
    monthlyAssessmentId: parseInt(monthlyEditForm.value.id), // 确保是数字类型
    year: year, // 确保只是年份字符串
    month: monthlyEditForm.value.month || 1,
    count: 1 // 固定抽取1人
  }
  
  console.log("抽取监督人员请求参数:", data) // 调试信息
  
  selectRandomSupervisors(data).then(response => {
    if (response.code === 200 && response.data && response.data.supervisors && response.data.supervisors.length > 0) {
      // 获取抽取的监督人员
      const supervisor = response.data.supervisors[0]
      // 更新表单中的监督人员
      monthlyEditForm.value.supervisorStaff = supervisor.userName
      
      // 记录抽取的监督人员信息，用于最终保存时记录历史
      selectedStaffFromDraw.value.supervisors = [supervisor]
      
      proxy.$modal.msgSuccess(`成功抽取监督人员: ${supervisor.userName}${supervisor.nickName ? '(' + supervisor.nickName + ')' : ''}`)
    } else {
      proxy.$modal.msgWarning("未能抽取到符合条件的监督人员，请手动选择")
    }
    selectSupervisorOpen.value = false
  }).catch(error => {
    console.error("抽取监督人员失败:", error)
    proxy.$modal.msgError("抽取监督人员失败")
    selectSupervisorOpen.value = false
  })
}

getList()
</script>

<style scoped>
.input-with-button {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.input-with-button .el-select {
  flex: 1;
  min-width: 0;
}

.input-with-button .el-button {
  flex-shrink: 0;
  height: 32px;
  white-space: nowrap;
  min-width: fit-content;
}
</style>

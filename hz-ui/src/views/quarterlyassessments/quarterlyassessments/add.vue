<template>
    <div class="app-container">
        <div class="store_header">
            <div class="store_header_title">
                <div>新增考核</div>
            </div>
        </div>
        <div class="store_content">
            <div class="store_content_header">
                <div class="title_line"></div>
                <div class="title">基本信息</div>
            </div>
            <el-form class="store_content_form" ref="quarterlyassessmentsRef" :model="form" label-width="auto">
                <el-row>
                    <el-col :span="7">
                        <el-form-item label="所属年份" prop="year">
                            <el-date-picker v-model="form.year" type="year" :clearable="false" placeholder="请选择年份"
                                value-format="YYYY" style="width: 90%" @change="handleYearQuarterChange"
                                :disabled-date="disabledDate" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="7">
                        <el-form-item label="所属季度" prop="quarter">
                            <el-select v-model="form.quarter" placeholder="请选择季度" style="width: 100%"
                                @change="handleYearQuarterChange">
                                <el-option label="第一季度" :value="1" />
                                <el-option label="第二季度" :value="2" />
                                <el-option label="第三季度" :value="3" />
                                <el-option label="第四季度" :value="4" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div class="from_container" style="padding: 35px 30px;">
            <el-tabs v-model="activeTab" style="margin-bottom: 20px" v-if="monthlyAssessmentsList.length > 0">
                <!-- 现场考核页签 -->
                <el-tab-pane :label="item.name" :name="item.month" v-for="(item, index) in monthlyAssessmentsList"
                    :key="index">
                    <el-table :data="item.childsList" border style="width: 100%" max-height="300"
                        :row-class-name="tableRowClassName">
                        <el-table-column label="县区" prop="label" align="center" />
                        <el-table-column label="所属月份" prop="month" align="center">
                            <template #default="scope">
                                <span>{{ item.month }}月</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>

                <!-- 目标考核页签 -->
                <el-tab-pane label="目标考核" name="target">
                    <el-empty description="目标考核功能开发中，敬请期待" :image-size="100">
                        <template #image>
                            <el-icon size="100" color="#409EFF">
                                <Document />
                            </el-icon>
                        </template>
                    </el-empty>
                </el-tab-pane>

                <!-- 汇总页签 -->
                <el-tab-pane label="汇总" name="summary">
                    <el-empty description="汇总功能开发中，敬请期待" :image-size="100">
                        <template #image>
                            <el-icon size="100" color="#67C23A">
                                <DataAnalysis />
                            </el-icon>
                        </template>
                    </el-empty>
                </el-tab-pane>
            </el-tabs>
            <el-empty v-if="monthlyAssessmentsList.length === 0 && form.year && form.quarter"
                description="请选择年份和季度以生成月度考核预览" :image-size="80" />
        </div>
        <div class="dialog-footer">
            <el-button class="upload_submit" @click="submitForm">确 定</el-button>
            <el-button class="upload_cancel" @click="cancel">取 消</el-button>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { addQuarterlyassessments } from "@/api/quarterlyassessments/quarterlyassessments"
import { deptTreeSelect } from "@/api/system/user"

const { proxy } = getCurrentInstance()
const form = ref({})
const monthlyAssessmentsList = ref([])
const activeTab = ref("")
const deptOptionsList = ref([])

onMounted(() => {
    const currentDate = new Date()
    const currentYear = currentDate.getFullYear()
    const currentMonth = currentDate.getMonth() + 1
    const currentQuarter = Math.ceil(currentMonth / 3)
    form.value.year = currentYear.toString()
    form.value.quarter = currentQuarter
    form.value.status = "1"
    getDeptTree()
})
/** 查询地区下拉树结构 */
const getDeptTree = () => {
    deptTreeSelect().then(response => {
        let list = []
        response.data.forEach(item => {
            if (item.children && item.children.length > 0) {
                item.children.forEach(child => {
                    list.push({
                        value: child.id,
                        label: child.label
                    })
                })
            }
        })
        deptOptionsList.value = list
        generateMonthlyAssessmentsList()
    })
}
const tableRowClassName = ({ row, rowIndex }) => {
    if ((rowIndex + 1) % 2 === 0) {
        return 'table-row'
    }
    return ''
}
/** 年份和季度变化处理 */
const handleYearQuarterChange = () => {
    if (form.value.year && form.value.quarter) {
        generateMonthlyAssessmentsList()
    } else {
        monthlyAssessmentsList.value = []
    }
}
/** 生成月度考核列表 */
const generateMonthlyAssessmentsList = () => {
    const year = form.value.year
    const quarter = form.value.quarter

    if (!year || !quarter) {
        monthlyAssessmentsList.value = []
        return
    }

    // 根据季度计算起始月份
    const startMonth = (quarter - 1) * 3 + 1
    const monthlyList = []

    // 生成三个月的数据
    for (let i = 0; i < 3; i++) {
        const month = startMonth + i
        const monthlyAssessment = {
            name: `${month}月现场考核`,
            month: month,
            assessmentType: '现场', // 现场考核类型
            creatorUserId: form.value.plannerUserId || null,
            district: null,
            inspectedHouseholds: null,
            status: '1', // 默认已提交
            childsList: deptOptionsList.value
        }
        monthlyList.push(monthlyAssessment)
    }

    monthlyAssessmentsList.value = monthlyList
    activeTab.value = monthlyAssessmentsList.value[0].month
}
/** 禁用日期处理 */
const disabledDate = (date) => {
    const currentDate = new Date()
    const currentYear = currentDate.getFullYear()
    const year = date.getFullYear()

    // 只禁用今年之前的年份
    return year < currentYear
}
const submitForm = () => {
    proxy.$refs["quarterlyassessmentsRef"].validate(valid => {
        if (valid) {
            form.value.name = form.value.year + '年' + '第' + form.value.quarter + '季度考核'
            const submitData = { ...form.value, monthlyAssessmentsList: monthlyAssessmentsList.value }
            addQuarterlyassessments(submitData).then(response => {
                proxy.$modal.msgSuccess("新增成功")
                cancel()
            }).catch(error => {
                // 如果错误信息是 'error'，说明响应拦截器已经显示了错误通知，不需要再显示
                if (error.message === 'error') {
                    return
                }
                
                // 处理其他错误情况
                let errorMessage = "新增失败，请稍后重试"
                
                if (error.response && error.response.data) {
                    // 如果服务器返回了具体的错误信息，使用服务器的错误信息
                    if (error.response.data.msg) {
                        errorMessage = error.response.data.msg
                    } else if (error.response.data.message) {
                        errorMessage = error.response.data.message
                    }
                }
                
                proxy.$modal.msgError(errorMessage)
            })
        }
    })
}

const cancel = () => {
    const obj = { path: "/assessment/quarterlyassessments" }
    proxy.$tab.closeOpenPage(obj)
}
</script>

<style lang="scss" scoped>
.app-container {
    background-color: #f4f8ff;
    color: #333333;
}

.store_header {
    box-sizing: border-box;
    padding: 20px 30px;
    background-color: #fff;
    border-radius: 5px;

    .store_header_title {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        display: flex;
        align-items: center;
    }
}

.store_content {
    box-sizing: border-box;
    padding: 20px 30px;
    background-color: #fff;
    border-radius: 5px;
    margin-top: 10px;

    .store_content_header {
        display: flex;
        align-items: center;

        .title_line {
            width: 3px;
            height: 19px;
            background: #21A042;
            border-radius: 2px;
        }

        .title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-left: 10px;
        }
    }

    .store_content_form {
        margin-top: 25px;
    }
}

:deep(.el-tabs) {
    .el-tabs__item {
        font-size: 22px;
        font-weight: bold;
        padding-bottom: 15px;
    }

    .el-tabs__nav-wrap:after {
        display: none;
    }

    .el-tabs__active-bar {
        background-color: #049B01;
        height: 4px;
        border-radius: 5px;
    }

    .el-tabs__item.is-active,
    .el-tabs__item:hover {
        color: #049B01;
    }
}

.from_container {
    min-height: calc(100vh - 600px);
}

.dialog-footer {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 20px 30px;
    background-color: #fff;
    margin-top: 10px;

    .upload_submit,
    .upload_cancel {
        box-sizing: border-box;
        width: 140px;
        height: 45px;
        font-size: 16px;
        border: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
    }

    .upload_submit {
        color: #fff;
        background-color: #21A042;
    }

    .upload_cancel {
        border: 1px solid #21A042;
        color: #21A042;
    }
}
</style>
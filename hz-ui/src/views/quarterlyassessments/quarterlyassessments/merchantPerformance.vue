<template>
    <div class="app-container">
        <div class="store_header">
            <div class="store_header_title">
                <div>{{ data.name }}</div>
            </div>
        </div>
        <div class="store_content">
            <div class="store_content_header">
                <div class="title_line"></div>
                <div class="title">基本信息</div>
            </div>
            <div class="store_content_cont">
                <div class="store_content_left">
                    <div class="store_content_name">
                        {{ data.year }}年{{ data.month }}月份商户现场考核详情
                    </div>
                    <div class="store_content_main">
                        <div>考核时间：{{ data.createTime }}</div>
                        <div class="county">考核县域：城区分公司</div>
                        <div class="num">检查户数：{{ data.inspectedHouseholds }}</div>
                    </div>
                    <div class="store_content_text">
                        考核人员：<span v-for="(item, index) in staffList" :key="index">{{ item.name }} ({{ item.type
                        }}) <span v-if="index !== staffList.length - 1">、</span> </span>
                    </div>
                </div>
                <div class="store_content_right">
                    <div class="content_num">
                        <div class="content_num_header">
                            {{ zhuanmaiNum }}
                        </div>
                        <div class="content_num_footer">专卖现场商户综合得分</div>
                    </div>
                    <div class="line"></div>
                    <div class="content_num">
                        <div class="content_num_header">
                            {{ yingxiaoNum }}
                        </div>
                        <div class="content_num_footer">营销现场商户综合得分</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="store_content">
            <div class="store_content_ps">
                <div class="store_content_header">
                    <div class="title_line"></div>
                    <div class="title">考核信息</div>
                </div>
                <div class="store_content_header_right">
                    <div class="title">任务数量：</div>
                    <div class="task_num">{{ successNum + '/' + monthlyAssessmentsList.length }}</div>
                </div>
            </div>

            <div class="from_container" style="min-height: 100%;padding: 0;">
                <el-table :data="monthlyAssessmentsList" border style="width: 100%" :row-class-name="tableRowClassName">
                    <el-table-column label="店铺名" prop="merchantName" align="center" width="300" />
                    <el-table-column label="类型" align="center">
                        <template #default="scope">
                            <div
                                :style="{ color: scope.row.selectionType == '0' ? '#049B01' : scope.row.selectionType == '1' ? '#0083EA' : '#FF1F1F' }">
                                {{ scope.row.selectionTypeLabel || getSelectionTypeLabel(scope.row.selectionType) }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="商户负责人" prop="legalName" align="center" />
                    <el-table-column label="联系电话" prop="phoneNumber" align="center" />
                    <el-table-column label="终端类型" prop="phoneNumber" align="center" />
                    <el-table-column label="专卖现场考核综合评分" prop="zhuanmaiScore" align="center" />
                    <el-table-column label="营销现场考核综合评分" prop="yingxiaoScore" align="center" />
                    <el-table-column label="专卖考核状态" align="center">
                        <template #default="scope">
                            <div class="status_text" :style="{ color: getStatusText(scope.row, 'zm', 'color') }">
                                <div class="icon" :style="{ background: getStatusText(scope.row, 'zm', 'color') }">
                                </div>
                                <div>{{ getStatusText(scope.row, 'zm') }}</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="营销考核状态" align="center">
                        <template #default="scope">
                            <div class="status_text" :style="{ color: getStatusText(scope.row, 'zm', 'color') }">
                                <div class="icon" :style="{ background: getStatusText(scope.row, 'zm', 'color') }">
                                </div>
                                <div>{{ getStatusText(scope.row, 'yx') }}</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" width="200">
                        <template #default="scope">
                            <div class="table-btn">
                                <el-button class="table-btn-edit" link
                                    @click="viewMerchantDetails(scope.row, 'check')">查看</el-button>
                                <div class="btn-line" v-if="scope.row.yingxiaoCheckStatus == '3' || scope.row.zhuanmaiCheckStatus == '3'"></div>
                                <el-button
                                    v-if="scope.row.yingxiaoCheckStatus == '3' || scope.row.zhuanmaiCheckStatus == '3'"
                                    class="table-btn-edit" link
                                    @click="viewMerchantDetails(scope.row, 'edit')">修改</el-button>
                                <div class="btn-line"
                                    v-if="scope.row.yingxiaoCheckStatus == '3' || scope.row.zhuanmaiCheckStatus == '3'">
                                </div>
                                <el-button
                                    v-if="scope.row.yingxiaoCheckStatus == '3' || scope.row.zhuanmaiCheckStatus == '3'"
                                    class="table-btn-delete" link @click="handleAppeal(scope.row)">申诉</el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <div class="dialog-footer">
            <el-button class="upload_cancel" @click="cancel">返 回</el-button>
        </div>
        <el-dialog v-model="appealShow" width="800" append-to-body class="dialog_component">
            <div class="dialog_header">
                <div class="header_left">
                    <img src="@/assets/images/dialog-icon.png" alt="">
                    <div class="dialog_title">
                        申诉
                    </div>
                </div>
                <div class="dialog_close" @click="appealShow = false">
                    <el-icon>
                        <CloseBold />
                    </el-icon>
                </div>
            </div>
            <div class="appeal_content">
                <div class="appeal_content_header">
                    <div class="appeal_content_header_left">
                        <div class="left_label">申诉商户</div>
                        <div class="left_text">{{ appealItem.merchantName }}</div>
                    </div>
                    <div class="appeal_content_header_right">
                        <div class="left_label">类型</div>
                        <div
                            :style="{ color: appealItem.selectionType == '0' ? '#049B01' : appealItem.selectionType == '1' ? '#0083EA' : '#FF1F1F' }">
                            {{ appealItem.selectionTypeLabel || getSelectionTypeLabel(appealItem.selectionType) }}
                        </div>
                    </div>
                </div>
                <div class="appeal_content_choose">
                    <div class="left_label">身份</div>
                    <el-radio-group v-model="appealMap.paperId">
                        <el-radio v-for="(item, index) in appealItem.examinationPaperList" :key="index"
                            :value="item.id">{{
                                item.name }}</el-radio>
                    </el-radio-group>
                </div>
                <div class="appeal_content_message">
                    <div class="left_label"><span style="color: #FF1414;">*</span> 申诉内容</div>
                    <el-input v-model="appealMap.msgContent" :autosize="{ minRows: 4 }" type="textarea"
                        placeholder="请输入申诉内容" />
                </div>
                <div class="appeal_content_image">
                    <div class="left_label">图片上传</div>
                    <ImageUpload v-model="appealMap.msgImage" width="120" height="120"></ImageUpload>
                </div>
            </div>
            <template #footer>
                <div class="dialog-footer-btn">
                    <el-button class="dialog_submit" @click="submitAppeal">申 诉</el-button>
                    <el-button class="dialog_cancel" @click="appealShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
import cache from '@/plugins/cache'
import ImageUpload from "@/components/ImageUpload"
import { ElMessage } from 'element-plus'
import { summaryByMonthlyId, messageAppeal } from "@/api/quarterlyassessments/quarterlyassessments"
const { proxy } = getCurrentInstance()
const data = cache.session.getJSON('configData')
const monthlyAssessmentsList = ref([])
const staffList = ref([])
const zhuanmaiNum = ref(0)
const yingxiaoNum = ref(0)
const successNum = ref(0)
const router = useRouter()
const appealShow = ref(false)
const appealItem = ref({})
const appealMap = ref({
    paperId: '',
    msgTitle: '申诉',
    msgContent: '',
    msgImage: '',
    merchantId: ''
})

onMounted(() => {
    staffList.value = []
    if (data.staffMap) {
        for (let i in data.staffMap) {
            staffList.value.push({
                name: data.staffMap[i],
                type: i
            })
        }
    }
    getSummaryByMonthlyId()
})

// 查询商户成绩列表
const getSummaryByMonthlyId = () => {
    const params = {
        monthlyAssessmentId: data.id
    }
    summaryByMonthlyId(params).then(res => {
        if (res.code === 200) {
            zhuanmaiNum.value = 0
            yingxiaoNum.value = 0
            successNum.value = 0
            monthlyAssessmentsList.value = res.rows
            for (let i = 0; i < monthlyAssessmentsList.value.length; i++) {
                if (monthlyAssessmentsList.value[i].selectionType == '2') {
                    const data = JSON.parse(monthlyAssessmentsList.value[i].remark)
                    monthlyAssessmentsList.value[i].merchantName = data.merchantName || ''
                    monthlyAssessmentsList.value[i].legalName = data.legalName || ''
                    monthlyAssessmentsList.value[i].county = data.county || ''
                    monthlyAssessmentsList.value[i].photo = data.photo || ''
                    monthlyAssessmentsList.value[i].businessAddress = data.businessAddress || ''
                }
                monthlyAssessmentsList.value[i].zhuanmaiScore = 0
                monthlyAssessmentsList.value[i].yingxiaoScore = 0
                if (monthlyAssessmentsList.value[i].examinationPaperList.length) {
                    const map = {
                        '专卖': 0,
                        '营销': 0
                    }
                    const list = monthlyAssessmentsList.value[i].examinationPaperList
                    for (let k = 0; k < list.length; k++) {
                        map[list[k].type] += list[k].sumScore
                    }
                    monthlyAssessmentsList.value[i].zhuanmaiScore = map['专卖']
                    monthlyAssessmentsList.value[i].yingxiaoScore = map['营销']
                }
            }
            for (let i = 0; i < monthlyAssessmentsList.value.length; i++) {
                zhuanmaiNum.value += monthlyAssessmentsList.value[i].zhuanmaiScore
                yingxiaoNum.value += monthlyAssessmentsList.value[i].yingxiaoScore
                if (monthlyAssessmentsList.value[i].yingxiaoCheckStatus == 3 && monthlyAssessmentsList.value[i].zhuanmaiCheckStatus == 3) {
                    successNum.value++
                }
            }
        }
    })
}

// table样式
const tableRowClassName = ({ row, rowIndex }) => {
    if ((rowIndex + 1) % 2 === 0) {
        return 'table-row'
    }
    return ''
}

// 获取商户类型标签
const getSelectionTypeLabel = (selectionType) => {
    switch (selectionType) {
        case '0':
            return '考核商户'
        case '1':
            return '自主添加商户'
        case '2':
            return '自主添加无证户'
        default:
            return selectionType
    }
}

const getStatusText = (item, type, ms) => {
    let msg = ''
    let color = ''
    if (item.assessmentStatus == 1) {
        msg = '已闭店'
        color = '#FE2121'
        if (ms == 'color') return color
        else return msg
    }
    const status = type == 'zm' ? item.zhuanmaiCheckStatus : item.yingxiaoCheckStatus
    switch (status) {
        case '0':
            msg = '未考核'
            color = '#0083EA'
            break
        case '1':
            msg = '考核中'
            color = '#FF6D00'
            break
        case '2':
            msg = '考核中'
            color = '#FF6D00'
            break
        case '3':
            msg = '已考核'
            color = '#21A042'
            break
    }
    if (ms == 'color') return color
    else return msg
}

const handleAppeal = (row) => {
    const list = []
    for (let i = 0; i < row.examinationPaperList.length; i++) {
        if (row.examinationPaperList[i].type == '专卖' && row.zhuanmaiCheckStatus == '3') {
            list.push(row.examinationPaperList[i])
        }
        if (row.examinationPaperList[i].type == '营销' && row.yingxiaoCheckStatus == '3') {
            list.push(row.examinationPaperList[i])
        }
    }
    appealMap.value = {
        paperId: list[0].id,
        msgTitle: '申诉',
        msgContent: '',
        msgImage: '',
        merchantId: row.merchantId
    }
    appealItem.value = {
        merchantName: row.merchantName,
        selectionType: row.selectionType,
        selectionTypeLabel: row.selectionTypeLabel,
        examinationPaperList: list
    }
    appealShow.value = true
}

const submitAppeal = () => {
    if (!appealMap.value.msgContent) {
        ElMessage.warning('请输入申诉内容')
        return
    }
    messageAppeal({
        ...appealMap.value
    }).then(res => {
        if (res.code === 200) {
            ElMessage.success('申诉成功')
            appealShow.value = false
        } else {
            ElMessage.error(res.msg || '申诉失败，请稍后再试')
        }
    }).catch(() => {
        ElMessage.error('申诉失败，请稍后再试')
    })
}

const viewMerchantDetails = (row, type) => {
    router.push({
        path: '/assessment/quarterlyassessments/childs/merchantDetails',
        query: {
            id: row.merchantId,
            type
        }
    })
}

const cancel = () => {
    const params = {
        path: "/assessment/quarterlyassessments/childs/configuration",
        query: {
            id: data.quarterId
        }
    }
    proxy.$tab.closeOpenPage(params)
}
</script>
<style lang="scss" scoped>
.app-container {
    background-color: #f4f8ff;
    color: #333333;
}

.store_header {
    box-sizing: border-box;
    padding: 20px 30px;
    background-color: #fff;
    border-radius: 5px;

    .store_header_title {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        display: flex;
        align-items: center;
    }
}

.status_text {
    display: flex;
    align-items: center;
    justify-content: center;

    .icon {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
    }
}

.store_content {
    box-sizing: border-box;
    padding: 35px 30px;
    background-color: #fff;
    border-radius: 5px;
    margin-top: 10px;
    color: #333333;

    .store_content_ps {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .store_content_header {
        display: flex;
        align-items: center;

        .title_line {
            width: 3px;
            height: 19px;
            background: #21A042;
            border-radius: 2px;
        }

        .title {
            font-size: 20px;
            font-weight: bold;
            margin-left: 10px;
        }
    }

    .store_content_header_left,
    .store_content_header_right {
        display: flex;
        align-items: center;
    }

    .task_num {
        font-weight: 600;
        font-size: 22px;
        color: #049B01;
    }

    .store_content_cont {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #333;

        .store_content_right {
            display: flex;
            align-items: center;

            .content_num_header {
                font-weight: 600;
                font-size: 50px;
                text-align: center;
            }

            .content_num_footer {
                margin-top: 10px;
                text-align: center;
            }

            .line {
                width: 4px;
                height: 45px;
                background: #A5FFBD;
                margin: 0 40px;
            }
        }
    }

    .store_content_name {
        font-weight: 600;
        font-size: 20px;
        margin-top: 20px;
    }

    .store_content_main {
        display: flex;
        align-items: center;
        margin-top: 18px;
        font-size: 14px;

        .county,
        .num {
            margin-left: 70px;
        }
    }

    .store_content_text {
        margin-top: 18px;
        font-size: 14px;
    }
}

.dialog-footer {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 20px 30px;
    background-color: #fff;
    margin-top: 10px;

    .upload_submit,
    .upload_cancel {
        box-sizing: border-box;
        width: 140px;
        height: 45px;
        font-size: 16px;
        border: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
    }

    .upload_submit {
        color: #fff;
        background-color: #21A042;
    }

    .upload_cancel {
        border: 1px solid #21A042;
        color: #21A042;
    }
}

.appeal_content {
    color: #333;
    font-size: 14px;

    .left_label {
        min-width: 90px;
        text-align: right;
        font-weight: 600;
        margin-right: 20px;
    }

    .appeal_content_header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .appeal_content_header_left,
        .appeal_content_header_right {
            display: flex;
            align-items: center;
        }
    }

    .appeal_content_choose {
        display: flex;
        align-items: center;
        margin-top: 25px;
    }

    .appeal_content_message,
    .appeal_content_image {
        display: flex;
        margin-top: 25px;
    }
}
</style>
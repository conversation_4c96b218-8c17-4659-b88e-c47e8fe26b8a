<template>
  <div class="app-container">
    <div class="from_header">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
        <!--      <el-form-item label="省" prop="provinces">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.provinces"-->
        <!--          placeholder="请输入省"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="许可证号" prop="licence">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.licence"-->
        <!--          placeholder="请输入许可证号"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <el-form-item label="商户名称" prop="merchantName">
          <el-input v-model="queryParams.merchantName" placeholder="请输入商户名称" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <!--      <el-form-item label="法人" prop="legalName">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.legalName"-->
        <!--          placeholder="请输入法人"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <el-form-item label="创建人" prop="creatBy">
          <el-input v-model="queryParams.creatBy" placeholder="请输入创建人" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="状态" prop="merchantStatue">
          <el-select v-model="queryParams.merchantStatue" placeholder="请选择状态" clearable style="width: 200px">
            <el-option label="有效" value="有效" />
            <el-option label="禁用" value="禁用" />
          </el-select>
        </el-form-item>
        <!--      <el-form-item label="联系电话" prop="phoneNumber">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.phoneNumber"-->
        <!--          placeholder="请输入联系电话"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="县区" prop="county">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.county"-->
        <!--          placeholder="请输入县区"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="县区id" prop="countyId">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.countyId"-->
        <!--          placeholder="请输入县区id"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="市场部" prop="shichangbu">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.shichangbu"-->
        <!--          placeholder="请输入市场部"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="营销线" prop="yingxiaoxian">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.yingxiaoxian"-->
        <!--          placeholder="请输入营销线"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="经营地址" prop="businessAddress">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.businessAddress"-->
        <!--          placeholder="请输入经营地址"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="经营范围" prop="businessScope">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.businessScope"-->
        <!--          placeholder="请输入经营范围"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="市场类型细分" prop="marketTypeSegment">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.marketTypeSegment"-->
        <!--          placeholder="请输入市场类型细分"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="业态" prop="yetai">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.yetai"-->
        <!--          placeholder="请输入业态"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="经营规模" prop="jingyingguimo">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.jingyingguimo"-->
        <!--          placeholder="请输入经营规模"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="商圈" prop="shangquan">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.shangquan"-->
        <!--          placeholder="请输入商圈"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="订货周期类型" prop="dinghuozhouqileixing">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.dinghuozhouqileixing"-->
        <!--          placeholder="请输入订货周期类型"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="订货日" prop="dinghuori">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.dinghuori"-->
        <!--          placeholder="请输入订货日"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="订货方式" prop="dinghuofangshi">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.dinghuofangshi"-->
        <!--          placeholder="请输入订货方式"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="结算方式" prop="jiesuanfangshi">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.jiesuanfangshi"-->
        <!--          placeholder="请输入结算方式"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="网上结算" prop="wangshangjiesuan">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.wangshangjiesuan"-->
        <!--          placeholder="请输入网上结算"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="诚信等级" prop="chengxindengji">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.chengxindengji"-->
        <!--          placeholder="请输入诚信等级"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="档位编码" prop="dangweibianma">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.dangweibianma"-->
        <!--          placeholder="请输入档位编码"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="档位" prop="dangwei">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.dangwei"-->
        <!--          placeholder="请输入档位"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="入网日期" prop="ruwangriqi">-->
        <!--        <el-date-picker clearable-->
        <!--          v-model="queryParams.ruwangriqi"-->
        <!--          type="date"-->
        <!--          value-format="YYYY-MM-DD"-->
        <!--          placeholder="请选择入网日期">-->
        <!--        </el-date-picker>-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="终端层级" prop="zhongduancengji">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.zhongduancengji"-->
        <!--          placeholder="请输入终端层级"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="终端类别" prop="zhongduanleibie">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.zhongduanleibie"-->
        <!--          placeholder="请输入终端类别"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="终端类型细分" prop="zhongduanleibiexifen">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.zhongduanleibiexifen"-->
        <!--          placeholder="请输入终端类型细分"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="雪茄烟档位" prop="xuejiadangwei">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.xuejiadangwei"-->
        <!--          placeholder="请输入雪茄烟档位"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="雪茄烟终端类型" prop="xuejiayanzhongduanleixing">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.xuejiayanzhongduanleixing"-->
        <!--          placeholder="请输入雪茄烟终端类型"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="创建人id" prop="creatId">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.creatId"-->
        <!--          placeholder="请输入创建人id"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <el-form-item label="创建时间" prop="creatTime">
          <el-date-picker clearable v-model="queryParams.creatTime" type="date" value-format="YYYY-MM-DD"
            placeholder="请选择创建时间">
          </el-date-picker>
        </el-form-item>
        <!--      <el-form-item label="更新者id" prop="updateId">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.updateId"-->
        <!--          placeholder="请输入更新者id"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="更新者" prop="updateBy">-->
        <!--        <el-input-->
        <!--          v-model="queryParams.updateBy"-->
        <!--          placeholder="请输入更新者"-->
        <!--          clearable-->
        <!--          @keyup.enter="handleQuery"-->
        <!--        />-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="更新时间" prop="updateTime">-->
        <!--        <el-date-picker clearable-->
        <!--          v-model="queryParams.updateTime"-->
        <!--          type="date"-->
        <!--          value-format="YYYY-MM-DD"-->
        <!--          placeholder="请选择更新时间">-->
        <!--        </el-date-picker>-->
        <!--      </el-form-item>-->
        <el-form-item>
          <el-button class="btn-search" icon="Search" @click="handleQuery">查询</el-button>
          <el-button class="btn-refresh" icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="from_splitpanes_container">
      <div class="from_splitpanes_container_left">
        <el-col>
          <div class="head-container">
            <el-input v-model="deptName" placeholder="请输入部门名称" clearable prefix-icon="Search"
              style="margin-bottom: 20px" />
          </div>
          <div class="head-container">
            <el-tree :data="deptOptions" :props="{ label: 'label', children: 'children' }" :expand-on-click-node="false"
              :filter-node-method="filterNode" ref="deptTreeRef" node-key="id" highlight-current default-expand-all
              @node-click="handleNodeClick" />
          </div>
        </el-col>
      </div>
      <div class="from_container">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="btn-add" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['merchant:info:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button class="btn-edit" plain icon="Edit" :disabled="single" @click="handleUpdate"
              v-hasPermi="['merchant:info:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button class="btn-delete" plain icon="Delete" :disabled="multiple" @click="handleDelete"
              v-hasPermi="['merchant:info:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button class="btn-download" plain icon="Download" @click="handleExport"
              v-hasPermi="['merchant:info:export']">导出</el-button>
          </el-col>
          <right-toolbar :showSearch="showSearch" @update:showSearch="showSearch = $event"
            @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="infoList" @selection-change="handleSelectionChange" border
          :row-class-name="tableRowClassName">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="商品编号" align="center" prop="id" width="80" />
          <!--      <el-table-column label="省" align="center" prop="provinces" />-->
          <!-- <el-table-column label="许可证号" align="center" prop="licence" width="150" :show-overflow-tooltip="true"
          resizable /> -->
          <el-table-column label="店铺名" align="center" prop="merchantName" :show-overflow-tooltip="true" resizable />
          <el-table-column label="商户负责人" align="center" prop="legalName" width="100" :show-overflow-tooltip="true"
            resizable />
          <el-table-column label="联系电话" align="center" prop="phoneNumber" :show-overflow-tooltip="true" resizable />
          <el-table-column label="状态" align="center" prop="merchantStatue" width="120" resizable>
            <template #default="scope">
              <el-switch v-model="scope.row.merchantStatue" active-value="有效" inactive-value="禁用"
                style="--el-switch-on-color: #21A042;" @change="handleStatusChange(scope.row)"
                v-hasPermi="['merchant:info:edit']" />
            </template>
          </el-table-column>
          <!-- <el-table-column label="门店照片" align="center" prop="photo" width="100" resizable>
          <template #default="scope">
            <el-image v-if="scope.row.photo" :src="getImageUrl(scope.row.photo)"
              :preview-src-list="[getImageUrl(scope.row.photo)]" fit="cover"
              style="width: 50px; height: 50px; border-radius: 4px;" preview-teleported />
            <span v-else>暂无照片</span>
          </template>
        </el-table-column> -->
          <el-table-column label="县区" align="center" prop="county" />
          <!--      <el-table-column label="县区id" align="center" prop="countyId" />-->
          <!--      <el-table-column label="市场部" align="center" prop="shichangbu" />-->
          <!--      <el-table-column label="营销线" align="center" prop="yingxiaoxian" />-->
          <!-- <el-table-column label="经营地址" align="center" prop="businessAddress" /> -->
          <!--      <el-table-column label="经营范围" align="center" prop="businessScope" />-->
          <!--      <el-table-column label="市场类型" align="center" prop="marketType" />-->
          <!--      <el-table-column label="市场类型细分" align="center" prop="marketTypeSegment" />-->
          <!--      <el-table-column label="业态" align="center" prop="yetai" />-->
          <!--      <el-table-column label="经营规模" align="center" prop="jingyingguimo" />-->
          <!--      <el-table-column label="商圈" align="center" prop="shangquan" />-->
          <!--      <el-table-column label="订货周期类型" align="center" prop="dinghuozhouqileixing" />-->
          <!--      <el-table-column label="订货日" align="center" prop="dinghuori" />-->
          <!--      <el-table-column label="订货方式" align="center" prop="dinghuofangshi" />-->
          <!--      <el-table-column label="结算方式" align="center" prop="jiesuanfangshi" />-->
          <!--      <el-table-column label="网上结算" align="center" prop="wangshangjiesuan" />-->
          <!--      <el-table-column label="诚信等级" align="center" prop="chengxindengji" />-->
          <!--      <el-table-column label="档位编码" align="center" prop="dangweibianma" />-->
          <!--      <el-table-column label="档位" align="center" prop="dangwei" />-->
          <!--      <el-table-column label="入网日期" align="center" prop="ruwangriqi" width="180">-->
          <!--        <template #default="scope">-->
          <!--          <span>{{ parseTime(scope.row.ruwangriqi, '{y}-{m}-{d}') }}</span>-->
          <!--        </template>-->
          <!--      </el-table-column>-->
          <!--      <el-table-column label="终端层级" align="center" prop="zhongduancengji" />-->
          <!--      <el-table-column label="终端类别" align="center" prop="zhongduanleibie" />-->
          <!--      <el-table-column label="终端类型细分" align="center" prop="zhongduanleibiexifen" />-->
          <!--      <el-table-column label="雪茄烟档位" align="center" prop="xuejiadangwei" />-->
          <!--      <el-table-column label="雪茄烟终端类型" align="center" prop="xuejiayanzhongduanleixing" />-->
          <!--      <el-table-column label="创建人id" align="center" prop="creatId" />-->
          <el-table-column label="创建人" align="center" prop="creatBy" :show-overflow-tooltip="true" resizable />
          <el-table-column label="创建时间" align="center" prop="creatTime" resizable>
            <template #default="scope">
              <span>{{ proxy.parseTime(scope.row.creatTime, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <!--      <el-table-column label="更新者id" align="center" prop="updateId" />-->
          <!--      <el-table-column label="更新者" align="center" prop="updateBy" />-->
          <!--      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">-->
          <!--        <template #default="scope">-->
          <!--          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>-->
          <!--        </template>-->
          <!--      </el-table-column>-->
          <!-- <el-table-column label="备注" align="center" prop="remark" width="150" :show-overflow-tooltip="true" resizable /> -->
          <el-table-column label="操作" align="center" fixed="right">
            <template #default="scope">
              <div class="table-btn">
                <el-button class="table-btn-edit" link @click="handleView(scope.row)"
                  v-hasPermi="['merchant:info:query']">查看</el-button>
                <div class="btn-line"></div>
                <el-button class="table-btn-edit" link @click="handleUpdate(scope.row)"
                  v-hasPermi="['merchant:info:edit']">修改</el-button>
                <div class="btn-line"></div>
                <el-button class="table-btn-delete" link @click="handleDelete(scope.row)"
                  v-hasPermi="['merchant:info:remove']">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page="queryParams.pageNum" :limit="queryParams.pageSize"
          @update:page="queryParams.pageNum = $event" @update:limit="queryParams.pageSize = $event"
          @pagination="getList" />
      </div>
    </div>
    <!-- 添加或修改商户信息对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="infoRef" :model="form">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="省">
            <el-input v-model="form.provinces" placeholder="请输入省" />
          </el-descriptions-item>
          <el-descriptions-item label="商户名称">
            <el-input v-model="form.merchantName" placeholder="请输入商户名称" />
          </el-descriptions-item>
          <el-descriptions-item label="许可证号">
            <el-input v-model="form.licence" placeholder="请输入许可证号" />
          </el-descriptions-item>
          <el-descriptions-item label="法人">
            <el-input v-model="form.legalName" placeholder="请输入法人" />
          </el-descriptions-item>
          <el-descriptions-item label="商户状态">
            <el-select v-model="form.merchantStatue" placeholder="请选择商户状态">
              <el-option label="有效" value="有效" />
              <el-option label="禁用" value="禁用" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            <el-input v-model="form.phoneNumber" placeholder="请输入联系电话" />
          </el-descriptions-item>
          <el-descriptions-item label="县区">
            <el-input v-model="form.county" placeholder="请输入县区" />
          </el-descriptions-item>
          <el-descriptions-item label="县区id">
            <el-input v-model="form.countyId" placeholder="请输入县区id" />
          </el-descriptions-item>
          <el-descriptions-item label="市场部">
            <el-input v-model="form.shichangbu" placeholder="请输入市场部" />
          </el-descriptions-item>
          <el-descriptions-item label="营销线">
            <el-input v-model="form.yingxiaoxian" placeholder="请输入营销线" />
          </el-descriptions-item>
          <el-descriptions-item label="经营地址" :span="2">
            <el-input v-model="form.businessAddress" placeholder="请输入经营地址" />
          </el-descriptions-item>
          <el-descriptions-item label="经营范围" :span="2">
            <el-input v-model="form.businessScope" placeholder="请输入经营范围" />
          </el-descriptions-item>
          <el-descriptions-item label="业态">
            <el-input v-model="form.yetai" placeholder="请输入业态" />
          </el-descriptions-item>
          <el-descriptions-item label="经营规模">
            <el-input v-model="form.jingyingguimo" placeholder="请输入经营规模" />
          </el-descriptions-item>
          <el-descriptions-item label="商圈">
            <el-input v-model="form.shangquan" placeholder="请输入商圈" />
          </el-descriptions-item>
          <el-descriptions-item label="订货周期类型">
            <el-input v-model="form.dinghuozhouqileixing" placeholder="请输入订货周期类型" />
          </el-descriptions-item>
          <el-descriptions-item label="订货日">
            <el-input v-model="form.dinghuori" placeholder="请输入订货日" />
          </el-descriptions-item>
          <el-descriptions-item label="订货方式">
            <el-input v-model="form.dinghuofangshi" placeholder="请输入订货方式" />
          </el-descriptions-item>
          <el-descriptions-item label="结算方式">
            <el-input v-model="form.jiesuanfangshi" placeholder="请输入结算方式" />
          </el-descriptions-item>
          <el-descriptions-item label="网上结算">
            <el-input v-model="form.wangshangjiesuan" placeholder="请输入网上结算" />
          </el-descriptions-item>
          <el-descriptions-item label="诚信等级">
            <el-input v-model="form.chengxindengji" placeholder="请输入诚信等级" />
          </el-descriptions-item>
          <el-descriptions-item label="档位编码">
            <el-input v-model="form.dangweibianma" placeholder="请输入档位编码" />
          </el-descriptions-item>
          <el-descriptions-item label="档位">
            <el-input v-model="form.dangwei" placeholder="请输入档位" />
          </el-descriptions-item>
          <el-descriptions-item label="入网日期">
            <el-date-picker clearable v-model="form.ruwangriqi" type="date" value-format="YYYY-MM-DD"
              placeholder="请选择入网日期">
            </el-date-picker>
          </el-descriptions-item>
          <el-descriptions-item label="终端层级">
            <el-input v-model="form.zhongduancengji" placeholder="请输入终端层级" />
          </el-descriptions-item>
          <el-descriptions-item label="终端类别">
            <el-input v-model="form.zhongduanleibie" placeholder="请输入终端类别" />
          </el-descriptions-item>
          <el-descriptions-item label="终端类型细分">
            <el-input v-model="form.zhongduanleibiexifen" placeholder="请输入终端类型细分" />
          </el-descriptions-item>
          <el-descriptions-item label="雪茄烟档位">
            <el-input v-model="form.xuejiadangwei" placeholder="请输入雪茄烟档位" />
          </el-descriptions-item>
          <el-descriptions-item label="雪茄烟终端类型">
            <el-input v-model="form.xuejiayanzhongduanleixing" placeholder="请输入雪茄烟终端类型" />
          </el-descriptions-item>
          <el-descriptions-item label="创建人id">
            <el-input v-model="form.creatId" placeholder="请输入创建人id" />
          </el-descriptions-item>
          <el-descriptions-item label="创建人">
            <el-input v-model="form.creatBy" placeholder="请输入创建人" />
          </el-descriptions-item>
          <el-descriptions-item label="门店照片">
            <image-upload v-model="form.photo" :limit="1" />
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            <el-date-picker clearable v-model="form.creatTime" type="date" value-format="YYYY-MM-DD"
              placeholder="请选择创建时间">
            </el-date-picker>
          </el-descriptions-item>
          <el-descriptions-item label="更新者id">
            <el-input v-model="form.updateId" placeholder="请输入更新者id" />
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看商户信息对话框 -->
    <el-dialog title="查看商户信息" v-model="viewOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="门店名称">{{ viewForm.merchantName }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ viewForm.phoneNumber }}</el-descriptions-item>
        <el-descriptions-item label="许可证号">{{ viewForm.licence }}</el-descriptions-item>
        <el-descriptions-item label="法人">{{ viewForm.legalName }}</el-descriptions-item>
        <el-descriptions-item label="商户状态">{{ viewForm.merchantStatue }}</el-descriptions-item>
        <el-descriptions-item label="县区">{{ viewForm.county }}</el-descriptions-item>
        <el-descriptions-item label="市场部">{{ viewForm.shichangbu }}</el-descriptions-item>
        <el-descriptions-item label="营销线">{{ viewForm.yingxiaoxian }}</el-descriptions-item>
        <el-descriptions-item label="经营地址" :span="2">{{ viewForm.businessAddress }}</el-descriptions-item>
        <el-descriptions-item label="经营范围" :span="2">{{ viewForm.businessScope }}</el-descriptions-item>
        <el-descriptions-item label="业态">{{ viewForm.yetai }}</el-descriptions-item>
        <el-descriptions-item label="经营规模">{{ viewForm.jingyingguimo }}</el-descriptions-item>
        <el-descriptions-item label="商圈">{{ viewForm.shangquan }}</el-descriptions-item>
        <el-descriptions-item label="订货周期类型">{{ viewForm.dinghuozhouqileixing }}</el-descriptions-item>
        <el-descriptions-item label="订货日">{{ viewForm.dinghuori }}</el-descriptions-item>
        <el-descriptions-item label="订货方式">{{ viewForm.dinghuofangshi }}</el-descriptions-item>
        <el-descriptions-item label="结算方式">{{ viewForm.jiesuanfangshi }}</el-descriptions-item>
        <el-descriptions-item label="网上结算">{{ viewForm.wangshangjiesuan }}</el-descriptions-item>
        <el-descriptions-item label="诚信等级">{{ viewForm.chengxindengji }}</el-descriptions-item>
        <el-descriptions-item label="档位编码">{{ viewForm.dangweibianma }}</el-descriptions-item>
        <el-descriptions-item label="档位">{{ viewForm.dangwei }}</el-descriptions-item>
        <el-descriptions-item label="入网日期">{{ proxy.parseTime(viewForm.ruwangriqi, '{y}-{m}-{d}')
        }}</el-descriptions-item>
        <el-descriptions-item label="终端层级">{{ viewForm.zhongduancengji }}</el-descriptions-item>
        <el-descriptions-item label="终端类别">{{ viewForm.zhongduanleibie }}</el-descriptions-item>
        <el-descriptions-item label="终端类型细分">{{ viewForm.zhongduanleibiexifen }}</el-descriptions-item>
        <el-descriptions-item label="雪茄烟档位">{{ viewForm.xuejiadangwei }}</el-descriptions-item>
        <el-descriptions-item label="雪茄烟终端类型">{{ viewForm.xuejiayanzhongduanleixing }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ viewForm.creatBy }}</el-descriptions-item>
        <el-descriptions-item label="门店照片">
          <el-image v-if="viewForm.photo" :src="getImageUrl(viewForm.photo)"
            :preview-src-list="[getImageUrl(viewForm.photo)]" fit="cover"
            style="width: 100px; height: 100px; border-radius: 4px;" preview-teleported />
          <span v-else>暂无照片</span>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ proxy.parseTime(viewForm.creatTime, '{y}-{m}-{d}')
        }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Info">
import { listInfo, getInfo, delInfo, addInfo, updateInfo, changeStatus } from "@/api/merchant/info"
import { deptTreeSelect } from "@/api/system/user"
import ImageUpload from "@/components/ImageUpload"
import { onMounted } from "vue";
import { useRouter } from 'vue-router';

const { proxy } = getCurrentInstance()
const router = useRouter();
const infoList = ref([])
const open = ref(false)
const viewOpen = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const deptOptions = ref(undefined)
const enabledDeptOptions = ref(undefined)
const deptName = ref("")
const viewForm = ref({})

const data = reactive({
  form: {
    merchantStatue: "有效"
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    provinces: null,
    licence: null,
    merchantName: null,
    legalName: null,
    merchantStatue: null,
    phoneNumber: null,
    county: null,
    countyId: null,
    shichangbu: null,
    yingxiaoxian: null,
    businessAddress: null,
    businessScope: null,
    marketType: null,
    marketTypeSegment: null,
    yetai: null,
    jingyingguimo: null,
    shangquan: null,
    dinghuozhouqileixing: null,
    dinghuori: null,
    dinghuofangshi: null,
    jiesuanfangshi: null,
    wangshangjiesuan: null,
    chengxindengji: null,
    dangweibianma: null,
    dangwei: null,
    ruwangriqi: null,
    zhongduancengji: null,
    zhongduanleibie: null,
    zhongduanleibiexifen: null,
    xuejiadangwei: null,
    xuejiayanzhongduanleixing: null,
    creatId: null,
    creatBy: null,
    photo: null,
    creatTime: null,
    updateId: null,
    updateBy: null,
    updateTime: null,
    remark: null
  }
})

const { queryParams, form } = toRefs(data)

/** 查询商户信息列表 */
function getList() {
  loading.value = true
  // 创建查询参数的副本，过滤掉空值
  const cleanParams = {}
  Object.keys(queryParams.value).forEach(key => {
    const value = queryParams.value[key]
    if (value !== null && value !== undefined && value !== '') {
      cleanParams[key] = value
    }
  })
  // 确保分页参数存在
  cleanParams.pageNum = queryParams.value.pageNum || 1
  cleanParams.pageSize = queryParams.value.pageSize || 10

  listInfo(cleanParams).then(response => {
    infoList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    provinces: null,
    licence: null,
    merchantName: null,
    legalName: null,
    merchantStatue: "有效",
    phoneNumber: null,
    county: null,
    countyId: null,
    shichangbu: null,
    yingxiaoxian: null,
    businessAddress: null,
    businessScope: null,
    marketType: null,
    marketTypeSegment: null,
    yetai: null,
    jingyingguimo: null,
    shangquan: null,
    dinghuozhouqileixing: null,
    dinghuori: null,
    dinghuofangshi: null,
    jiesuanfangshi: null,
    wangshangjiesuan: null,
    chengxindengji: null,
    dangweibianma: null,
    dangwei: null,
    ruwangriqi: null,
    zhongduancengji: null,
    zhongduanleibie: null,
    zhongduanleibiexifen: null,
    xuejiadangwei: null,
    xuejiayanzhongduanleixing: null,
    creatId: null,
    creatBy: null,
    photo: null,
    creatTime: null,
    updateId: null,
    updateBy: null,
    updateTime: null,
    remark: null
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  // 手动重置查询参数
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    provinces: null,
    licence: null,
    merchantName: null,
    legalName: null,
    merchantStatue: null,
    phoneNumber: null,
    county: null,
    countyId: null,
    shichangbu: null,
    yingxiaoxian: null,
    businessAddress: null,
    businessScope: null,
    marketType: null,
    marketTypeSegment: null,
    yetai: null,
    jingyingguimo: null,
    shangquan: null,
    dinghuozhouqileixing: null,
    dinghuori: null,
    dinghuofangshi: null,
    jiesuanfangshi: null,
    wangshangjiesuan: null,
    chengxindengji: null,
    dangweibianma: null,
    dangwei: null,
    ruwangriqi: null,
    zhongduancengji: null,
    zhongduanleibie: null,
    zhongduanleibiexifen: null,
    xuejiadangwei: null,
    xuejiayanzhongduanleixing: null,
    creatId: null,
    creatBy: null,
    photo: null,
    creatTime: null,
    updateId: null,
    updateBy: null,
    updateTime: null,
    remark: null
  }
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}
/** 查询地区下拉树结构 */
function getDeptTree() {
  deptTreeSelect().then(response => {
    deptOptions.value = response.data
    enabledDeptOptions.value = filterDisabledDept(JSON.parse(JSON.stringify(response.data)))
  })
}
/** 过滤禁用的地区 */
function filterDisabledDept(deptList) {
  return deptList.filter(dept => {
    if (dept.disabled) {
      return false
    }
    if (dept.children && dept.children.length) {
      dept.children = filterDisabledDept(dept.children)
    }
    return true
  })
}
/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true
  return data.label.indexOf(value) !== -1
}
/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.countyId = data.id
  handleQuery()
}
/** 根据名称筛选地区树 */
watch(deptName, val => {
  proxy.$refs["deptTreeRef"].filter(val)
})
/** 新增按钮操作 */
function handleAdd() {
  router.push({
    path: '/merchant/info/edit/index',
    query: {
      type: 'add'
    }
  });
}

/** 查看按钮操作 */
function handleView(row) {
  const _id = row.id
  router.push({
    path: '/merchant/info/edit/index',
    query: {
      type: 'check',
      id: _id
    }
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  const _id = row.id || ids.value
  router.push({
    path: '/merchant/info/edit/index',
    query: {
      type: 'edit',
      id: _id
    }
  });
}

/** 提交按钮 */
function submitForm() {
  // 简单验证商户名称是否为空
  if (!form.value.merchantName || form.value.merchantName.trim() === '') {
    proxy.$modal.msgError("商户名称不能为空")
    return
  }

  if (form.value.id != null) {
    updateInfo(form.value).then(response => {
      proxy.$modal.msgSuccess("修改成功")
      open.value = false
      getList()
    })
  } else {
    addInfo(form.value).then(response => {
      proxy.$modal.msgSuccess("新增成功")
      open.value = false
      getList()
    })
  }
}

const tableRowClassName = ({ row, rowIndex }) => {
  if ((rowIndex + 1) % 2 === 0) {
    return 'table-row'
  }
  return ''
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除商户信息编号为"' + _ids + '"的数据项？').then(function () {
    return delInfo(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => { })
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('merchant/info/export', {
    ...queryParams.value
  }, `info_${new Date().getTime()}.xlsx`)
}

/** 获取图片完整URL */
function getImageUrl(photo) {
  if (!photo) return ''
  const baseUrl = import.meta.env.VITE_APP_BASE_API
  // 如果已经是完整URL，直接返回
  if (photo.startsWith('http://') || photo.startsWith('https://')) {
    return photo
  }
  // 如果以/开头，直接拼接baseUrl
  if (photo.startsWith('/')) {
    return baseUrl + photo
  }
  // 否则添加/前缀再拼接
  return baseUrl + '/' + photo
}

/** 商户状态切换 */
function handleStatusChange(row) {
  let text = row.merchantStatue === "有效" ? "有效" : "禁用"
  proxy.$modal.confirm('确认要"' + text + '""' + row.merchantName + '"商户吗？').then(function () {
    return changeStatus({
      id: row.id,
      merchantStatue: row.merchantStatue
    })
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess(text + "成功")
  }).catch(function () {
    row.merchantStatue = row.merchantStatue === "有效" ? "禁用" : "有效"
  })
}
onMounted(() => {
  getDeptTree()
  getList()
})
</script>

<template>
    <div class="app-container">
        <div class="store_header">
            <div class="store_header_title">
                <div>{{ editType == 'add' ? '新增商户' : editType == 'edit' ? '编辑商户' : '查看商户' }}</div>
            </div>
        </div>
        <div class="form_content" v-if="editType == 'edit' || editType == 'add'">
            <el-form ref="assetRef" inline :model="form" :rules="rules" class="basic_from" label-width="auto">
                <p class="photo">门店照片</p>
                <div class="form_content_header">
                    <image-upload v-model="form.photo" />
                    <div class="form_content_input">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="店铺名称" prop="merchantName">
                                    <el-input style="width: 320px;" v-model="form.merchantName" placeholder="请输入店铺名称" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="手机号" prop="phoneNumber">
                                    <el-input style="width: 320px;" v-model="form.phoneNumber" placeholder="请输入手机号" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="状态" prop="merchantStatue">
                                    <el-switch v-model="form.merchantStatue" active-value="启用" inactive-value="禁用"
                                        class="ml-2" style="--el-switch-on-color: #21A042;" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="许可证号" prop="licence">
                                    <el-input style="width: 320px;" v-model="form.licence" placeholder="请输入许可证号" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="坐标" prop="businessAddress">
                                    <el-input style="width: 130px;" v-model="form.originalLongitude" placeholder="经度" />
                                </el-form-item>
                                <el-form-item prop="businessAddress">
                                    <el-input style="width: 130px;" v-model="form.originalLatitude" placeholder="纬度" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="偏移坐标" prop="businessAddress">
                                    <el-input style="width: 130px;" v-model="form.longitudeAfterOffset"
                                        placeholder="经度" />
                                </el-form-item>
                                <el-form-item prop="businessAddress">
                                    <el-input style="width: 130px;" v-model="form.latitudeAfterOffset"
                                        placeholder="纬度" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="商户负责人" prop="legalName">
                                    <el-input style="width: 320px;" v-model="form.legalName" placeholder="请输入商户负责人" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="16">
                                <el-form-item label="所属地区" prop="countyId">
                                    <el-select v-model="form.countyId" placeholder="请输入所属地区" style="width: 320px"
                                        @change="chooseCounty">
                                        <el-option v-for="item in deptOptions" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="诚信等级" prop="chengxindengji">
                                    <el-select v-model="form.chengxindengji" placeholder="请选择诚信等级" style="width: 150px">
                                        <el-option v-for="item in merchant_cxdj" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                    <div style="margin-left: 10px;">级</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="经营规模" prop="jingyingguimo">
                                    <el-select v-model="form.jingyingguimo" placeholder="请选择经营规模" style="width: 150px">
                                        <el-option v-for="item in merchant_jygm" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="终端类型" prop="zhongduancengji">
                                    <el-select v-model="form.zhongduancengji" placeholder="请选择终端类型"
                                        style="width: 150px">
                                        <el-option v-for="item in merchant_zdlx" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </div>
                <el-row>
                    <el-form-item label="详细地址" prop="address" style="width: 100%;">
                        <el-input style="width: 100%;" v-model="form.address" placeholder="请输入详细地址" />
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="备注" prop="remark" style="width: 100%;">
                        <el-input type="textarea" style="width: 100%;" v-model="form.remark" placeholder="请输入备注"
                            :autosize="{ minRows: 2 }" />
                    </el-form-item>
                </el-row>
            </el-form>
        </div>
        <div class="form_content" v-else>
            <el-form ref="assetRef" inline :model="form" :rules="rules" class="basic_from" label-width="auto">
                <p class="photo">门店照片</p>
                <div class="form_content_header">
                    <img class="form_content_image" :src="form.photo" alt="">
                    <div class="form_content_input">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="店铺名称" prop="merchantName">
                                    <div>{{ form.merchantName }}</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="手机号" prop="phoneNumber">
                                    <div>{{ form.phoneNumber }}</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="状态" prop="merchantStatue">
                                    <div>{{ form.merchantStatue }}</div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="许可证号" prop="licence">
                                    <div>{{ form.licence }}</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="坐标" prop="businessAddress">
                                    <div>{{ form.originalLongitude }}</div>
                                </el-form-item>
                                <el-form-item prop="businessAddress">
                                    <div>{{ form.originalLatitude }}</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="偏移坐标" prop="businessAddress">
                                    <div>{{ form.longitudeAfterOffset }}</div>
                                </el-form-item>
                                <el-form-item prop="businessAddress">
                                    <div>{{ form.latitudeAfterOffset }}</div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="商户负责人" prop="legalName">
                                    <div>{{ form.legalName }}</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="16">
                                <el-form-item label="所属地区" prop="countyId">
                                    <div>{{ form.county }}</div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="诚信等级" prop="chengxindengji">
                                    <div>{{ form.chengxindengji }}</div>
                                    <div style="margin-left: 10px;">级</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="经营规模" prop="jingyingguimo">
                                    <div>{{ form.jingyingguimo }}</div>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="终端类型" prop="zhongduancengji">
                                    <div>{{ form.zhongduancengji }}</div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </div>
                <el-row>
                    <el-form-item label="详细地址" prop="address" style="width: 100%;">
                        <div>{{ form.address }}</div>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="备注" prop="remark" style="width: 100%;">
                        <div>{{ form.remark }}</div>
                    </el-form-item>
                </el-row>
            </el-form>
        </div>
        <div class="dialog-footer">
            <el-button class="upload_submit" @click="submitForm">确 定</el-button>
            <el-button class="upload_cancel" @click="cancel">取 消</el-button>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { getInfo, addInfo, updateInfo } from "@/api/merchant/info"
import ImageUpload from "@/components/ImageUpload"
import { deptTreeSelect } from "@/api/system/user"
const { proxy } = getCurrentInstance()
const { merchant_jygm, merchant_cxdj, merchant_zdlx } = proxy.useDict("merchant_jygm", "merchant_cxdj", "merchant_zdlx")
const route = useRoute();
const editType = route.query.type
const isId = route.query.id || null;
const deptOptions = ref([])
const form = ref({})
const rules = ref({
    merchantName: [{ required: true, message: "店铺名称不能为空", trigger: "blur" }],
    licence: [{ required: true, message: "许可证号不能为空", trigger: "blur" }],
    legalName: [{ required: true, message: "商户负责人不能为空", trigger: "blur" }],
    countyId: [{ required: true, message: "所属地区不能为空", trigger: "select" }],
    chengxindengji: [{ required: true, message: "诚信等级不能为空", trigger: "select" }],
    jingyingguimo: [{ required: true, message: "经营规模不能为空", trigger: "select" }],
    zhongduancengji: [{ required: true, message: "终端层级不能为空", trigger: "select" }],
})

onMounted(() => {
    if (editType === 'edit' || editType === 'check') {
        handleView(isId)
    }
    getDeptTree()
})
const chooseCounty = (value) => {
    const label = deptOptions.value.find(item => item.value === value)?.label || '';
    form.value.county = label
}
/** 查询地区下拉树结构 */
function getDeptTree() {
    deptTreeSelect().then(response => {
        let list = []
        response.data.forEach(item => {
            if (item.children && item.children.length > 0) {
                item.children.forEach(child => {
                    list.push({
                        value: child.id,
                        label: child.label
                    })
                })
            }
        })
        deptOptions.value = list
    })
}
// 编辑
function handleView(id) {
    getInfo(id).then(response => {
        form.value = response.data
    })
}
const submitForm = () => {
    proxy.$refs["assetRef"].validate(valid => {
        if (valid) {
            if (editType == 'edit' || editType == 'check') {
                updateInfo(form.value).then(response => {
                    proxy.$modal.msgSuccess("修改成功")
                    cancel()
                })
            } else {
                addInfo(form.value).then(response => {
                    proxy.$modal.msgSuccess("新增成功")
                    cancel()
                })
            }
        }
    })
}

const cancel = () => {
    const obj = { path: "/merchant/info" }
    proxy.$tab.closeOpenPage(obj)
}
</script>

<style lang="scss" scoped>
.app-container {
    background-color: #f4f8ff;
    color: #333333;
}

.store_header {
    box-sizing: border-box;
    padding: 20px 30px;
    background-color: #fff;
    border-radius: 5px;

    .store_header_title {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        display: flex;
        align-items: center;
    }
}

.form_content {
    margin-top: 10px;
    padding: 20px 30px;
    background-color: #fff;
    border-radius: 5px;

    .form_content_header {
        display: flex;

        .form_content_image {
            width: 175px;
            height: 177px;
            background: #F5F5F5;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
    }

    .photo {
        font-size: 14px;
        color: #333333;
    }

    :deep(.el-form) {
        .el-form-item {
            margin-bottom: 23px;
        }

        .el-form-item__label {
            color: #333;
        }
    }

    :deep(.el-input) {
        .el-input__wrapper.is-focus {
            box-shadow: 0 0 0 1px #21a042 !important;
        }
    }

    :deep(.el-select) {

        .el-select__wrapper {
            .el-icon {
                color: #00b578;
            }
        }

        .el-select__wrapper.is-focused {
            box-shadow: 0 0 0 1px #21a042 !important;
        }
    }

    .form_content_input {
        margin-left: 30px;
        flex: 1;
    }
}

.upload_submit,
.upload_cancel {
    box-sizing: border-box;
    width: 140px;
    height: 45px;
    font-size: 16px;
    border: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
}

.dialog-footer {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 20px 30px;
    background-color: #fff;
    margin-top: 10px;

    .upload_submit {
        color: #fff;
        background-color: #21A042;
    }

    .upload_cancel {
        border: 1px solid #21A042;
        color: #21A042;
    }
}
</style>
<template>
  <div class="login">
    <div class="login_title">
      {{ title }}
    </div>
    <div class="login_from_main">
      <div class="login_from_content">
        <div class="login_from_bg">
          <el-image style="width: 350px;" :src="loginLogo" fit="fill" />
        </div>
        <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
          <div class="wel_come">欢迎登录</div>
          <div class="title">{{ title }}</div>
          <el-form-item prop="username">
            <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off" placeholder="请输入账户">
              <template #prefix><img src="../assets/images/login/login-user.png" class="input-icon"></template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input v-model="loginForm.password" type="password" size="large" auto-complete="off" placeholder="请输入密码"
              @keyup.enter="handleLogin">
              <template #prefix><img src="../assets/images/login/login-clock.png" class="input-icon"></template>
            </el-input>
          </el-form-item>
          <el-form-item prop="code" v-if="captchaEnabled">
            <el-input v-model="loginForm.code" size="large" auto-complete="off" placeholder="请输入图片验证码"
              style="width: 63%" @keyup.enter="handleLogin">
              <template #prefix><img src="../assets/images/login/login-code.png" class="input-icon"></template>
            </el-input>
            <div class="login-code">
              <img :src="codeUrl" @click="getCode" class="login-code-img" />
            </div>
          </el-form-item>
          <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
          <el-form-item style="width:100%;">
            <el-button class="login-btn" :loading="loading" size="large" type="primary" style="width:100%;"
              @click.prevent="handleLogin">
              <span v-if="!loading">登录</span>
              <span v-else>登 录 中...</span>
            </el-button>
            <div style="float: right;" v-if="register">
              <router-link style="" class="link-type" :to="'/register'">立即注册</router-link>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!--  底部  -->
    <!-- <div class="el-login-footer">
      <span>Copyright © 2018-2025 hz All Rights Reserved.</span>
    </div> -->
  </div>
</template>

<script setup>
import { getCodeImg } from "@/api/login"
import Cookies from "js-cookie"
import { encrypt, decrypt } from "@/utils/jsencrypt"
import useUserStore from '@/store/modules/user'
import loginLogo from "@/assets/images/login/login-logo.png"
const title = import.meta.env.VITE_APP_TITLE
const userStore = useUserStore()
const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

const loginForm = ref({
  username: "admin",
  password: "admin123",
  rememberMe: false,
  code: "",
  uuid: ""
})

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }]
}

const codeUrl = ref("")
const loading = ref(false)
// 验证码开关
const captchaEnabled = ref(true)
// 注册开关
const register = ref(false)
const redirect = ref(undefined)

watch(route, (newRoute) => {
  redirect.value = newRoute.query && newRoute.query.redirect
}, { immediate: true })

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 30 })
        Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 })
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 })
      } else {
        // 否则移除
        Cookies.remove("username")
        Cookies.remove("password")
        Cookies.remove("rememberMe")
      }
      // 调用action的登录方法
      userStore.login(loginForm.value).then(() => {
        const query = route.query
        const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
          if (cur !== "redirect") {
            acc[cur] = query[cur]
          }
          return acc
        }, {})
        router.push({ path: redirect.value || "/", query: otherQueryParams })
      }).catch(() => {
        loading.value = false
        // 重新获取验证码
        if (captchaEnabled.value) {
          getCode()
        }
      })
    }
  })
}

function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img
      loginForm.value.uuid = res.uuid
    }
  })
}

function getCookie() {
  const username = Cookies.get("username")
  const password = Cookies.get("password")
  const rememberMe = Cookies.get("rememberMe")
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  }
}

getCode()
getCookie()
</script>

<style lang='scss' scoped>
.login {
  height: 100%;
  background-image: url("../assets/images/login/login-bg.png");
  background-size: cover;

  .login_title {
    font-size: 50px;
    color: #ffffff;
    font-weight: bold;
    text-align: center;
    padding-top: 80px;
    text-shadow: 0 2px 8px #2B2F2E;
    letter-spacing: 3px;
  }
}

.wel_come {
  font-weight: 700;
  font-size: 26px;
  color: #323232;
  letter-spacing: 1px;
}

.title {
  color: #666666;
  font-weight: bold;
  margin-top: 10px;
  letter-spacing: 1px;
  margin-bottom: 20px;
}

.login_from_main {
  display: flex;
  justify-content: center;
  margin-top: 80px;
  }

.login_from_content {
  display: flex;
  width: 900px;
  border-radius: 0 10px 10px 0;
  overflow: hidden;

  .login_from_bg {
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #008160;
  }
}

.login-form {
  background: #ffffff;
  width: 400px;
  padding: 45px 35px 30px 35px;
  flex: 1;

  .el-input {
    height: 50px;

    input {
      height: 50px;
    }
  }

  .input-icon {
    height: 14px;
    width: 12px;
    margin-left: 0px;
    color: #008160;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 40px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.login-btn {
  height: 50px;
  font-size: 18px;
  background: #049B01;
  margin-top: 10px;
  box-shadow: 0 1px 3px 0 #00b57891;
  border-radius: 40px;
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 40px;
  padding-left: 12px;
}
</style>

@import './variables.module.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';
@import './hz.scss';

body {
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ' ';
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(
    90deg,
    rgba(32, 182, 249, 1) 0%,
    rgba(32, 182, 249, 1) 0%,
    rgba(33, 120, 241, 1) 100%,
    rgba(33, 120, 241, 1) 100%
  );

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

// 通用样式

.el-form-item__label {
  color: #333;
}
.el-input-number {
  .el-icon {
    color: #00b578;
  }
}
.el-year-table td .el-date-table-cell__text:hover,
.el-year-table td.today .el-date-table-cell__text,
.el-select-dropdown__item.is-selected {
  color: #21a042;
}
.el-year-table td.current:not(.disabled) .el-date-table-cell__text {
  background-color: #21a042;
}
.el-upload:focus,
.el-upload:focus .el-upload-dragger,
.el-upload-dragger:hover,
.el-checkbox__inner:hover {
  border-color: #21a042;
}
.el-input-number__decrease:hover
  ~ .el-input:not(.is-disabled)
  .el-input__wrapper,
.el-input-number__increase:hover
  ~ .el-input:not(.is-disabled)
  .el-input__wrapper,
.el-textarea__inner:focus {
  box-shadow: 0 0 0 1px #21a042 !important;
}
.el-select__wrapper,
.el-input__wrapper {
  .el-icon {
    color: #00b578;
  }
}
.el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px #21a042 !important;
}
.el-select__wrapper.is-focused {
  box-shadow: 0 0 0 1px #21a042 !important;
}
.el-radio__input.is-checked .el-radio__inner,
.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #21a042;
  border-color: #21a042;
}
.el-radio__inner {
  border-color: #21a042;
}
.el-radio.is-checked .el-radio__label {
  color: #21a042 !important;
}
.el-checkbox__inner {
  border-color: #21a042 !important;
}
.el-radio__inner:hover {
  border-color: #21a042;
}
.el-checkbox__input.is-indeterminate .el-checkbox__inner,
.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #21a042 !important;
  border-color: #21a042 !important;
}
.el-checkbox__input.is-checked + .el-checkbox__label {
  color: #21a042 !important;
}
.from_header {
  box-sizing: border-box;
  padding: 15px 20px 0px 20px;
  background-color: #fff;
  border-radius: 5px;

  .from_header_title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
  }
  .el-input,
  .el-select {
    width: 220px;
  }
  .el-select__wrapper,
  .el-input__wrapper {
    box-shadow: 0 0 0 0 !important;
    background-color: #f7f8fa;
    .el-icon {
      color: #00b578;
    }
  }
}
.btn-search {
  color: #fff;
  border: 0;
  background: #049b01;
}

.btn-search:hover {
  color: #fff;
  background: #049b01;
}

.btn-clear {
  color: #fff;
  border: 0;
  background: #ff3232;
}

.btn-clear:hover {
  color: #fff;
  background: #ff3232;
}

.btn-refresh {
  color: #333;
  border: 0;
  background: #f7f8fa;
}

.btn-refresh:hover {
  color: #333;
  background: #f7f8fa;
}

.from_splitpanes_container {
  display: flex;
  justify-content: space-between;
  .from_container {
    width: 84.5%;
  }
  .el-input {
    height: 38px;
    .el-input__inner {
      color: #333;
    }
    .el-icon {
      font-size: 16px;
      color: #333;
    }
  }
  .from_splitpanes_container_left {
    width: 15%;
    padding: 10px;
    margin-top: 10px;
    min-height: calc(100vh - 250px);
    border-radius: 5px;
    background-color: #fff;

    .el-tree-node__content {
      height: 100%;
      padding: 8px 0 8px 20px !important;
      border-radius: 4px;
      color: #333;
    }
    .el-tree-node__children {
      .el-tree-node__content {
        padding-left: 30px !important;
      }
    }
    .el-tree--highlight-current
      .el-tree-node.is-current
      > .el-tree-node__content {
      background-color: #21a042;
      .el-text {
        color: #fff;
      }
      .el-icon {
        color: #fff;
      }
    }
  }
}
.from_container {
  box-sizing: border-box;
  padding: 15px 20px 15px 20px;
  margin-top: 10px;
  min-height: calc(100vh - 250px);
  background-color: #fff;

  .table-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    .btn-line {
      width: 2px;
      height: 12px;
      margin: 0 3px;
      background-color: #21a042;
    }
    .table-btn-edit {
      color: #21a042;
    }
    .table-btn-delete {
      color: #f40c0c;
    }
    .table-btn-import {
      color: #ff9900;
    }
    .table-btn-data {
      color: #ff0088;
    }
  }

  .el-table {
    color: #333;
    margin-top: 30px;
  }

  .table-row {
    background-color: #fafafa !important;
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
    background-color: transparent !important;
  }

  .table-active {
    color: #fff;
    background-color: #049b01 !important;
  }

  .el-table .el-table__header-wrapper th {
    line-height: 35px;
    color: #333;
    background-color: #f2f3f5 !important;
  }

  .el-pagination.is-background .el-pager li {
    background-color: #fff;
    border: 1px solid #eeeeee;
  }
  .el-pagination__total,
  .el-pagination__jump {
    color: #333;
  }
  .btn-next,
  .btn-prev {
    background-color: #fff !important;
    border: 1px solid #eeeeee !important;
  }
  .el-pagination.is-background .el-pager li.is-active {
    background: #21a042;
    box-shadow: 0 1px 3px 0 #00b57891;
    border-radius: 5px;
    border: 0;
  }
}

.btn-add {
  color: #21a042;
  background-color: rgba(33, 160, 66, 0.1);
  border: 1px solid #21a042;
}

.btn-add:hover {
  color: #21a042;
  background-color: rgba(33, 160, 66, 0.1);
  border: 1px solid #21a042;
}

.btn-edit {
  color: #165dff;
  background-color: rgba(22, 93, 255, 0.1);
  border: 1px solid #165dff;
}

.btn-edit:hover {
  color: #165dff;
  background-color: rgba(22, 93, 255, 0.1);
  border: 1px solid #165dff;
}

.btn-delete {
  color: #ff1616;
  background-color: rgba(255, 22, 22, 0.1);
  border: 1px solid #ff1616;
}

.btn-delete:hover {
  color: #ff1616;
  background-color: rgba(255, 22, 22, 0.1);
  border: 1px solid #ff1616;
}

.btn-import {
  color: #ff9900;
  background: rgba(255, 161, 22, 0.1);
  border: 1px solid #ff9b00;
}

.btn-import:hover {
  color: #ff9900;
  background: rgba(255, 161, 22, 0.1);
  border: 1px solid #ff9b00;
}

.btn-data {
  color: #ff0088;
  background: rgba(250, 41, 152, 0.1);
  border: 1px solid #ff0088;
}

.btn-data:hover {
  color: #ff0088;
  background: rgba(250, 41, 152, 0.1);
  border: 1px solid #ff0088;
}

.btn-download {
  color: #008e7e;
  background: rgba(0, 190, 169, 0.1);
  border: 1px solid #00bea9;
}

.btn-download:hover {
  color: #008e7e;
  background: rgba(0, 190, 169, 0.1);
  border: 1px solid #00bea9;
}

.dialog_component {
  padding: 30px 40px;
  border-radius: 10px;

  .el-dialog__header {
    display: none;
  }
  .dialog_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 25px;
    .header_left {
      display: flex;
      align-items: center;
      color: #333333;
      font-weight: 600;
      font-size: 18px;
      letter-spacing: 1px;
      img {
        width: 38px;
        height: 38px;
        margin-right: 6px;
      }
    }
    .dialog_close {
      font-size: 18px;
      color: #444;
      cursor: pointer;
    }
  }
  .dialog_component_not_margin {
    margin-right: 0;
  }
  .dialog_submit {
    color: #fff;
    border-radius: 2.4px;
    background-color: #21a042;
    border: 0;
  }
  .dialog_cancel {
    color: #333;
    border-radius: 2.4px;
    background-color: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.15);
  }
}

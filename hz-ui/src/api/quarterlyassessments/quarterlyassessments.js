import request from '@/utils/request';

// 查询季度考核列表
export function listQuarterlyassessments(query) {
  return request({
    url: '/quarterlyassessments/quarterlyassessments/list',
    method: 'get',
    params: query,
  });
}

// 查询季度考核详细
export function getQuarterlyassessments(id) {
  return request({
    url: '/quarterlyassessments/quarterlyassessments/' + id,
    method: 'get',
  });
}

// 新增季度考核
export function addQuarterlyassessments(data) {
  return request({
    url: '/quarterlyassessments/quarterlyassessments',
    method: 'post',
    data: data,
  });
}

// 修改季度考核
export function updateQuarterlyassessments(data) {
  return request({
    url: '/quarterlyassessments/quarterlyassessments',
    method: 'put',
    data: data,
  });
}

// 更新月度考核人员
export function updateMonthlyAssessmentStaff(data) {
  return request({
    url: '/quarterlyassessments/staff/monthly',
    method: 'put',
    data: data,
  });
}

// 删除季度考核
export function delQuarterlyassessments(id) {
  return request({
    url: '/quarterlyassessments/quarterlyassessments/' + id,
    method: 'delete',
  });
}

// 查询现场考核
export function paperMonthlyId(monthlyId) {
  return request({
    url: '/examination/paper/monthlyId?monthlyId=' + monthlyId,
    method: 'get',
  });
}

// 保存抽取的商户到月度考核
export function saveSelectedMerchants(data) {
  return request({
    url: '/quarterlyassessments/monthlyMerchant/saveSelectedMerchants',
    method: 'post',
    data: data,
  });
}

// 根据月度考核ID查询关联的商户列表
export function listMerchantsByMonthlyId(monthlyAssessmentId) {
  return request({
    url:
      '/quarterlyassessments/monthlyMerchant/listByMonthlyId/' +
      monthlyAssessmentId,
    method: 'get',
  });
}

// 统计月度考核关联的商户数量
export function countMerchantsByMonthlyId(monthlyAssessmentId) {
  return request({
    url: '/quarterlyassessments/monthlyMerchant/count/' + monthlyAssessmentId,
    method: 'get',
  });
}

// 获取月度考核详情（包括人员配置和商户数据）
export function getMonthlyAssessmentDetail(monthlyAssessmentId) {
  return request({
    url:
      '/quarterlyassessments/monthlyAssessment/detail/' + monthlyAssessmentId,
    method: 'get',
  });
}

export function saveSelectedStaffAndMerchants(data) {
  return request({
    url: '/quarterlyassessments/quarterlyassessments/saveSelectedStaffAndMerchants',
    method: 'post',
    data: data,
  });
}

// 根据月份查询商户考核数据（精简字段）
export function getMerchantAssessmentsSummaryByMonth(month, year) {
  return request({
    url:
      '/quarterlyassessments/monthlyMerchant/summaryByMonth/' +
      month +
      (year ? '?year=' + year : ''),
    method: 'get',
  });
}

// 根据月份查询商户考核数据（精简字段 + 分页）
export function getMerchantAssessmentsSummaryByMonthWithPage(params) {
  return request({
    url: '/quarterlyassessments/monthlyMerchant/summaryByMonth',
    method: 'get',
    params: params,
  });
}

// 保存题型
export function topicBatch(id, data) {
  return request({
    url: '/examination/topic/batch/' + id,
    method: 'post',
    data: data,
  });
}

// 查询题型
export function replyByPaperId(params) {
  return request({
    url: '/examination/topic/ReplyByPaperId',
    method: 'get',
    params: params,
  });
}

// 查询模板
export function listByTypeAndBrand(params) {
  return request({
    url: '/examination/paper/listByTypeAndBrand',
    method: 'get',
    params: params,
  });
}

// 商户成绩
export function summaryByMonthlyId(params) {
  return request({
    url: '/quarterlyassessments/monthlyMerchant/summaryByMonthlyId',
    method: 'get',
    params: params,
  });
}

// 发布
export function release(id) {
  return request({
    url: 'quarterlyassessments/quarterlyassessments/release/' + id,
    method: 'post',
  });
}

// 撤销发布
export function unRelease(id) {
  return request({
    url: 'quarterlyassessments/quarterlyassessments/unRelease/' + id,
    method: 'post',
  });
}

// 查询商户题库
export function ReplyMerchantByPaperId(params) {
  return request({
    url: '/examination/topic/ReplyMerchantByPaperId',
    method: 'get',
    params,
  });
}

// 保存商户题库
export function paperBatch(data, paperId, merchantId) {
  return request({
    url: '/examination/topic/paperBatch/' + paperId + '/' + merchantId,
    method: 'post',
    data,
  });
}

// 申诉
export function messageAppeal(data) {
  return request({
    url: '/system/message',
    method: 'post',
    data,
  });
}

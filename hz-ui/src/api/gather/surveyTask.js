import request from '@/utils/request'

// 查询调研任务列表
export function listTask(query){
    return request({
        url: '/gather/surveyTask/list',
        method: 'get',
        params: query
    })
}

// 查询调研任务详细
export function getTask(id){
    return request({
        url: '/gather/surveyTask/' + id,
        method: 'get'
    })
}

// 新增调研任务
export function addTask(data){
    return request({
        url: '/gather/surveyTask',
        method: 'post',
        data: data
    })
}

// 修改调研任务
export function updateTask(data){
    return request({
        url: '/gather/surveyTask',
        method: 'put',
        data: data
    })
}

// 删除调研任务
export function delTask(id){
    return request({
        url: '/gather/surveyTask/' + id,
        method: 'delete'
    })
}

// 文件上传
export function uploadFile(data){
    return request({
        url: '/gather/surveyTask/upload' ,
        method: 'post',
        data: data,
        headers: {'Content-Type': 'multipart/form-data'},
    })
}

// 文件上传
export function statisticTableData(data){
    return request({
        url: '/gather/surveyTask/statisticTableData' ,
        method: 'post',
        data: data,
        headers: {'Content-Type': 'multipart/form-data'},
    })
}

// 按规则清理数据
export function ruleCleanData(data){
  return request({
    url: '/gather/surveyTask/ruleCleanData' ,
    method: 'post',
    data: data,
  })
}


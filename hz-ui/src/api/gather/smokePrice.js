import request from '@/utils/request'

// 查询卷烟价格列表
export function listGatherSmokePrice(query){
    return request({
        url: '/gather/smokePrice/list',
        method: 'get',
        params: query
    })
}

// 查询卷烟价格详细
export function getGatherSmokePrice(id){
    return request({
        url: '/gather/smokePrice/' + id,
        method: 'get'
    })
}

// 新增卷烟价格
export function addGatherSmokePrice(data){
    return request({
        url: '/gather/smokePrice',
        method: 'post',
        data: data
    })
}

// 修改卷烟价格
export function updateGatherSmokePrice(data){
    return request({
        url: '/gather/smokePrice',
        method: 'put',
        data: data
    })
}

// 删除卷烟价格
export function delGatherSmokePrice(id){
    return request({
        url: '/gather/smokePrice/' + id,
        method: 'delete'
    })
}

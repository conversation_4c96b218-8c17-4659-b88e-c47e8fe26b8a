import request from '@/utils/request'

// 查询香烟数据清理规则列表
export function listRules(query) {
  return request({
    url: '/gather/cleanRules/list',
    method: 'get',
    params: query
  })
}

// 查询香烟数据清理规则列表
export function listAllRules(query) {
  return request({
    url: '/gather/cleanRules/listAll',
    method: 'get',
    params: query
  })
}

// 查询香烟数据清理规则详细
export function getRules(id) {
  return request({
    url: '/gather/cleanRules/' + id,
    method: 'get'
  })
}

// 新增香烟数据清理规则
export function addRules(data) {
  return request({
    url: '/gather/cleanRules',
    method: 'post',
    data: data
  })
}

// 修改香烟数据清理规则
export function updateRules(data) {
  return request({
    url: '/gather/cleanRules',
    method: 'put',
    data: data
  })
}

// 删除香烟数据清理规则
export function delRules(id) {
  return request({
    url: '/gather/cleanRules/' + id,
    method: 'delete'
  })
}

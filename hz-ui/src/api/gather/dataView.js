import request from '@/utils/request'

// 累计的
export function leijApi(query){
  return request({
    url: '/gather/dataView/leij',
    method: 'get',
    params: query
  })
}

// 该次的（卷烟列表）
export function gaicSmokePrice(query){
  return request({
    url: '/gather/dataView/gaicSmokePrice',
    method: 'get',
    params: query
  })
}

// 该次的
export function gaicApi(query){
  return request({
    url: '/gather/dataView/gaic',
    method: 'get',
    params: query
  })
}

// 该次的(柱状图)
export function gaicZztApi(query){
  return request({
    url: '/gather/dataView/gaicZzt',
    method: 'get',
    params: query
  })
}
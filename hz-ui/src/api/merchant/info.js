import request from '@/utils/request'

// 查询商户信息列表
export function listInfo(query) {
  return request({
    url: '/merchant/info/list',
    method: 'get',
    params: query
  })
}

// 查询商户信息详细
export function getInfo(id) {
  return request({
    url: '/merchant/info/' + id,
    method: 'get'
  })
}

// 新增商户信息
export function addInfo(data) {
  return request({
    url: '/merchant/info',
    method: 'post',
    data: data
  })
}

// 修改商户信息
export function updateInfo(data) {
  return request({
    url: '/merchant/info',
    method: 'put',
    data: data
  })
}

// 删除商户信息
export function delInfo(ids) {
  return request({
    url: '/merchant/info/' + ids,
    method: 'delete'
  })
}

// 切换商户状态
export function changeStatus(data) {
  return request({
    url: '/merchant/info/changeStatus',
    method: 'put',
    data: data
  })
}

// 智能抽取考核商户
export function selectMerchantsForAssessment(data) {
  return request({
    url: '/merchant/info/selectForAssessment',
    method: 'post',
    data: data
  })
}

export function selectMerchants(data) {
  return request({
    url: '/merchant/info/selectMerchants',
    method: 'post',
    data: data,
  });
}

// 根据条件抽取商户
export function selectMerchantsByConditions(data) {
  return request({
    url: '/merchant/info/selectByConditions',
    method: 'post',
    data: data
  })
}

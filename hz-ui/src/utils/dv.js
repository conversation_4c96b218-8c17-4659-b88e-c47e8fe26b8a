import * as echarts from 'echarts';

//--------------------------------------------------------------- 配置管理 ---------------------------------------------------------------------

// 地图配置
export function dtOption(obj){
  let nums = []
  obj.data.forEach(item => {
    nums.push(item.value[2])
  })
  const max = Math.max(...nums)
  return {
    visualMap: {
      type: 'piecewise',
      min: 0,
      max: max,
      inRange: { color: ['#38d799', '#ffc35e', '#fab726', '#cf4813', '#ff0014'] },
      textStyle: { color: '#FFFFFF' },
      bottom: 20,
      right: 20
    },
    geo: [
      {
        map: 'puyang',
        roam: true,
        zoom: 1.2,
        zlevel: 5,
        label: {show: true, color: '#FFFFFF'},
        itemStyle: {
          color: '#01294e', // 背景
          borderWidth: '1', // 边框宽度
          borderColor: '#38abf1', // 边框颜色
        }
      },
      {
        map: 'puyang',
        roam: true,
        zoom: 1.2,
        top: '11%',
        zlevel: 4,
        itemStyle: {
          color: '#38abf1', // 背景
          borderWidth: '1', // 边框宽度
          borderColor: '#38abf1', // 边框颜色
        }
      },
      {
        map: 'puyang',
        roam: true,
        zoom: 1.2,
        top: '13%',
        zlevel: 3,
        itemStyle: {
          color: '#2f738e', // 背景
          borderWidth: '1', // 边框宽度
          borderColor: '#2f738e', // 边框颜色
        }
      },
    ],
    series: [{
      zlevel: 6,
      name: '城市热力',
      type: 'heatmap',
      universalTransition: {
        enabled: true,
      },
      coordinateSystem: 'geo',
      data: obj.data,
      pointSize: 15,
      blurSize: 20,
      minOpacity: 0.3,
      maxOpacity: 0.8
    }]
  };
}

// 箱线图配置（累计，价格）
export function xxtOptionByLjJg(flag, obj) {
  return {
    grid: {
      left: '1%',  // 左边距
      right: '1%', // 右边距
      top: '6%',
      bottom: '6%', // 下边距
      containLabel: true
    },
    tooltip: {
      confine: true,
      trigger: 'item',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params, ticket) => {
        if(params.componentSubType && params.componentSubType === 'boxplot'){
          let view = '<div style="font-size: 14px; width: 200px;">'
          view = view + '<div>'+params.name+'</div>'

          view = view + '<div><span >置信区间：</span><span>' + params.value[2].toFixed(2) + ' 至 ' + params.value[4].toFixed(2) + '</span></div>'
          view = view + '<div><span >平均值：</span><span>' + params.value[3].toFixed(2) + '</span></div>'
          view = view + '<div><span >最小值：</span><span>' + params.value[1].toFixed(2) + '</span></div>'
          view = view + '<div><span >最大值：</span><span>' + params.value[5].toFixed(2) + '</span></div>'

          view = view + '</div>'
          return view
        }
        return ''
      }
    },
    xAxis: {
      type: 'category',
      splitLine: {show: false},
      data: flag === '1' ? obj.xData1 : obj.xData2,
      axisLabel:{
        color: '#FFFFFF',
        interval: 0
      },
      axisTick: { show: false }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: '#888888',
          type: 'dashed'
        }
      },
      max: Math.ceil(obj.yMax * 1.2)
    },
    series: [
      {
        type: 'boxplot',
        data: obj.sData,
        boxWidth: 21,
        animationDuration: function (idx) { return idx * 500; },
        animationEasing: 'bounceOut'
      },
      {
        type: 'line',
        animation: true,
        animationEasing: 'bounceOut',
        data: obj.zxData1,
        itemStyle: { color: '#2acaf8' }
      },
      {
        type: 'line',
        animation: true,
        animationEasing: 'bounceOut',
        data: obj.zxData2,
        itemStyle: { color: 'red' }
      },
      {
        type: 'line',
        markLine: {
          symbol:"none",
          animation: true,
          animationEasing: 'bounceOut',
          data: [ { yAxis: obj.lsj } ],
          label: { show: true, formatter: '建议零售价', position:"insideEndTop", color: '#FFFFFF', fontSize: 10 },
          lineStyle: { color: '#fbff9b', width: 1.5, type: 'solid' }
        }
      },
      {
        type: 'line',
        markLine: {
          symbol:"none",
          animation: true,
          animationEasing: 'bounceOut',
          data: [ { yAxis: obj.pfj } ],
          label: { show: true, formatter: '批发价', position:"insideEndTop", color: '#FFFFFF', fontSize: 10 },
          lineStyle: { color: '#ff9ee9', width: 1.5, type: 'solid' }
        }
      },
    ],
  };
}


// 箱线图配置（累计，库存）
export function xxtOptionByLjKc(flag, obj) {
  return {
    grid: {
      left: '1%',  // 左边距
      right: '1%', // 右边距
      top: '6%',
      bottom: '6%', // 下边距
      containLabel: true
    },
    tooltip: {
      confine: true,
      trigger: 'item',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params, ticket) => {
        if(params.componentSubType && params.componentSubType === 'boxplot'){
          let view = '<div style="font-size: 14px; width: 200px;">'
          view = view + '<div>'+params.name+'</div>'

          view = view + '<div><span >置信区间：</span><span>' + params.value[2].toFixed(2) + ' 至 ' + params.value[4].toFixed(2) + '</span></div>'
          view = view + '<div><span >平均值：</span><span>' + params.value[3].toFixed(2) + '</span></div>'
          view = view + '<div><span >最小值：</span><span>' + params.value[1].toFixed(2) + '</span></div>'
          view = view + '<div><span >最大值：</span><span>' + params.value[5].toFixed(2) + '</span></div>'

          view = view + '</div>'
          return view
        }
        return ''
      }
    },
    xAxis: {
      type: 'category',
      splitLine: {show: false},
      data: flag === '1' ? obj.xData1 : obj.xData2,
      axisLabel:{
        color: '#FFFFFF',
        interval: 0
      },
      axisTick: { show: false }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: '#888888',
          type: 'dashed'
        }
      },
      max: Math.ceil(obj.yMax * 1.2)
    },
    series: [
      {
        type: 'boxplot',
        data: obj.sData,
        boxWidth: 21,
        animationDuration: function (idx) { return idx * 500; },
        animationEasing: 'bounceOut'
      },
      {
        type: 'line',
        animation: true,
        animationEasing: 'bounceOut',
        data: obj.zxData1,
        itemStyle: { color: '#2acaf8' }
      },
    ],
  };
}

// 箱线图配置（该次，价格）
export function xxtOptionByGcJg(obj) {
  return {
    grid: {
      left: '1%',  // 左边距
      right: '1%', // 右边距
      top: '6%',
      bottom: '5%', // 下边距
      containLabel: true
    },
    tooltip: {
      confine: true,
      trigger: 'item',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params, ticket) => {
        if(params.componentSubType && params.componentSubType === 'boxplot'){
          let view = '<div style="font-size: 14px; width: 200px;">'
          view = view + '<div>'+params.name+'</div>'

          view = view + '<div><span >置信区间：</span><span>' + params.value[2].toFixed(2) + ' 至 ' + params.value[4].toFixed(2) + '</span></div>'
          view = view + '<div><span >平均值：</span><span>' + params.value[3].toFixed(2) + '</span></div>'
          view = view + '<div><span >最小值：</span><span>' + params.value[1].toFixed(2) + '</span></div>'
          view = view + '<div><span >最大值：</span><span>' + params.value[5].toFixed(2) + '</span></div>'

          view = view + '</div>'
          return view
        }
        return ''
      }
    },
    xAxis: {
      type: 'category',
      splitLine: {show: false},
      data: obj.xData,
      axisLabel:{
        color: '#FFFFFF',
        interval: 0
      },
      axisTick: { show: false }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: '#888888',
          type: 'dashed'
        }
      },
      max: Math.ceil(obj.yMax * 1.2)
    },
    series: [
      {
        type: 'boxplot',
        data: obj.sData,
        boxWidth: 21,
        animationDuration: function (idx) { return idx * 500; },
        animationEasing: 'bounceOut'
      },
      {
        type: 'line',
        data: obj.zxData1,
        animation: true,
        animationEasing: 'bounceInOut',
        itemStyle: { color: '#2acaf8' }
      },
      {
        type: 'line',
        markLine: {
          symbol:"none",
          animation: true,
          animationEasing: 'bounceInOut',
          data: [ { yAxis: obj.lsj } ],
          label: { show: true, formatter: '建议零售价', position:"insideEndTop", color: '#FFFFFF', fontSize: 10 },
          lineStyle: { color: '#fbff9b', width: 1.5, type: 'solid' }
        }
      },
      {
        type: 'line',
        markLine: {
          symbol:"none",
          animation: true,
          animationEasing: 'bounceInOut',
          data: [ { yAxis: obj.pfj } ],
          label: { show: true, formatter: '批发价', position:"insideEndTop", color: '#FFFFFF', fontSize: 10 },
          lineStyle: { color: '#ff9ee9', width: 1.5, type: 'solid' }
        }
      },
      {
        type: 'line',
        markLine: {
          symbol:"none",
          animation: true,
          animationEasing: 'bounceInOut',
          data: [ { yAxis: obj.thj } ],
          label: { show: true, formatter: '调货价', position:"insideEndTop", color: '#FFFFFF', fontSize: 10 },
          lineStyle: { color: 'red', width: 1.5, type: 'solid' }
        }
      },
    ],
  };
}

// 箱线图配置（该次，库存）
export function xxtOptionByGcKc(obj) {
  return {
    grid: {
      left: '1%',  // 左边距
      right: '1%', // 右边距
      top: '6%',
      bottom: '5%', // 下边距
      containLabel: true
    },
    tooltip: {
      confine: true,
      trigger: 'item',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params, ticket) => {
        if(params.componentSubType && params.componentSubType === 'boxplot'){
          let view = '<div style="font-size: 14px; width: 200px;">'
          view = view + '<div>'+params.name+'</div>'

          view = view + '<div><span >置信区间：</span><span>' + params.value[2].toFixed(2) + ' 至 ' + params.value[4].toFixed(2) + '</span></div>'
          view = view + '<div><span >平均值：</span><span>' + params.value[3].toFixed(2) + '</span></div>'
          view = view + '<div><span >最小值：</span><span>' + params.value[1].toFixed(2) + '</span></div>'
          view = view + '<div><span >最大值：</span><span>' + params.value[5].toFixed(2) + '</span></div>'

          view = view + '</div>'
          return view
        }
        return ''
      }
    },
    xAxis: {
      type: 'category',
      splitLine: {show: false},
      data: obj.xData,
      axisLabel:{
        color: '#FFFFFF',
        interval: 0
      },
      axisTick: { show: false }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: '#888888',
          type: 'dashed'
        }
      },
      max: Math.ceil(obj.yMax * 1.2)
    },
    series: [
      {
        type: 'boxplot',
        data: obj.sData,
        boxWidth: 21,
        animationDuration: function (idx) { return idx * 500; },
        animationEasing: 'bounceOut'
      },
      {
        type: 'line',
        data: obj.zxData1,
        animation: true,
        animationEasing: 'bounceInOut',
        itemStyle: { color: '#2acaf8' }
      },
    ],
  };
}

// 柱状图配置
export function zztOption(obj) {
  let nums = []
  obj.source.forEach(item => {
    Object.values(item).forEach(it => {
      if(typeof it === 'number' && it > 500){
        nums.push(it)
      }
    })
  })
  obj.yMax = Math.max(...nums)
  return {
    grid: {
      left: '0.1%',  // 左边距
      right: '1%', // 右边距
      top: '4%',
      bottom: '2%', // 下边距
      containLabel: true
    },
    tooltip: {
      confine: true,
      formatter: (params, ticket) => {
        let view = '<div style="font-size: 14px;">'
        view = view + '<div>'+params.name+'</div>'
        let iv = '<div style="display: flex; flex-direction: row;">'
        params.dimensionNames.forEach((item, index) => {
          if(index !== 0){
            iv = iv + '<div style="width: 140px;"><span>'+item+'：</span><span>'+params.data[item].toFixed(2)+'</span></div>'
            if(index % 2 === 0){
              iv = iv + '</div>'
              view = view + iv
              iv = '<div style="display: flex; flex-direction: row;">'
            }
          }
        })
        view = view + '</div>'
        return view
      }
    },
    dataset: {
      dimensions: obj.dimensions,
      source: obj.source
    },
    xAxis: {
      type: 'category',
      axisLine: {
        lineStyle: {
          color: '#888888',
          interval: 0
        }
      },
      axisLabel:{
        color: '#FFFFFF',
        interval: 0
      },
      axisTick: { show: false }
    },
    yAxis: {
      splitLine: {
        lineStyle: {
          color: '#888888',
          type: 'dashed'
        }
      },
      max: Math.ceil(obj.yMax * 1.2)
    },
    series: obj.series,
  };
}

//--------------------------------------------------------------- 方法管理 ---------------------------------------------------------------------

// 箱线图颜色
export function setXxtColorAndYMax(obj) {
  let temp = []
  let max = []
  obj.sData.forEach((item, index) => {
    let itemStyle = {
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0baefd' }, { offset: 1, color: '#116cfd' }]),
      borderWidth: 1,
      borderColor: '#15ecfd',
    }
    let emphasis = {
      itemStyle: {
        borderWidth: 1,
        borderColor: '#15ecfd',
      }
    }
    if(index % 2 === 1){
      itemStyle = {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#51ffd0' }, { offset: 1, color: '#51c4ff' }]),
        borderWidth: 1,
        borderColor: '#0d97fd',
      }
      emphasis = {
        itemStyle: {
          borderWidth: 1,
          borderColor: '#0d97fd',
        }
      }
    }
    temp.push({ value: item, itemStyle: itemStyle, emphasis: emphasis })
    max.push(Math.max(...item))
  })
  obj.sData = temp
  obj.yMax = Math.max(...max)
}

// 柱状图颜色
export function getZztColor(type) {
  if(type === 'dw'){
    return [
      { type: 'bar', animation: true, animationEasing: 'backInOut', animationDuration: function (idx) { return idx * 100; }, barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#E4B90C' }]), borderWidth: 1, borderColor: '#FFD735' } },
      { type: 'bar', animation: true, animationEasing: 'backInOut', animationDuration: function (idx) { return idx * 100; }, barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#00A8FF' }]), borderWidth: 1, borderColor: '#3681FF' } },
      { type: 'bar', animation: true, animationEasing: 'backInOut', animationDuration: function (idx) { return idx * 100; }, barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#6FFFFF' }]), borderWidth: 1, borderColor: '#13FAFE' } },
      { type: 'bar', animation: true, animationEasing: 'backInOut', animationDuration: function (idx) { return idx * 100; }, barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#E73341' }]), borderWidth: 1, borderColor: '#FF616E' } },
      { type: 'bar', animation: true, animationEasing: 'backInOut', animationDuration: function (idx) { return idx * 100; }, barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#5FDA33' }]), borderWidth: 1, borderColor: '#6DFF38' } },
      { type: 'bar', animation: true, animationEasing: 'backInOut', animationDuration: function (idx) { return idx * 100; }, barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#D76AFF' }]), borderWidth: 1, borderColor: '#DF8AFF' } },
    ]
  }
  if(type === 'czxc'){
    return [
      { type: 'bar', animation: true, animationEasing: 'backInOut', animationDuration: function (idx) { return idx * 100; }, barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#E4B90C' }]), borderWidth: 1, borderColor: '#FFD735' } },
      { type: 'bar', animation: true, animationEasing: 'backInOut', animationDuration: function (idx) { return idx * 100; }, barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#00A8FF' }]), borderWidth: 1, borderColor: '#3681FF' } },
    ]
  }
  if(type === 'yt'){
    return [
      { type: 'bar', animation: true, animationEasing: 'backInOut', animationDuration: function (idx) { return idx * 100; }, barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#E73341' }]), borderWidth: 1, borderColor: '#FF616E' } },
      { type: 'bar', animation: true, animationEasing: 'backInOut', animationDuration: function (idx) { return idx * 100; }, barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#5FDA33' }]), borderWidth: 1, borderColor: '#6DFF38' } },
      { type: 'bar', animation: true, animationEasing: 'backInOut', animationDuration: function (idx) { return idx * 100; }, barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#D76AFF' }]), borderWidth: 1, borderColor: '#DF8AFF' } },
      { type: 'bar', animation: true, animationEasing: 'backInOut', animationDuration: function (idx) { return idx * 100; }, barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#0022E6' }]), borderWidth: 1, borderColor: '#1838EF' } },
      { type: 'bar', animation: true, animationEasing: 'backInOut', animationDuration: function (idx) { return idx * 100; }, barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#C50497' }]), borderWidth: 1, borderColor: '#C50497' } },
      { type: 'bar', animation: true, animationEasing: 'backInOut', animationDuration: function (idx) { return idx * 100; }, barWidth: 16, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#0F3352' }, { offset: 1, color: '#FF6AB7' }]), borderWidth: 1, borderColor: '#FF7BBF' } },
    ]
  }
}


<template>
    <div class="component-upload-image">
        <el-upload :action="uploadImgUrl" :on-success="handleUploadSuccess" :before-upload="handleBeforeUpload"
            :data="data" :on-error="handleUploadError" :show-file-list="false" :headers="headers"
            class="form_content_upload" :style="{ width: width + 'px', height: height + 'px' }">
            <img v-if="imageUrl" :src="imageUrl" alt="imageUrl" />
            <el-icon v-else class="avatar-uploader-icon" size="28">
                <Plus />
            </el-icon>
        </el-upload>
    </div>
</template>

<script setup>
import { getToken } from "@/utils/auth"

const props = defineProps({
    modelValue: String,
    // 上传接口地址
    action: {
        type: String,
        default: "/common/upload"
    },
    width: {
        type: String,
        default: '175'
    },
    height: {
        type: String,
        default: '175'
    },
    // 上传携带的参数
    data: {
        type: Object
    },
    // 大小限制(MB)
    fileSize: {
        type: Number,
        default: 5
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
        type: Array,
        default: () => ["png", "jpg", "jpeg"]
    },
})

const { proxy } = getCurrentInstance()
const emit = defineEmits()
const baseUrl = import.meta.env.VITE_APP_BASE_API
const uploadImgUrl = ref(import.meta.env.VITE_APP_BASE_API + props.action) // 上传的图片服务器地址
const headers = ref({ Authorization: "Bearer " + getToken() })
const imageUrl = ref("")

watch(() => props.modelValue, val => {
    if (val) {
        imageUrl.value = baseUrl + props.modelValue
    } else {
        imageUrl.value = ""
        return ""
    }
}, { deep: true, immediate: true })

// 上传前loading加载
function handleBeforeUpload(file) {
    let isImg = false
    if (props.fileType.length) {
        let fileExtension = ""
        if (file.name.lastIndexOf(".") > -1) {
            fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1)
        }
        isImg = props.fileType.some(type => {
            if (file.type.indexOf(type) > -1) return true
            if (fileExtension && fileExtension.indexOf(type) > -1) return true
            return false
        })
    } else {
        isImg = file.type.indexOf("image") > -1
    }
    if (!isImg) {
        proxy.$modal.msgError(`文件格式不正确，请上传${props.fileType.join("/")}图片格式文件!`)
        return false
    }
    if (file.name.includes(',')) {
        proxy.$modal.msgError('文件名不正确，不能包含英文逗号!')
        return false
    }
    if (props.fileSize) {
        const isLt = file.size / 1024 / 1024 < props.fileSize
        if (!isLt) {
            proxy.$modal.msgError(`上传头像图片大小不能超过 ${props.fileSize} MB!`)
            return false
        }
    }
    proxy.$modal.loading("正在上传图片，请稍候...")
}

// 上传成功回调
function handleUploadSuccess(res, file) {
    if (res.code === 200) {
        imageUrl.value = baseUrl + res.fileName
        uploadedSuccessfully()
    } else {
        proxy.$modal.closeLoading()
        proxy.$modal.msgError(res.msg)
        uploadedSuccessfully()
    }
}

// 上传结束处理
function uploadedSuccessfully() {
    emit("update:modelValue", listToString(imageUrl.value))
    proxy.$modal.closeLoading()
}

// 上传失败
function handleUploadError() {
    proxy.$modal.msgError("上传图片失败")
    proxy.$modal.closeLoading()
}

// 对象转成指定字符串分隔
function listToString(url, separator) {
    let strs = ""
    separator = separator || ","
    if (undefined !== url && url.indexOf("blob:") !== 0) {
        strs += url.replace(baseUrl, "") + separator
    }
    return strs != "" ? strs.substr(0, strs.length - 1) : ""
}
</script>

<style scoped lang="scss">
.form_content_upload {
    background: #F5F5F5;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}
</style>
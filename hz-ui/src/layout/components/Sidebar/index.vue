<template>
  <div :class="{ 'has-logo': showLogo }" class="sidebar-container">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu :default-active="activeMenu" :collapse="isCollapse" :background-color="getMenuBackground"
        :text-color="getMenuTextColor" :unique-opened="true" :active-text-color="theme" :collapse-transition="false"
        mode="vertical" :class="sideTheme">
        <sidebar-item v-for="(route, index) in sidebarRouters" :key="route.path + index" :item="route"
          :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/assets/styles/variables.module.scss'
import useAppStore from '@/store/modules/app'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'

const route = useRoute()
const appStore = useAppStore()
const settingsStore = useSettingsStore()
const permissionStore = usePermissionStore()

const sidebarRouters = computed(() => permissionStore.sidebarRouters)
const showLogo = computed(() => settingsStore.sidebarLogo)
const sideTheme = computed(() => settingsStore.sideTheme)
const theme = computed(() => settingsStore.theme)
const isCollapse = computed(() => !appStore.sidebar.opened)

// 获取菜单背景色
const getMenuBackground = computed(() => {
  if (settingsStore.isDark) {
    return 'var(--sidebar-bg)'
  }
  return sideTheme.value === 'theme-dark' ? variables.menuBg : variables.menuLightBg
})

// 获取菜单文字颜色
const getMenuTextColor = computed(() => {
  if (settingsStore.isDark) {
    return 'var(--sidebar-text)'
  }
  return sideTheme.value === 'theme-dark' ? variables.menuText : variables.menuLightText
})

const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})
</script>

<style lang="scss" scoped>
.sidebar-container {
  background-color: v-bind(getMenuBackground);

  .scrollbar-wrapper {
    background-color: v-bind(getMenuBackground);
  }

  :deep(.el-menu) {
    border: none;
    height: 100%;

    .el-sub-menu {
      &.is-active {
        .el-sub-menu__title {
          color: #049B01;
        }

        .el-sub-menu {
          .el-sub-menu__title {
            color: #333;
          }

          &.is-active {
            .el-sub-menu__title {
              color: #049B01;
            }
          }
        }
      }
    }

    .el-menu--inline {
      margin: 0 15px;
      padding: 8px 10px;
      background-color: #F4F6F8;
      border-radius: 8px;
    }

    .el-menu-item {
      color: v-bind(getMenuTextColor);
      letter-spacing: 1px;
      font-weight: bold;
      padding-left: 30px;
      height: 50px;
      font-size: 15px;

      &.is-active {
        color: #fff;
        background-color: #049B01 !important;
        border-radius: 5px;
      }
    }

    .el-sub-menu__title {
      font-size: 15px;
      padding-left: 30px;
      color: v-bind(getMenuTextColor);
      letter-spacing: 1px;
      font-weight: bold;
      border-radius: 0;
    }
  }
}
</style>

// uno.config.ts
import { defineConfig, presetAttributify, presetIcons, presetUno } from 'unocss'
import { presetScrollbar } from 'unocss-preset-scrollbar'

export default defineConfig({
    // ...UnoCSS options
    presets: [
        presetAttributify(),
        presetUno(),
        presetIcons({
            extraProperties: {
                'display': 'inline-block',
                'vertical-align': 'middle',
            },
            scale: 1.2,
            warn: true,
        }),
        presetScrollbar({}),
    ],
    rules: [
        ['fs-12', { 'font-size': '12px' }], // 自定义字体大小
        ['fs-13', { 'font-size': '13px' }], // 自定义字体大小
        ['fs-14', { 'font-size': '14px' }], // 自定义字体大小
        ['fs-15', { 'font-size': '15px' }], // 自定义字体大小
        ['fs-16', { 'font-size': '16px' }], // 自定义字体大小
        ['fs-17', { 'font-size': '17px' }], // 自定义字体大小
        ['fs-18', { 'font-size': '18px' }], // 自定义字体大小
        ['fs-19', { 'font-size': '19px' }], // 自定义字体大小
        ['fs-20', { 'font-size': '20px' }], // 自定义字体大小
        ['fs-21', { 'font-size': '21px' }], // 自定义字体大小
        ['fs-22', { 'font-size': '22px' }], // 自定义字体大小
        ['fs-23', { 'font-size': '23px' }], // 自定义字体大小
        ['fs-24', { 'font-size': '24px' }], // 自定义字体大小
        ['fs-25', { 'font-size': '25px' }], // 自定义字体大小
        ['fs-26', { 'font-size': '26px' }], // 自定义字体大小
        ['fs-27', { 'font-size': '27px' }], // 自定义字体大小
        ['fs-28', { 'font-size': '28px' }], // 自定义字体大小
        ['fs-29', { 'font-size': '29px' }], // 自定义字体大小
        ['fs-30', { 'font-size': '30px' }], // 自定义字体大小
        ['fs-31', { 'font-size': '31px' }], // 自定义字体大小
        ['fs-32', { 'font-size': '32px' }], // 自定义字体大小
        ['fs-33', { 'font-size': '33px' }], // 自定义字体大小
        ['fs-34', { 'font-size': '34px' }], // 自定义字体大小
        ['fs-35', { 'font-size': '35px' }], // 自定义字体大小
        ['fs-36', { 'font-size': '36px' }], // 自定义字体大小
        ['fs-37', { 'font-size': '37px' }], // 自定义字体大小
        ['fs-38', { 'font-size': '38px' }], // 自定义字体大小
        ['fs-39', { 'font-size': '39px' }], // 自定义字体大小
        ['fs-40', { 'font-size': '40px' }], // 自定义字体大小
        ['fs-41', { 'font-size': '41px' }], // 自定义字体大小
        ['fs-42', { 'font-size': '42px' }], // 自定义字体大小
        ['fs-43', { 'font-size': '43px' }], // 自定义字体大小
        ['fs-44', { 'font-size': '44px' }], // 自定义字体大小
        ['fs-45', { 'font-size': '45px' }], // 自定义字体大小
        ['br-3', { 'border-radius': '3px' }], // 自定义字体大小
        ['br-4', { 'border-radius': '4px' }], // 自定义字体大小
        ['br-5', { 'border-radius': '5px' }], // 自定义字体大小
        ['br-6', { 'border-radius': '6px' }], // 自定义字体大小
        ['br-7', { 'border-radius': '7px' }], // 自定义字体大小
        ['br-8', { 'border-radius': '8px' }], // 自定义字体大小
    ],
    shortcuts: [
        ['wh-full', 'w-full h-full'],
        ['flex-center', 'flex justify-center items-center'],
        [
            'icon-btn',
            'text-16 inline-block cursor-pointer select-none opacity-75 transition duration-200 ease-in-out hover:opacity-100 hover:text-primary !outline-none',
        ],
    ],
    theme: {
        colors: {
            hz_white: '#FFFFFF',
            hz_com_blue: '#296dff',
        }
    }
})
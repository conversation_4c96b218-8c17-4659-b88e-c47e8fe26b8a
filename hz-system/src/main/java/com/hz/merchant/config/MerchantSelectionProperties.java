package com.hz.merchant.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 商户选择配置属性
 * 支持从application.properties中读取配置，也支持代码动态设置
 */
@Component
@ConfigurationProperties(prefix = "hz.merchant-selection")
public class MerchantSelectionProperties {

    // 基础配置
    private int totalCount = 50;
    private int simulationIterations = 30;

    // 距离限制配置（单位：公里）
    private double maxSingleDistance = 50.0;

    // 商户分配比例配置（使用比例形式）
    private String areaRatio = "6:4";           // 城镇:乡村 = 6:4
    private String terminalRatio = "3:5:2";     // 加盟店:普通终端:现代终端 = 3:5:2
    private String collectionRatio = "8:2";     // 采集:非采集 = 8:2

    // 路径优化配置
    private boolean enableRouteOptimization = true;
    private String optimizationAlgorithm = "AUTO";

    // 保留旧的配置以兼容现有代码
    private double scoreWeightDistance = 0.7;
    private double scoreWeightTime = 0.3;

    // Getters and Setters
    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getSimulationIterations() {
        return simulationIterations;
    }

    public void setSimulationIterations(int simulationIterations) {
        this.simulationIterations = simulationIterations;
    }

    public double getMaxSingleDistance() {
        return maxSingleDistance;
    }

    public void setMaxSingleDistance(double maxSingleDistance) {
        this.maxSingleDistance = maxSingleDistance;
    }

    public String getAreaRatio() {
        return areaRatio;
    }

    public void setAreaRatio(String areaRatio) {
        this.areaRatio = areaRatio;
    }

    public String getTerminalRatio() {
        return terminalRatio;
    }

    public void setTerminalRatio(String terminalRatio) {
        this.terminalRatio = terminalRatio;
    }

    public String getCollectionRatio() {
        return collectionRatio;
    }

    public void setCollectionRatio(String collectionRatio) {
        this.collectionRatio = collectionRatio;
    }

    public boolean isEnableRouteOptimization() {
        return enableRouteOptimization;
    }

    public void setEnableRouteOptimization(boolean enableRouteOptimization) {
        this.enableRouteOptimization = enableRouteOptimization;
    }

    public String getOptimizationAlgorithm() {
        return optimizationAlgorithm;
    }

    public void setOptimizationAlgorithm(String optimizationAlgorithm) {
        this.optimizationAlgorithm = optimizationAlgorithm;
    }

    public double getScoreWeightDistance() {
        return scoreWeightDistance;
    }

    public void setScoreWeightDistance(double scoreWeightDistance) {
        this.scoreWeightDistance = scoreWeightDistance;
    }

    public double getScoreWeightTime() {
        return scoreWeightTime;
    }

    public void setScoreWeightTime(double scoreWeightTime) {
        this.scoreWeightTime = scoreWeightTime;
    }
}
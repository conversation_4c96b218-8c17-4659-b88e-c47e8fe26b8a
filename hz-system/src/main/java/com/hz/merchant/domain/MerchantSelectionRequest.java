package com.hz.merchant.domain;

/**
 * 商户抽取请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
public class MerchantSelectionRequest {
    
    /** 地区ID */
    private Long deptId;
    
    /** 诚信等级 */
    private String chengxindengji;
    
    /** 终端类型 */
    private String zhongduancengji;
    
    /** 经营规模 */
    private String jingyingguimo;
    
    /** 抽取数量 */
    private Integer num;
    
    /** 抽取类型：smart-智能抽取，condition-条件抽取 */
    private String selectionType;

    /** 起点经度 */
    private Double startLongitude;

    /** 起点纬度 */
    private Double startLatitude;

    /** 地域比例配置 (格式: "城镇:乡村", 如 "6:4") */
    private String areaRatio;

    /** 终端比例配置 (格式: "加盟店:普通终端:现代终端", 如 "3:5:2") */
    private String terminalRatio;

    /** 采集比例配置 (格式: "采集:非采集", 如 "8:2") */
    private String collectionRatio;

    public Long getDeptId() {
        return deptId;
    }
    
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    
    public String getChengxindengji() {
        return chengxindengji;
    }
    
    public void setChengxindengji(String chengxindengji) {
        this.chengxindengji = chengxindengji;
    }
    
    public String getZhongduancengji() {
        return zhongduancengji;
    }
    
    public void setZhongduancengji(String zhongduancengji) {
        this.zhongduancengji = zhongduancengji;
    }
    
    public String getJingyingguimo() {
        return jingyingguimo;
    }
    
    public void setJingyingguimo(String jingyingguimo) {
        this.jingyingguimo = jingyingguimo;
    }
    
    public Integer getNum() {
        return num;
    }
    
    public void setNum(Integer num) {
        this.num = num;
    }
    
    public String getSelectionType() {
        return selectionType;
    }
    
    public void setSelectionType(String selectionType) {
        this.selectionType = selectionType;
    }

    public Double getStartLongitude() {
        return startLongitude;
    }

    public void setStartLongitude(Double startLongitude) {
        this.startLongitude = startLongitude;
    }

    public Double getStartLatitude() {
        return startLatitude;
    }

    public void setStartLatitude(Double startLatitude) {
        this.startLatitude = startLatitude;
    }

    public String getAreaRatio() {
        return areaRatio;
    }

    public void setAreaRatio(String areaRatio) {
        this.areaRatio = areaRatio;
    }

    public String getTerminalRatio() {
        return terminalRatio;
    }

    public void setTerminalRatio(String terminalRatio) {
        this.terminalRatio = terminalRatio;
    }

    public String getCollectionRatio() {
        return collectionRatio;
    }

    public void setCollectionRatio(String collectionRatio) {
        this.collectionRatio = collectionRatio;
    }

    @Override
    public String toString() {
        return "MerchantSelectionRequest{" +
                "deptId=" + deptId +
                ", chengxindengji='" + chengxindengji + '\'' +
                ", zhongduancengji='" + zhongduancengji + '\'' +
                ", jingyingguimo='" + jingyingguimo + '\'' +
                ", num=" + num +
                ", selectionType='" + selectionType + '\'' +
                ", startLongitude=" + startLongitude +
                ", startLatitude=" + startLatitude +
                ", areaRatio='" + areaRatio + '\'' +
                ", terminalRatio='" + terminalRatio + '\'' +
                ", collectionRatio='" + collectionRatio + '\'' +
                '}';
    }
} 
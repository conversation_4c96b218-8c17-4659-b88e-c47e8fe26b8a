package com.hz.merchant.util;

import com.hz.merchant.constant.MerchantConstants;
import com.hz.merchant.domain.MerchantInfo;

/**
 * 地理位置工具类
 * 统一处理距离计算和坐标解析
 */
public class GeoUtils {

    /**
     * 计算两个商户之间的距离
     */
    public static double calculateDistance(MerchantInfo m1, MerchantInfo m2) {
        if (m1 == null || m2 == null) return 0.0;

        Double lat1 = m1.getLatitude();
        Double lon1 = m1.getLongitude();
        Double lat2 = m2.getLatitude();
        Double lon2 = m2.getLongitude();

        if (lat1 == null || lon1 == null || lat2 == null || lon2 == null ||
            lat1 == MerchantConstants.INVALID_COORDINATE || lon1 == MerchantConstants.INVALID_COORDINATE ||
            lat2 == MerchantConstants.INVALID_COORDINATE || lon2 == MerchantConstants.INVALID_COORDINATE) {
            return MerchantConstants.MAX_SINGLE_DISTANCE_KM;
        }

        return haversine(lat1, lon1, lat2, lon2);
    }
    
    /**
     * 使用Haversine公式计算两点间距离
     */
    public static double haversine(double lat1, double lon1, double lat2, double lon2) {
        double R = MerchantConstants.EARTH_RADIUS_KM; // 地球半径，单位：公里
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
                        Math.sin(dLon / 2) * Math.sin(dLon / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }
    
    /**
     * 使用经纬度直接计算距离
     */
    public static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        return haversine(lat1, lon1, lat2, lon2);
    }
    
    /**
     * 检查商户是否有有效的坐标
     */
    public static boolean hasValidCoordinates(MerchantInfo merchant) {
        Double longitude = merchant.getLongitude();
        Double latitude = merchant.getLatitude();
        return longitude != null && latitude != null &&
               longitude != MerchantConstants.INVALID_COORDINATE &&
               latitude != MerchantConstants.INVALID_COORDINATE;
    }
    
    /**
     * 获取商户的坐标字符串
     */
    public static String getCoordinate(MerchantInfo merchant) {
        Double longitude = merchant.getLongitude();
        Double latitude = merchant.getLatitude();
        
        if (longitude == null || latitude == null) {
            return MerchantConstants.INVALID_COORDINATE + "," + MerchantConstants.INVALID_COORDINATE;
        }
        
        return longitude + "," + latitude;
    }
}

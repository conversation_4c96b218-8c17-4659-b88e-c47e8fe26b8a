package com.hz.merchant.util;

import com.hz.merchant.constant.MerchantConstants;
import java.util.Arrays;

/**
 * 比例解析工具类
 * 用于解析比例字符串并转换为百分比数组
 */
public class RatioParser {
    
    /**
     * 解析比例字符串
     * @param ratio 比例字符串，格式如 "2:1" 或 "3:5:2"
     * @return 百分比数组
     */
    public static double[] parseRatio(String ratio) {
        if (ratio == null || ratio.trim().isEmpty()) {
            throw new IllegalArgumentException(MerchantConstants.ERROR_RATIO_EMPTY);
        }

        String[] parts = ratio.split(":");
        if (parts.length == 0) {
            throw new IllegalArgumentException(MerchantConstants.ERROR_RATIO_FORMAT + "：" + ratio);
        }
        
        double[] values = new double[parts.length];
        double sum = 0;
        
        try {
            for (int i = 0; i < parts.length; i++) {
                values[i] = Double.parseDouble(parts[i].trim());
                if (values[i] < 0) {
                    throw new IllegalArgumentException(MerchantConstants.ERROR_RATIO_NEGATIVE + "：" + parts[i]);
                }
                sum += values[i];
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException(MerchantConstants.ERROR_RATIO_FORMAT + "，包含非数字内容：" + ratio);
        }
        
        // 如果总和为0，返回均匀分布
        if (sum == 0) {
            Arrays.fill(values, 1.0 / values.length);
            return values;
        }
        
        // 转换为百分比
        for (int i = 0; i < values.length; i++) {
            values[i] = values[i] / sum;
        }
        
        return values;
    }
    
    /**
     * 解析地域比例（城镇:乡村）
     * @param areaRatio 地域比例字符串
     * @return [城镇比例, 乡村比例]
     */
    public static double[] parseAreaRatio(String areaRatio) {
        double[] ratios = parseRatio(areaRatio);
        if (ratios.length != 2) {
            throw new IllegalArgumentException("地域比例必须是2个值（城镇:乡村）：" + areaRatio);
        }
        return ratios;
    }
    
    /**
     * 解析终端类型比例（加盟店:普通终端:现代终端）
     * @param terminalRatio 终端类型比例字符串
     * @return [加盟店比例, 普通终端比例, 现代终端比例]
     */
    public static double[] parseTerminalRatio(String terminalRatio) {
        double[] ratios = parseRatio(terminalRatio);
        if (ratios.length != 3) {
            throw new IllegalArgumentException("终端类型比例必须是3个值（加盟店:普通终端:现代终端）：" + terminalRatio);
        }
        return ratios;
    }
    
    /**
     * 解析采集比例（采集:非采集）
     * @param collectionRatio 采集比例字符串
     * @return [采集比例, 非采集比例]
     */
    public static double[] parseCollectionRatio(String collectionRatio) {
        double[] ratios = parseRatio(collectionRatio);
        if (ratios.length != 2) {
            throw new IllegalArgumentException("采集比例必须是2个值（采集:非采集）：" + collectionRatio);
        }
        return ratios;
    }
    
    
}

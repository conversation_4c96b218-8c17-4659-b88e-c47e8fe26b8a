package com.hz.merchant.service.impl;

import com.hz.merchant.domain.MerchantInfo;
import com.hz.merchant.util.GeoUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 闭环路径优化器 - TSP问题求解
 * 支持闭环路径算法：最近邻、2-opt、改进算法等
 * 支持单次距离限制
 * 支持起点坐标的闭环路径规划
 * 所有算法都确保闭环路径（起点→商户→起点）
 */
@Component
public class RouteOptimizer {
    
    private static final Logger logger = LoggerFactory.getLogger(RouteOptimizer.class);
    
    private double maxSingleDistance = 50.0; // 默认单次最大距离50公里
    
    /**
     * 构造函数
     */
    public RouteOptimizer() {}
    
    /**
     * 带单次距离限制的构造函数
     */
    public RouteOptimizer(double maxSingleDistance) {
        this.maxSingleDistance = maxSingleDistance;
    }
    
    /**
     * 设置单次最大距离限制
     */
    public void setMaxSingleDistance(double maxSingleDistance) {
        this.maxSingleDistance = maxSingleDistance;
    }
    
    /**
     * 路径优化结果
     */
    public static class RouteResult {
        private final List<MerchantInfo> route;
        private final double totalDistance;
        private final String algorithm;
        private final long executionTime;
        
        public RouteResult(List<MerchantInfo> route, double totalDistance, String algorithm, long executionTime) {
            this.route = new ArrayList<>(route);
            this.totalDistance = totalDistance;
            this.algorithm = algorithm;
            this.executionTime = executionTime;
        }
        
        public List<MerchantInfo> getRoute() { return route; }
        public double getTotalDistance() { return totalDistance; }
        public String getAlgorithm() { return algorithm; }
        public long getExecutionTime() { return executionTime; }
    }
    
    /**
     * 基于起点的闭环路径优化
     * @param merchants 商户列表
     * @param startLongitude 起点经度
     * @param startLatitude 起点纬度
     * @return 优化后的路径结果
     */
    public RouteResult optimizeRouteFromStart(List<MerchantInfo> merchants,
                                            Double startLongitude,
                                            Double startLatitude) {
        if (merchants == null || merchants.isEmpty()) {
            return new RouteResult(new ArrayList<>(), 0.0, "EMPTY", 0);
        }

        if (startLongitude == null || startLatitude == null) {
            logger.warn("起点坐标为空，使用默认闭环优化");
            return optimizeCircularRoute(merchants);
        }

        logger.info("开始基于起点的环形路径优化，起点坐标: ({}, {}), 商户数量: {}",
            startLongitude, startLatitude, merchants.size());

        long startTime = System.currentTimeMillis();

        // 1. 创建起点虚拟商户，参与环形路径优化
        MerchantInfo startPoint = createStartPointMerchant(startLongitude, startLatitude);
        List<MerchantInfo> merchantsWithStart = new ArrayList<>(merchants);
        merchantsWithStart.add(startPoint);

        // 2. 使用闭环优化算法进行TSP优化
        RouteResult circularResult = optimizeCircularRoute(merchantsWithStart);
        List<MerchantInfo> optimizedRouteWithStart = circularResult.getRoute();

        // 3. 调整路径顺序，使其从起点开始，并移除虚拟起点
        List<MerchantInfo> finalRoute = adjustRouteToStartFromOrigin(optimizedRouteWithStart, startPoint);

        long executionTime = System.currentTimeMillis() - startTime;

        logger.info("基于起点的环形路径优化完成，总距离: {} km，用时: {} ms，访问商户: {} 个",
            String.format("%.2f", circularResult.getTotalDistance()), executionTime, finalRoute.size());

        return new RouteResult(finalRoute, circularResult.getTotalDistance(),
                              "CIRCULAR_FROM_START_" + circularResult.getAlgorithm(), executionTime);
    }
    
    /**
     * 优化闭环路径 - 使用最佳可用算法（起点终点相同）
     */
    public RouteResult optimizeCircularRoute(List<MerchantInfo> merchants) {
        if (merchants == null || merchants.size() <= 2) {
            return new RouteResult(merchants, calculateCircularRouteDistance(merchants), "CIRCULAR_TRIVIAL", 0);
        }
        
        // 对于闭环路径问题，使用专门的优化算法
        if (merchants.size() <= 20) {
            return optimizeCircularWith2Opt(merchants);
        } else if (merchants.size() <= 100) {
            return optimizeCircularWithImprovement(merchants);
        } else {
            return optimizeCircularWithNearestNeighbor(merchants);
        }
    }
    
    /**
     * 使用多种算法优化并比较结果，返回最优结果
     */
    public RouteResult optimizeWithComparison(List<MerchantInfo> merchants) {
        if (merchants == null || merchants.size() <= 2) {
            return new RouteResult(merchants, calculateCircularRouteDistance(merchants), "TRIVIAL", 0);
        }

        List<RouteResult> results = new ArrayList<>();

        logger.info("正在运行多种算法进行比较...");

        try {
            // 1. 最近邻算法
            RouteResult nearestNeighbor = optimizeCircularWithNearestNeighbor(merchants);
            results.add(nearestNeighbor);
            logger.info("最近邻算法完成 - 距离: {} km",
                String.format("%.2f", nearestNeighbor.getTotalDistance()));
        } catch (Exception e) {
            logger.error("最近邻算法执行失败: {}", e.getMessage());
        }

        // 2. 2-opt算法（适用于中小规模问题）
        if (merchants.size() <= 50) {
            try {
                RouteResult twoOpt = optimizeCircularWith2Opt(merchants);
                results.add(twoOpt);
                logger.info("2-opt算法完成 - 距离: {} km",
                    String.format("%.2f", twoOpt.getTotalDistance()));
            } catch (Exception e) {
                logger.error("2-opt算法执行失败: {}", e.getMessage());
            }
        }

        // 3. 改进算法（适用于中等规模问题）
        if (merchants.size() <= 100) {
            try {
                RouteResult improvement = optimizeCircularWithImprovement(merchants);
                results.add(improvement);
                logger.info("改进算法完成 - 距离: {} km",
                    String.format("%.2f", improvement.getTotalDistance()));
            } catch (Exception e) {
                logger.error("改进算法执行失败: {}", e.getMessage());
            }
        }

        // 如果没有成功的结果，返回默认结果
        if (results.isEmpty()) {
            logger.error("所有算法都失败了，返回默认结果");
            return new RouteResult(merchants, calculateCircularRouteDistance(merchants), "DEFAULT", 0);
        }

        // 找到最优结果（距离最短）
        RouteResult bestResult = results.get(0);
        for (RouteResult result : results) {
            if (result.getTotalDistance() < bestResult.getTotalDistance()) {
                bestResult = result;
            }
        }

        logger.info("=== 算法比较结果 ===");
        logger.info("参与比较的算法数量: {}", results.size());
        for (RouteResult result : results) {
            logger.info("• {}: {} km (用时: {} ms)",
                result.getAlgorithm(), String.format("%.2f", result.getTotalDistance()), result.getExecutionTime());
        }
        logger.info("最优算法: {} - 距离: {} km",
            bestResult.getAlgorithm(), String.format("%.2f", bestResult.getTotalDistance()));

        // 创建一个新的结果对象，标注这是比较后的最优结果
        return new RouteResult(bestResult.getRoute(), bestResult.getTotalDistance(),
                "BEST_OF_COMPARISON_" + bestResult.getAlgorithm(), bestResult.getExecutionTime());
    }
    
    /**
     * 找到距离当前位置最近的商户
     */
    private MerchantInfo findNearestMerchant(Set<MerchantInfo> merchants, 
                                           double currentLat, 
                                           double currentLon) {
        MerchantInfo nearest = null;
        double minDistance = Double.MAX_VALUE;
        
        for (MerchantInfo merchant : merchants) {
            if (GeoUtils.hasValidCoordinates(merchant)) {
                double distance = GeoUtils.calculateDistance(
                    currentLat, currentLon,
                    merchant.getLatitude(), merchant.getLongitude()
                );
                
                if (distance < minDistance && distance <= maxSingleDistance) {
                    minDistance = distance;
                    nearest = merchant;
                }
            }
        }
        
        // 如果没有找到符合距离限制的商户，选择最近的
        if (nearest == null) {
            for (MerchantInfo merchant : merchants) {
                if (GeoUtils.hasValidCoordinates(merchant)) {
                    double distance = GeoUtils.calculateDistance(
                        currentLat, currentLon,
                        merchant.getLatitude(), merchant.getLongitude()
                    );
                    
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearest = merchant;
                    }
                }
            }
        }
        
        return nearest;
    }
    
    /**
     * 计算基于起点的路径总距离（包括从起点到第一个商户，以及从最后一个商户回到起点）
     */
    private double calculateRouteDistanceFromStart(List<MerchantInfo> merchants, 
                                                 double startLat, 
                                                 double startLon) {
        if (merchants.isEmpty()) {
            return 0.0;
        }
        
        double totalDistance = 0.0;
        double currentLat = startLat;
        double currentLon = startLon;
        
        // 计算从起点到各个商户的距离
        for (MerchantInfo merchant : merchants) {
            if (GeoUtils.hasValidCoordinates(merchant)) {
                double distance = GeoUtils.calculateDistance(
                    currentLat, currentLon,
                    merchant.getLatitude(), merchant.getLongitude()
                );
                totalDistance += distance;
                currentLat = merchant.getLatitude();
                currentLon = merchant.getLongitude();
            }
        }
        
        // 计算从最后一个商户回到起点的距离
        if (!merchants.isEmpty()) {
            MerchantInfo lastMerchant = merchants.get(merchants.size() - 1);
            if (GeoUtils.hasValidCoordinates(lastMerchant)) {
                double returnDistance = GeoUtils.calculateDistance(
                    lastMerchant.getLatitude(), lastMerchant.getLongitude(),
                    startLat, startLon
                );
                totalDistance += returnDistance;
            }
        }
        
        return totalDistance;
    }

    /**
     * 创建包含起点和终点的完整路径
     *
     * @param merchantRoute 商户路径
     * @param startLat 起点纬度
     * @param startLon 起点经度
     * @return 包含起点和终点的完整路径
     */
    private List<MerchantInfo> createCompleteRouteWithStartEnd(List<MerchantInfo> merchantRoute,
                                                              Double startLat,
                                                              Double startLon) {
        List<MerchantInfo> completeRoute = new ArrayList<>();

        // 创建起点虚拟商户
        MerchantInfo startPoint = createVirtualMerchant("起点", startLat, startLon, "START");
        completeRoute.add(startPoint);

        // 添加所有商户
        completeRoute.addAll(merchantRoute);

        // 创建终点虚拟商户（与起点坐标相同）
        MerchantInfo endPoint = createVirtualMerchant("终点", startLat, startLon, "END");
        completeRoute.add(endPoint);

        return completeRoute;
    }

    /**
     * 创建起点虚拟商户点
     */
    private MerchantInfo createStartPointMerchant(Double startLng, Double startLat) {
        MerchantInfo startPoint = new MerchantInfo();
        startPoint.setId(-1L);
        startPoint.setMerchantName("起点");
        startPoint.setOriginalLongitude(startLng);
        startPoint.setOriginalLatitude(startLat);
        startPoint.setLongitudeAfterOffset(startLng);
        startPoint.setLatitudeAfterOffset(startLat);
        startPoint.setAddress("起点位置");
        startPoint.setBusinessAddress("起点位置");
        startPoint.setCounty("起点");
        startPoint.setMarketType("起点");
        return startPoint;
    }

    /**
     * 创建虚拟商户节点（用于表示起点和终点）
     */
    private MerchantInfo createVirtualMerchant(String name, Double lat, Double lon, String type) {
        MerchantInfo virtualMerchant = new MerchantInfo();
        virtualMerchant.setMerchantName(name);
        // 设置偏移后的坐标（用于高德地图显示）
        virtualMerchant.setLatitudeAfterOffset(lat);
        virtualMerchant.setLongitudeAfterOffset(lon);
        // 也设置原始坐标
        virtualMerchant.setOriginalLatitude(lat);
        virtualMerchant.setOriginalLongitude(lon);
        // 设置一个特殊的ID来标识这是虚拟节点
        virtualMerchant.setId(-1L); // 使用负数ID表示虚拟节点
        virtualMerchant.setMerchantCode("VIRTUAL_" + type);
        return virtualMerchant;
    }

    /**
     * 闭环路径的最近邻算法
     */
    public RouteResult optimizeCircularWithNearestNeighbor(List<MerchantInfo> merchants) {
        long startTime = System.currentTimeMillis();

        if (merchants.size() <= 1) {
            return new RouteResult(merchants, 0.0, "CIRCULAR_NEAREST_NEIGHBOR",
                System.currentTimeMillis() - startTime);
        }

        List<MerchantInfo> route = new ArrayList<>();
        Set<MerchantInfo> unvisited = new HashSet<>(merchants);

        // 从第一个商户开始
        MerchantInfo current = merchants.get(0);
        route.add(current);
        unvisited.remove(current);

        while (!unvisited.isEmpty()) {
            MerchantInfo nearest = null;
            double minDistance = Double.MAX_VALUE;

            for (MerchantInfo candidate : unvisited) {
                if (GeoUtils.hasValidCoordinates(candidate) && GeoUtils.hasValidCoordinates(current)) {
                    double distance = GeoUtils.calculateDistance(current, candidate);
                    if (distance < minDistance && distance <= maxSingleDistance) {
                        minDistance = distance;
                        nearest = candidate;
                    }
                }
            }

            if (nearest == null) {
                for (MerchantInfo candidate : unvisited) {
                    if (GeoUtils.hasValidCoordinates(candidate) && GeoUtils.hasValidCoordinates(current)) {
                        double distance = GeoUtils.calculateDistance(current, candidate);
                        if (distance < minDistance) {
                            minDistance = distance;
                            nearest = candidate;
                        }
                    }
                }
            }

            if (nearest != null) {
                route.add(nearest);
                unvisited.remove(nearest);
                current = nearest;
            } else {
                break;
            }
        }

        double totalDistance = calculateCircularRouteDistance(route);
        return new RouteResult(route, totalDistance, "CIRCULAR_NEAREST_NEIGHBOR",
            System.currentTimeMillis() - startTime);
    }

    /**
     * 闭环路径的2-opt算法
     */
    public RouteResult optimizeCircularWith2Opt(List<MerchantInfo> merchants) {
        long startTime = System.currentTimeMillis();

        // 先用最近邻得到初始解
        RouteResult initial = optimizeCircularWithNearestNeighbor(merchants);
        List<MerchantInfo> route = new ArrayList<>(initial.getRoute());

        boolean improved = true;
        int iterations = 0;
        int maxIterations = 1000;

        while (improved && iterations < maxIterations) {
            improved = false;
            iterations++;

            for (int i = 0; i < route.size() - 1; i++) {
                for (int j = i + 2; j < route.size(); j++) {
                    // 对于闭环，需要特殊处理边界情况
                    if (i == 0 && j == route.size() - 1) continue;

                    List<MerchantInfo> newRoute = perform2OptSwap(route, i, j);
                    double newDistance = calculateCircularRouteDistance(newRoute);
                    double currentDistance = calculateCircularRouteDistance(route);

                    if (newDistance < currentDistance) {
                        route = newRoute;
                        improved = true;
                    }
                }
            }
        }

        double totalDistance = calculateCircularRouteDistance(route);
        return new RouteResult(route, totalDistance, "CIRCULAR_2OPT",
            System.currentTimeMillis() - startTime);
    }

    /**
     * 闭环路径的改进算法
     */
    public RouteResult optimizeCircularWithImprovement(List<MerchantInfo> merchants) {
        long startTime = System.currentTimeMillis();

        // 先用最近邻得到初始解
        RouteResult initial = optimizeCircularWithNearestNeighbor(merchants);
        List<MerchantInfo> route = new ArrayList<>(initial.getRoute());

        boolean improved = true;
        int iterations = 0;
        int maxIterations = 500;

        while (improved && iterations < maxIterations) {
            improved = false;
            iterations++;

            for (int i = 0; i < route.size(); i++) {
                for (int j = 0; j < route.size(); j++) {
                    if (i == j) continue;

                    List<MerchantInfo> newRoute = new ArrayList<>(route);
                    MerchantInfo merchant = newRoute.remove(i);
                    newRoute.add(j, merchant);

                    double currentDistance = calculateCircularRouteDistance(route);
                    double newDistance = calculateCircularRouteDistance(newRoute);

                    if (newDistance < currentDistance) {
                        route = newRoute;
                        improved = true;
                        break;
                    }
                }
                if (improved) break;
            }
        }

        double totalDistance = calculateCircularRouteDistance(route);
        return new RouteResult(route, totalDistance, "CIRCULAR_IMPROVEMENT",
            System.currentTimeMillis() - startTime);
    }

    /**
     * 计算闭环路径的总距离（起点终点相同）
     */
    private double calculateCircularRouteDistance(List<MerchantInfo> route) {
        if (route == null || route.size() <= 1) {
            return 0.0;
        }

        double totalDistance = 0.0;

        // 计算相邻商户之间的距离
        for (int i = 0; i < route.size() - 1; i++) {
            if (GeoUtils.hasValidCoordinates(route.get(i)) && GeoUtils.hasValidCoordinates(route.get(i + 1))) {
                totalDistance += GeoUtils.calculateDistance(route.get(i), route.get(i + 1));
            }
        }

        // 闭环：从最后一个商户回到起点
        if (route.size() > 1) {
            MerchantInfo first = route.get(0);
            MerchantInfo last = route.get(route.size() - 1);
            if (GeoUtils.hasValidCoordinates(first) && GeoUtils.hasValidCoordinates(last)) {
                totalDistance += GeoUtils.calculateDistance(last, first);
            }
        }

        return totalDistance;
    }

    /**
     * 执行2-opt交换操作，用于闭环路径优化
     */
    private List<MerchantInfo> perform2OptSwap(List<MerchantInfo> route, int i, int j) {
        List<MerchantInfo> newRoute = new ArrayList<>();

        // 添加 0 到 i 的部分
        for (int k = 0; k <= i; k++) {
            newRoute.add(route.get(k));
        }

        // 反转 i+1 到 j 的部分
        for (int k = j; k >= i + 1; k--) {
            newRoute.add(route.get(k));
        }

        // 添加 j+1 到 end 的部分
        for (int k = j + 1; k < route.size(); k++) {
            newRoute.add(route.get(k));
        }

        return newRoute;
    }

    /**
     * 调整路径顺序，使其从起点开始
     */
    private List<MerchantInfo> adjustRouteToStartFromOrigin(List<MerchantInfo> routeWithStart, MerchantInfo startPoint) {
        logger.info("=== TSP优化后的路径（包含虚拟起点）===");
        for (int i = 0; i < routeWithStart.size(); i++) {
            MerchantInfo merchant = routeWithStart.get(i);
            String type = merchant.getId().equals(startPoint.getId()) ? "[虚拟起点]" : "[商户]";
            logger.info("{}. {} {} (ID: {}, 坐标: {}, {})",
                i + 1,
                type,
                merchant.getMerchantName(),
                merchant.getId(),
                merchant.getLongitude(),
                merchant.getLatitude());
        }

        // 找到起点在路径中的位置
        int startIndex = -1;
        for (int i = 0; i < routeWithStart.size(); i++) {
            if (routeWithStart.get(i).getId().equals(startPoint.getId())) {
                startIndex = i;
                break;
            }
        }

        if (startIndex == -1) {
            logger.warn("未找到虚拟起点，直接返回商户列表");
            // 如果没找到起点，返回原有商户列表（移除起点）
            return routeWithStart.stream()
                    .filter(m -> !m.getId().equals(startPoint.getId()))
                    .collect(Collectors.toList());
        }

        // 重新排列路径，使其从起点开始，并移除起点（因为起点不是真正的商户）
        List<MerchantInfo> adjustedRoute = new ArrayList<>();

        // 从起点的下一个位置开始添加商户
        for (int i = 1; i < routeWithStart.size(); i++) {
            int index = (startIndex + i) % routeWithStart.size();
            MerchantInfo merchant = routeWithStart.get(index);
            if (!merchant.getId().equals(startPoint.getId())) {
                adjustedRoute.add(merchant);
            }
        }

        logger.info("=== 调整后的最终访问顺序（从起点开始）===");
        logger.info("起点: 虚拟起点 (坐标: {}, {})", startPoint.getLongitude(), startPoint.getLatitude());
        for (int i = 0; i < adjustedRoute.size(); i++) {
            MerchantInfo merchant = adjustedRoute.get(i);
            logger.info("{}. {} (ID: {}, 坐标: {}, {})",
                i + 1,
                merchant.getMerchantName(),
                merchant.getId(),
                merchant.getLongitude(),
                merchant.getLatitude());
        }
        logger.info("终点: 回到起点 (坐标: {}, {})", startPoint.getLongitude(), startPoint.getLatitude());

        logger.info("调整环形路径：起点位置在索引 {}，调整后访问 {} 个商户", startIndex, adjustedRoute.size());

        return adjustedRoute;
    }
}

package com.hz.merchant.service;

import com.hz.merchant.domain.MerchantInfo;
import com.hz.merchant.domain.MerchantSelectionRequest;
import com.hz.merchant.domain.MerchantSelectionResponse;
import java.util.List;

/**
 * 商户选择服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
public interface IMerchantSelectionService {
    

    
    /**
     * 根据条件抽取商户
     * 
     * @param deptId 地区ID
     * @param chengxindengji 诚信等级
     * @param zhongduancengji 终端类型
     * @param jingyingguimo 经营规模
     * @param num 抽取数量
     * @return 抽取的商户列表
     */
    List<MerchantInfo> selectMerchantsByConditions(Long deptId, String chengxindengji, 
                                                  String zhongduancengji, String jingyingguimo, Integer num);

    /**
     * 商户抽取（智能抽取 + 条件抽取）
     *
     * @param request 抽取请求参数
     * @return 抽取结果
     */
    MerchantSelectionResponse selectMerchants(MerchantSelectionRequest request);
} 
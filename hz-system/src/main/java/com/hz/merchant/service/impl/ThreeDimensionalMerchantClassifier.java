package com.hz.merchant.service.impl;

import com.hz.merchant.constant.MerchantConstants;
import com.hz.merchant.domain.MerchantInfo;
import com.hz.merchant.service.impl.MultiDimensionalQuotaCalculator.MerchantCategory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 三维商户分类器
 * 基于地域、终端类型、采集类型进行三维分类
 */
@Component
public class ThreeDimensionalMerchantClassifier {
    
    private static final Logger logger = LoggerFactory.getLogger(ThreeDimensionalMerchantClassifier.class);
    
    /**
     * 分类商户
     * @param merchant 商户信息
     * @return 商户类别
     */
    public MerchantCategory classifyMerchant(MerchantInfo merchant) {
        if (merchant == null) {
            throw new IllegalArgumentException(MerchantConstants.ERROR_MERCHANT_NULL);
        }
        
        // 地域分类
        boolean isUrban = isUrbanMerchant(merchant);
        
        // 终端类型分类
        TerminalType terminalType = classifyTerminalType(merchant);
        
        // 采集类型分类
        boolean isCollection = isCollectionMerchant(merchant);
        
        // 组合分类
        return getCombinedCategory(isUrban, terminalType, isCollection);
    }
    
    /**
     * 判断是否为城镇商户
     */
    private boolean isUrbanMerchant(MerchantInfo merchant) {
        String marketType = merchant.getMarketType();
        if (marketType == null || marketType.trim().isEmpty()) {
            // 默认为城镇商户
            return true;
        }
        
        // 包含"乡村"关键字的为农村商户，其他为城镇商户
        return !marketType.contains(MerchantConstants.RURAL_KEYWORD);
    }
    
    /**
     * 终端类型枚举
     */
    public enum TerminalType {
        AFFILIATE,    // 加盟店
        ORDINARY,     // 普通终端
        MODERN        // 现代终端
    }
    
    /**
     * 分类终端类型
     */
    private TerminalType classifyTerminalType(MerchantInfo merchant) {
        String zhongduancengji = merchant.getZhongduancengji();
        if (zhongduancengji == null || zhongduancengji.trim().isEmpty()) {
            // 默认为普通终端
            return TerminalType.ORDINARY;
        }
        
        String type = zhongduancengji.toLowerCase();
        
        // 加盟店判断
        if (type.contains(MerchantConstants.AFFILIATE_KEYWORD)) {
            return TerminalType.AFFILIATE;
        }

        // 现代终端判断
        if (type.contains(MerchantConstants.MODERN_TERMINAL_KEYWORD)) {
            return TerminalType.MODERN;
        }
        
        // 普通终端判断（默认）
        return TerminalType.ORDINARY;
    }
    
    /**
     * 判断是否为采集商户
     * 数据库中 collectionType 是 int 类型：
     * - 1 表示采集
     * - 0 或 null 表示非采集
     */
    private boolean isCollectionMerchant(MerchantInfo merchant) {
        Integer collectionType = merchant.getCollectionType();

        // 调试日志：输出原始值
        //logger.debug("商户[{}] collectionType原始值: [{}]", merchant.getMerchantName(), collectionType);

        // 如果为 null，则为非采集
        if (collectionType == null) {
            //logger.debug("商户[{}] collectionType为null，判定为非采集", merchant.getMerchantName());
            return false;
        }

        // 直接比较整数值
        boolean isCollection = collectionType.equals(MerchantConstants.COLLECTION_TYPE_YES);
//        logger.debug("商户[{}] collectionType数值: {}, 判定为: {}",
//            merchant.getMerchantName(), collectionType, isCollection ? "采集" : "非采集");
        return isCollection;
    }
    
    /**
     * 获取组合分类
     */
    private MerchantCategory getCombinedCategory(boolean isUrban, TerminalType terminalType, boolean isCollection) {
        if (isUrban) {
            // 城镇商户
            switch (terminalType) {
                case AFFILIATE:
                    return isCollection ? MerchantCategory.URBAN_AFFILIATE_COLLECTION 
                                       : MerchantCategory.URBAN_AFFILIATE_NON_COLLECTION;
                case ORDINARY:
                    return isCollection ? MerchantCategory.URBAN_ORDINARY_COLLECTION 
                                       : MerchantCategory.URBAN_ORDINARY_NON_COLLECTION;
                case MODERN:
                    return isCollection ? MerchantCategory.URBAN_MODERN_COLLECTION 
                                       : MerchantCategory.URBAN_MODERN_NON_COLLECTION;
            }
        } else {
            // 乡村商户
            switch (terminalType) {
                case AFFILIATE:
                    return isCollection ? MerchantCategory.RURAL_AFFILIATE_COLLECTION 
                                       : MerchantCategory.RURAL_AFFILIATE_NON_COLLECTION;
                case ORDINARY:
                    return isCollection ? MerchantCategory.RURAL_ORDINARY_COLLECTION 
                                       : MerchantCategory.RURAL_ORDINARY_NON_COLLECTION;
                case MODERN:
                    return isCollection ? MerchantCategory.RURAL_MODERN_COLLECTION 
                                       : MerchantCategory.RURAL_MODERN_NON_COLLECTION;
            }
        }
        
        // 默认情况（不应该到达这里）
        return MerchantCategory.URBAN_ORDINARY_COLLECTION;
    }
    
    /**
     * 获取商户分类详情
     */
    public String getClassificationDetails(MerchantInfo merchant) {
        boolean isUrban = isUrbanMerchant(merchant);
        TerminalType terminalType = classifyTerminalType(merchant);
        boolean isCollection = isCollectionMerchant(merchant);
        MerchantCategory category = getCombinedCategory(isUrban, terminalType, isCollection);
        
        return String.format("商户[%s] 分类详情: 地域=%s, 终端类型=%s, 采集类型=%s, 最终分类=%s",
                merchant.getMerchantName(),
                isUrban ? "城镇" : "乡村",
                getTerminalTypeName(terminalType),
                isCollection ? "采集" : "非采集",
                category.getDescription());
    }
    
    /**
     * 获取终端类型名称
     */
    private String getTerminalTypeName(TerminalType type) {
        switch (type) {
            case AFFILIATE: return "加盟店";
            case ORDINARY: return "普通终端";
            case MODERN: return "现代终端";
            default: return "未知";
        }
    }
    
    
    
    /**
     * 批量分类商户
     */
    public java.util.Map<MerchantCategory, java.util.List<MerchantInfo>> classifyMerchants(java.util.List<MerchantInfo> merchants) {
        java.util.Map<MerchantCategory, java.util.List<MerchantInfo>> result = new java.util.HashMap<>();

        // 初始化所有类别
        for (MerchantCategory category : MerchantCategory.values()) {
            result.put(category, new java.util.ArrayList<>());
        }

        // 统计采集类型的分布情况
        java.util.Map<String, Integer> collectionTypeStats = new java.util.HashMap<>();
        int collectionCount = 0;
        int nonCollectionCount = 0;

        // 分类商户
        for (MerchantInfo merchant : merchants) {
            try {
                // 统计采集类型分布
                Integer collectionType = merchant.getCollectionType();
                String collectionTypeStr = collectionType == null ? "null" : collectionType.toString();
                collectionTypeStats.put(collectionTypeStr, collectionTypeStats.getOrDefault(collectionTypeStr, 0) + 1);

                MerchantCategory category = classifyMerchant(merchant);
                result.get(category).add(merchant);

                // 统计采集/非采集数量
                if (isCollectionMerchant(merchant)) {
                    collectionCount++;
                } else {
                    nonCollectionCount++;
                }

            } catch (IllegalArgumentException e) {
                logger.warn("商户分类参数错误: 商户[{}], 错误: {}", merchant.getMerchantName(), e.getMessage());
                // 参数错误时，将商户分类为默认类别
                result.get(MerchantCategory.URBAN_ORDINARY_NON_COLLECTION).add(merchant);
            } catch (Exception e) {
                logger.error("商户分类异常: 商户[{}], 错误: {}", merchant.getMerchantName(), e.getMessage(), e);
                // 其他异常时，也将商户分类为默认类别
                result.get(MerchantCategory.URBAN_ORDINARY_NON_COLLECTION).add(merchant);
            }
        }

        // 输出采集类型统计信息
//        logger.info("=== 采集类型数据分析 ===");
//        logger.info("总商户数: {}", merchants.size());
//        logger.info("采集商户数: {}", collectionCount);
//        logger.info("非采集商户数: {}", nonCollectionCount);
//        logger.info("采集类型字段值分布:");
//        collectionTypeStats.entrySet().stream()
//            .sorted((a, b) -> b.getValue().compareTo(a.getValue()))
//            .forEach(entry -> logger.info("  '{}': {} 个商户", entry.getKey(), entry.getValue()));
//        logger.info("========================");

        return result;
    }
}

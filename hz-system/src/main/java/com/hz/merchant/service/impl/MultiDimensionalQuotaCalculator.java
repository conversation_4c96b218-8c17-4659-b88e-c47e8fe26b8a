package com.hz.merchant.service.impl;

import com.hz.merchant.config.MerchantSelectionProperties;
import com.hz.merchant.constant.MerchantConstants;
import com.hz.merchant.util.RatioParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 12类别配额计算器
 * 基于三维分类计算商户配额分配
 */
@Component
public class MultiDimensionalQuotaCalculator {
    
    private static final Logger logger = LoggerFactory.getLogger(MultiDimensionalQuotaCalculator.class);
    
    /**
     * 商户分类枚举
     */
    public enum MerchantCategory {
        // 城镇-加盟店
        URBAN_AFFILIATE_COLLECTION("城镇-加盟店-采集"),
        URBAN_AFFILIATE_NON_COLLECTION("城镇-加盟店-非采集"),
        
        // 城镇-普通终端
        URBAN_ORDINARY_COLLECTION("城镇-普通终端-采集"),
        URBAN_ORDINARY_NON_COLLECTION("城镇-普通终端-非采集"),
        
        // 城镇-现代终端
        URBAN_MODERN_COLLECTION("城镇-现代终端-采集"),
        URBAN_MODERN_NON_COLLECTION("城镇-现代终端-非采集"),
        
        // 乡村-加盟店
        RURAL_AFFILIATE_COLLECTION("乡村-加盟店-采集"),
        RURAL_AFFILIATE_NON_COLLECTION("乡村-加盟店-非采集"),
        
        // 乡村-普通终端
        RURAL_ORDINARY_COLLECTION("乡村-普通终端-采集"),
        RURAL_ORDINARY_NON_COLLECTION("乡村-普通终端-非采集"),
        
        // 乡村-现代终端
        RURAL_MODERN_COLLECTION("乡村-现代终端-采集"),
        RURAL_MODERN_NON_COLLECTION("乡村-现代终端-非采集");
        
        private final String description;
        
        MerchantCategory(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 计算各类别配额
     * @param properties 配置属性
     * @return 各类别配额映射
     */
    public Map<MerchantCategory, Integer> calculateQuotas(MerchantSelectionProperties properties) {
        if (properties == null) {
            throw new IllegalArgumentException("配置属性不能为空");
        }

        int totalCount = properties.getTotalCount();
        if (totalCount <= 0) {
            throw new IllegalArgumentException("总数量必须大于0");
        }

        try {
            // 解析比例
            double[] areaRatios = RatioParser.parseAreaRatio(properties.getAreaRatio());
            double[] terminalRatios = RatioParser.parseTerminalRatio(properties.getTerminalRatio());
            double[] collectionRatios = RatioParser.parseCollectionRatio(properties.getCollectionRatio());
        
        double urbanRatio = areaRatios[0];      // 城镇比例
        double ruralRatio = areaRatios[1];      // 乡村比例
        
        double affiliateRatio = terminalRatios[0];  // 加盟店比例
        double ordinaryRatio = terminalRatios[1];   // 普通终端比例
        double modernRatio = terminalRatios[2];     // 现代终端比例
        
        double collectionRatio = collectionRatios[0];    // 采集比例
        double nonCollectionRatio = collectionRatios[1]; // 非采集比例
        
        Map<MerchantCategory, Integer> quotas = new HashMap<>();
        
        // 计算12个类别的配额
        quotas.put(MerchantCategory.URBAN_AFFILIATE_COLLECTION, 
                   calculateQuota(totalCount, urbanRatio, affiliateRatio, collectionRatio));
        quotas.put(MerchantCategory.URBAN_AFFILIATE_NON_COLLECTION, 
                   calculateQuota(totalCount, urbanRatio, affiliateRatio, nonCollectionRatio));
        
        quotas.put(MerchantCategory.URBAN_ORDINARY_COLLECTION, 
                   calculateQuota(totalCount, urbanRatio, ordinaryRatio, collectionRatio));
        quotas.put(MerchantCategory.URBAN_ORDINARY_NON_COLLECTION, 
                   calculateQuota(totalCount, urbanRatio, ordinaryRatio, nonCollectionRatio));
        
        quotas.put(MerchantCategory.URBAN_MODERN_COLLECTION, 
                   calculateQuota(totalCount, urbanRatio, modernRatio, collectionRatio));
        quotas.put(MerchantCategory.URBAN_MODERN_NON_COLLECTION, 
                   calculateQuota(totalCount, urbanRatio, modernRatio, nonCollectionRatio));
        
        quotas.put(MerchantCategory.RURAL_AFFILIATE_COLLECTION, 
                   calculateQuota(totalCount, ruralRatio, affiliateRatio, collectionRatio));
        quotas.put(MerchantCategory.RURAL_AFFILIATE_NON_COLLECTION, 
                   calculateQuota(totalCount, ruralRatio, affiliateRatio, nonCollectionRatio));
        
        quotas.put(MerchantCategory.RURAL_ORDINARY_COLLECTION, 
                   calculateQuota(totalCount, ruralRatio, ordinaryRatio, collectionRatio));
        quotas.put(MerchantCategory.RURAL_ORDINARY_NON_COLLECTION, 
                   calculateQuota(totalCount, ruralRatio, ordinaryRatio, nonCollectionRatio));
        
        quotas.put(MerchantCategory.RURAL_MODERN_COLLECTION, 
                   calculateQuota(totalCount, ruralRatio, modernRatio, collectionRatio));
        quotas.put(MerchantCategory.RURAL_MODERN_NON_COLLECTION, 
                   calculateQuota(totalCount, ruralRatio, modernRatio, nonCollectionRatio));
        
        // 调整配额确保总和等于目标值
        quotas = adjustQuotas(quotas, totalCount);
        
            // 记录配额分配信息
            logQuotaDistribution(quotas, properties);

            return quotas;

        } catch (IllegalArgumentException e) {
            logger.error("配额计算参数错误: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("配额计算异常", e);
            throw new RuntimeException("配额计算失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 计算单个类别配额
     */
    private int calculateQuota(int totalCount, double areaRatio, double terminalRatio, double collectionRatio) {
        double quota = totalCount * areaRatio * terminalRatio * collectionRatio;
        return (int) Math.round(quota);
    }
    
    /**
     * 调整配额确保总和等于目标值
     */
    private Map<MerchantCategory, Integer> adjustQuotas(Map<MerchantCategory, Integer> quotas, int targetTotal) {
        int actualTotal = quotas.values().stream().mapToInt(Integer::intValue).sum();
        int difference = targetTotal - actualTotal;
        
        if (difference == 0) {
            return quotas;
        }
        
        logger.info("配额调整：目标总数={}, 实际总数={}, 差值={}", targetTotal, actualTotal, difference);
        
        // 按配额大小排序，优先调整配额较大的类别
        MerchantCategory[] categories = quotas.entrySet().stream()
                .sorted((a, b) -> b.getValue().compareTo(a.getValue()))
                .map(Map.Entry::getKey)
                .toArray(MerchantCategory[]::new);
        
        // 分配差值
        int step = difference > 0 ? 1 : -1;
        int remaining = Math.abs(difference);
        int index = 0;
        
        while (remaining > 0 && index < categories.length) {
            MerchantCategory category = categories[index];
            int currentQuota = quotas.get(category);
            
            // 确保配额不会变成负数
            if (step > 0 || currentQuota > 0) {
                quotas.put(category, currentQuota + step);
                remaining--;
            }
            
            index = (index + 1) % categories.length;
        }
        
        return quotas;
    }
    
    /**
     * 记录配额分配信息
     */
    private void logQuotaDistribution(Map<MerchantCategory, Integer> quotas, MerchantSelectionProperties properties) {
        logger.info("=== 12类别配额分配 ===");
        logger.info("总配额: {}", properties.getTotalCount());
        logger.info("地域比例: {}", properties.getAreaRatio());
        logger.info("终端比例: {}", properties.getTerminalRatio());
        logger.info("采集比例: {}", properties.getCollectionRatio());
        logger.info("--- 配额分配详情 ---");
        
        int totalAllocated = 0;
        for (MerchantCategory category : MerchantCategory.values()) {
            int quota = quotas.get(category);
            totalAllocated += quota;
            logger.info("{}: {}", category.getDescription(), quota);
        }
        
        logger.info("总分配配额: {}", totalAllocated);
        logger.info("==================");
    }
    
    
}

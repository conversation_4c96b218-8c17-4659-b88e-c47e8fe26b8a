package com.hz.merchant.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import com.alibaba.fastjson2.JSON;
import com.hz.common.annotation.DataScope;
import com.hz.common.utils.DateUtils;
import com.hz.merchant.constant.MerchantRedisConstant;
import com.hz.merchant.config.MerchantSelectionProperties;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import com.hz.merchant.mapper.MerchantInfoMapper;
import com.hz.merchant.domain.MerchantInfo;
import com.hz.merchant.service.IMerchantInfoService;

/**
 * 商户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class MerchantInfoServiceImpl implements IMerchantInfoService{

    private final Logger logger = LoggerFactory.getLogger(MerchantInfoServiceImpl.class);
    @Resource
    private MerchantInfoMapper merchantInfoMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private MerchantSelectionProperties selectionProperties;

    /**
     * 查询商户信息
     *
     * @param id 商户信息主键
     * @return 商户信息
     */
    @Override
    public MerchantInfo selectMerchantInfoById(Long id){
        String key = MerchantRedisConstant.MERCHANT_INFO_ID + id;
        if(Boolean.TRUE.equals(stringRedisTemplate.hasKey(key))){
            String value = stringRedisTemplate.opsForValue().get(key);
            return JSON.parseObject(value, MerchantInfo.class);
        }else{
            MerchantInfo merchantInfo = merchantInfoMapper.selectMerchantInfoById(id);
            stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(merchantInfo), 20, TimeUnit.MINUTES);
            return merchantInfo;
        }
    }

    /**
     * 查询商户信息
     *
     * @param shopLicence 商户许可证号
     * @return 商户信息
     */
    @Override
    public MerchantInfo selectMerchantInfoByLicence(String shopLicence){
        String key = MerchantRedisConstant.MERCHANT_INFO_LICENCE + shopLicence;
        if(Boolean.TRUE.equals(stringRedisTemplate.hasKey(key))){
            String value = stringRedisTemplate.opsForValue().get(key);
            return JSON.parseObject(value, MerchantInfo.class);
        }else{
            MerchantInfo merchantInfo = merchantInfoMapper.selectMerchantInfoByLicence(shopLicence);
            stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(merchantInfo), 30, TimeUnit.DAYS);
            return merchantInfo;
        }
    }

    /**
     * 查询商户信息列表
     *
     * @param merchantInfo 商户信息
     * @return 商户信息
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<MerchantInfo> selectMerchantInfoList(MerchantInfo merchantInfo){
        return merchantInfoMapper.selectMerchantInfoList(merchantInfo);
    }

    /**
     * 新增商户信息
     *
     * @param merchantInfo 商户信息
     * @return 结果
     */
    @Override
    public int insertMerchantInfo(MerchantInfo merchantInfo){
        // 如果商户状态为空，设置默认值为"有效"
        if(merchantInfo.getMerchantStatue() == null || merchantInfo.getMerchantStatue().trim().isEmpty()){
            merchantInfo.setMerchantStatue("有效");
        }
        merchantInfo.setCreatTime(DateUtils.getNowDate());
        int i = merchantInfoMapper.insertMerchantInfo(merchantInfo);
        stringRedisTemplate.delete(MerchantRedisConstant.MERCHANT_INFO_ID + merchantInfo.getId());
        stringRedisTemplate.delete(MerchantRedisConstant.MERCHANT_INFO_LICENCE + merchantInfo.getLicence());
        return i;
    }

    /**
     * 修改商户信息
     *
     * @param merchantInfo 商户信息
     * @return 结果
     */
    @Override
    public int updateMerchantInfo(MerchantInfo merchantInfo){
        merchantInfo.setUpdateTime(DateUtils.getNowDate());
        int i = merchantInfoMapper.updateMerchantInfo(merchantInfo);
        stringRedisTemplate.delete(MerchantRedisConstant.MERCHANT_INFO_ID + merchantInfo.getId());
        stringRedisTemplate.delete(MerchantRedisConstant.MERCHANT_INFO_LICENCE + merchantInfo.getLicence());
        return i;
    }

    /**
     * 批量删除商户信息
     *
     * @param ids 需要删除的商户信息主键
     * @return 结果
     */
    @Override
    public int deleteMerchantInfoByIds(Long[] ids){
        if (ids == null || ids.length == 0) {
            return 0;
        }
        // 1. 一次性获取所有待删除商户的信息，避免N+1查询
        List<MerchantInfo> merchantsToDelete = merchantInfoMapper.selectMerchantInfoByIds(ids);
        if (merchantsToDelete == null || merchantsToDelete.isEmpty()) {
            // 如果根据ID查不到任何商户，可能数据库记录已被删除，直接返回
            return 0;
        }

        // 2. 准备要删除的缓存键
        List<String> keysToDelete = new ArrayList<>();
        for(MerchantInfo merchant : merchantsToDelete){
            keysToDelete.add(MerchantRedisConstant.MERCHANT_INFO_ID + merchant.getId());
            if (merchant.getLicence() != null) {
                keysToDelete.add(MerchantRedisConstant.MERCHANT_INFO_LICENCE + merchant.getLicence());
            }
        }

        // 3. 批量删除缓存
        if (!keysToDelete.isEmpty()) {
            stringRedisTemplate.delete(keysToDelete);
        }

        // 4. 批量删除数据库记录
        return merchantInfoMapper.deleteMerchantInfoByIds(ids);
    }

    /**
     * 删除商户信息信息
     *
     * @param id 商户信息主键
     * @return 结果
     */
    @Override
    public int deleteMerchantInfoById(Long id){
        // 在单个删除时，我们可能不知道licence，所以先获取一下
        MerchantInfo merchantInfo = this.selectMerchantInfoById(id);
        if (merchantInfo != null) {
            if (merchantInfo.getLicence() != null) {
                stringRedisTemplate.delete(MerchantRedisConstant.MERCHANT_INFO_LICENCE + merchantInfo.getLicence());
            }
        }
        stringRedisTemplate.delete(MerchantRedisConstant.MERCHANT_INFO_ID + id);
        return merchantInfoMapper.deleteMerchantInfoById(id);
    }

    /**
     * 修改商户状态
     *
     * @param merchantInfo 商户信息
     * @return 结果
     */
    @Override
    public int updateMerchantStatus(MerchantInfo merchantInfo){
        stringRedisTemplate.delete(MerchantRedisConstant.MERCHANT_INFO_ID + merchantInfo.getId());
        stringRedisTemplate.delete(MerchantRedisConstant.MERCHANT_INFO_LICENCE + merchantInfo.getLicence());
        merchantInfo.setUpdateTime(DateUtils.getNowDate());
        return merchantInfoMapper.updateMerchantStatus(merchantInfo);
    }
}

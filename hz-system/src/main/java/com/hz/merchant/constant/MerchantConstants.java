package com.hz.merchant.constant;

/**
 * 商户相关业务常量定义
 * 包含业务规则、分类标准、默认配置等常量
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public class MerchantConstants {
    
    // ==================== 业务规则常量 ====================
    /** 采集类型：采集 */
    public static final int COLLECTION_TYPE_YES = 1;
    
    /** 采集类型：非采集 */
    public static final int COLLECTION_TYPE_NO = 0;
    
    /** 地域类型：乡村关键字 */
    public static final String RURAL_KEYWORD = "乡村";
    
    /** 终端类型：加盟店关键字 */
    public static final String AFFILIATE_KEYWORD = "加盟";
    
    /** 终端类型：现代终端关键字 */
    public static final String MODERN_TERMINAL_KEYWORD = "现代";
    
    /** 采集类型：采集关键字 */
    public static final String COLLECTION_KEYWORD = "采集";
    
    /** 采集类型：非采集关键字 */
    public static final String NON_COLLECTION_KEYWORD = "非采集";
    
    // ==================== 坐标验证常量 ====================
    /** 无效坐标值 */
    public static final double INVALID_COORDINATE = 0.0;
    
    /** 地球半径（公里） */
    public static final double EARTH_RADIUS_KM = 6371.0;
    
    /** 最大单次距离（公里） */
    public static final double MAX_SINGLE_DISTANCE_KM = 1000.0;
    
    // ==================== 默认配置常量 ====================
    /** 默认总数量 */
    public static final int DEFAULT_TOTAL_COUNT = 30;
    
    /** 默认地域比例 */
    public static final String DEFAULT_AREA_RATIO = "6:4";
    
    /** 默认终端比例 */
    public static final String DEFAULT_TERMINAL_RATIO = "3:5:2";
    
    /** 默认采集比例 */
    public static final String DEFAULT_COLLECTION_RATIO = "8:2";
    
    // ==================== 错误消息常量 ====================
    /** 商户信息为空错误 */
    public static final String ERROR_MERCHANT_NULL = "商户信息不能为空";
    
    /** 比例字符串为空错误 */
    public static final String ERROR_RATIO_EMPTY = "比例字符串不能为空";
    
    /** 比例格式错误 */
    public static final String ERROR_RATIO_FORMAT = "比例字符串格式错误";
    
    /** 比例值为负数错误 */
    public static final String ERROR_RATIO_NEGATIVE = "比例值不能为负数";
    
    /** 数据库访问失败错误 */
    public static final String ERROR_DATABASE_ACCESS = "数据库访问失败";
    
    /** 商户分类失败错误 */
    public static final String ERROR_MERCHANT_CLASSIFICATION = "商户分类失败";
    
    /** 智能选择失败错误 */
    public static final String ERROR_INTELLIGENT_SELECTION = "智能选择失败";
    
    /** 条件选择成功消息 */
    public static final String MSG_CONDITIONAL_SUCCESS = "条件抽取成功";
    
    /** 智能选择成功消息 */
    public static final String MSG_INTELLIGENT_SUCCESS = "智能抽取成功";
    
    /** 未找到商户消息 */
    public static final String MSG_NO_MERCHANTS_FOUND = "未找到符合条件的商户";
    
    /** 配额不足消息 */
    public static final String MSG_INSUFFICIENT_QUOTA = "未能找到符合条件的商户组合";
    
    // ==================== 日志消息常量 ====================
    /** 开始智能选择日志 */
    public static final String LOG_START_INTELLIGENT_SELECTION = "开始智能商户选择";
    
    /** 完成智能选择日志 */
    public static final String LOG_COMPLETE_INTELLIGENT_SELECTION = "智能商户选择完成";
    
    /** 开始商户分类日志 */
    public static final String LOG_START_CLASSIFICATION = "开始商户分类统计";
    
    /** 配额调整日志 */
    public static final String LOG_QUOTA_ADJUSTMENT = "配额调整";
    
    // ==================== 私有构造函数 ====================
    private MerchantConstants() {
        // 防止实例化
    }
}

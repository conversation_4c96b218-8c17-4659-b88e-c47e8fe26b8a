package com.hz.gather.service;

import com.hz.gather.domain.GatherStInventory;

import java.util.HashMap;
import java.util.List;

/**
 * 调研库存Service接口
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface IGatherStInventoryService{
    /**
     * 查询调研库存
     *
     * @param id 调研库存主键
     * @return 调研库存
     */
    GatherStInventory selectById(Long id);

    /**
     * 查询调研库存列表
     *
     * @param inventory 调研库存
     * @return 调研库存集合
     */
    List<GatherStInventory> selectList(GatherStInventory inventory);

    /**
     * 新增调研库存
     *
     * @param inventory 调研库存
     * @return 结果
     */
    int insert(GatherStInventory inventory);

    /**
     * 批量插入调研价格
     *
     * @param inventoryList 调研价格列表
     * @return 插入成功的记录数
     */
    int insertBatch(List<GatherStInventory> inventoryList);

    /**
     * 修改调研库存
     *
     * @param inventory 调研库存
     * @return 结果
     */
    int update(GatherStInventory inventory);

    /**
     * 批量删除调研库存
     *
     * @param ids 需要删除的调研库存主键集合
     * @return 结果
     */
    int deleteByIds(Long[] ids);

    /**
     * 删除调研库存信息
     *
     * @param id 调研库存主键
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 根据任务ID删除调研库存信息
     *
     * @param taskId 任务ID
     * @return 结果
     */
    int deleteByTaskId(long taskId);

    /**
     * 批量更新
     *
     * @param ids
     * @param clear2
     */
    void batchUpdateClear2(List<Long> ids, int clear2);

    /**
     * 获取调研任务的数据
     *
     * @param taskId  任务ID
     * @param operate 1:原始数据 2:清洗后数据 3:对比数据 4:清洗提示
     * @return 表格数据
     */
    HashMap<String, Object> getTableData(String taskId, String operate);
}

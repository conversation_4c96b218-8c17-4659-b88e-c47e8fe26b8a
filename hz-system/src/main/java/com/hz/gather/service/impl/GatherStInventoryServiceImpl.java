package com.hz.gather.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.hz.common.utils.DateUtils;
import com.hz.common.utils.SecurityUtils;
import com.hz.common.utils.StringUtils;
import com.hz.gather.domain.GatherSmokePrice;
import com.hz.gather.domain.GatherStInventory;
import com.hz.gather.domain.GatherStPrice;
import com.hz.gather.mapper.GatherStInventoryMapper;
import com.hz.gather.service.IGatherSmokePriceService;
import com.hz.gather.service.IGatherStInventoryService;
import com.hz.merchant.domain.MerchantInfo;
import com.hz.merchant.service.IMerchantInfoService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.ListUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 调研库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class GatherStInventoryServiceImpl implements IGatherStInventoryService{
    @Resource
    private GatherStInventoryMapper inventoryMapper;
    @Resource
    private IMerchantInfoService merchantInfoService;
    @Resource
    private IGatherSmokePriceService smokePriceService;
    @Resource
    private SqlSessionFactory sqlSessionFactory;

    /**
     * 查询调研库存
     *
     * @param id 调研库存主键
     * @return 调研库存
     */
    @Override
    public GatherStInventory selectById(Long id){
        return inventoryMapper.selectById(id);
    }

    /**
     * 查询调研库存列表
     *
     * @param inventory 调研库存
     * @return 调研库存
     */
    @Override
    public List<GatherStInventory> selectList(GatherStInventory inventory){
        return inventoryMapper.selectList(inventory);
    }

    /**
     * 新增调研库存
     *
     * @param inserted 调研库存
     * @return 结果
     */
    @Override
    public int insert(GatherStInventory inserted){
        return inventoryMapper.insert(inserted);
    }

    /**
     * 批量插入调研库存
     *
     * @param inventoryList 调研库存列表
     * @return 插入成功的记录数
     */
    @Override
    public int insertBatch(List<GatherStInventory> inventoryList){
        int batchSize = 100;
        int totalInserted = 0;
        for(int i = 0; i < inventoryList.size(); i += batchSize){
            int endIndex = Math.min(i + batchSize, inventoryList.size());
            List<GatherStInventory> batch = inventoryList.subList(i, endIndex);

            // 为每个调研库存对象设置创建信息
            for(GatherStInventory inventory : batch){
                inventory.setCreateId(SecurityUtils.getUserId());
                inventory.setCreateBy(SecurityUtils.getUsername());
                inventory.setCreateTime(DateUtils.getNowDate());
            }

            try{
                totalInserted += inventoryMapper.insertBatch(batch);
            }catch(Exception e){
                e.printStackTrace();
            }
        }
        return totalInserted;
    }

    /**
     * 修改调研库存
     *
     * @param inventory 调研库存
     * @return 结果
     */
    @Override
    public int update(GatherStInventory inventory){
        return inventoryMapper.update(inventory);
    }

    /**
     * 批量删除调研库存
     *
     * @param ids 需要删除的调研库存主键
     * @return 结果
     */
    @Override
    public int deleteByIds(Long[] ids){
        return inventoryMapper.deleteByIds(ids);
    }

    /**
     * 删除调研库存信息
     *
     * @param id 调研库存主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id){
        return inventoryMapper.deleteById(id);
    }

    /**
     * 根据任务ID删除调研库存信息
     *
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    public int deleteByTaskId(long taskId){
        return inventoryMapper.deleteByTaskId(taskId);
    }

    /**
     * 批量更新
     *
     * @param ids
     * @param clear2
     */
    @Override
    public void batchUpdateClear2(List<Long> ids, int clear2) {
        List<List<Long>> lists = ListUtils.partition(ids, 500);
        for (List<Long> list : lists) {
            inventoryMapper.batchUpdateClear2(list, clear2);
        }
    }

    /**
     * 获取调研任务的数据
     *
     * @param taskId  任务ID
     * @param operate 1:原始数据 2:清洗后数据 3:对比数据 4:清洗提示
     * @return 表格数据
     */
    @Override
    public HashMap<String, Object> getTableData(String taskId, String operate){
        List<String> tableHeader;
        List<List<String>> tableData = new ArrayList<>();
        if("4".equals(operate)){
            tableHeader = new ArrayList<>(){{
                add("营销线路");
                add("商户数量(未去重)");
                add("商户数量(去重)");
            }};
            List<JSONObject> list = inventoryMapper.countYxxShopNumOfErrorByTaskId(taskId);
            for(JSONObject json : list){
                List<String> row = new ArrayList<>();
                row.add(json.getString("sh_yxx"));
                row.add(json.getString("sh_xkz_count"));
                row.add(json.getString("sh_xkz_distinct_count"));
                tableData.add(row);
            }
        }else{
            tableHeader = new ArrayList<>(){{
                add("许可证号");
                add("客户:名称");
                add("客户:法人");
                add("客户:档位");
                add("客户:城镇");
                add("客户:业态");
                add("客户:营销线");
            }};

            // 根据任务ID查询调研价格信息
            List<GatherStInventory> inventoryList = inventoryMapper.selectList(new GatherStInventory(){{
                setStId(Long.valueOf(taskId));
            }});
            if(!inventoryList.isEmpty()){
                // 拼接表头 卷烟品规名称
                List<Long> headerList2 = inventoryList.stream()
                                                      .map(GatherStInventory::getSpId)
                                                      .distinct()
                                                      .toList();
                for(Long id : headerList2){
                    GatherSmokePrice smokePrice = smokePriceService.selectById(id);
                    tableHeader.add(smokePrice.getName());
                }
                // 拼接表格内容
                // 计算表格每行的长度
                int rowSize = tableHeader.size();
                for(int i = 0; i < inventoryList.size(); i++){
                    GatherStInventory prize = inventoryList.get(i);
                    List<String> row;
                    if(i % (rowSize - 7) == 0){    // 第一个单元格
                        // new一个新List
                        row = new ArrayList<>();
                        // 查询商户信息
                        String shopCode = prize.getShXkz();
                        MerchantInfo merchantInfo = merchantInfoService.selectMerchantInfoByLicence(shopCode);
                        row.add(merchantInfo.getLicence().toString());
                        row.add(merchantInfo.getMerchantName());
                        row.add(merchantInfo.getLegalName());
                        row.add(merchantInfo.getDangwei());
                        row.add(merchantInfo.getMarketType());
                        row.add(merchantInfo.getYetai());
                        row.add(merchantInfo.getYingxiaoxian());
                        // operate 1:原始数据 2:清洗后数据 3:对比数据 4:清洗提示
                        switch(operate){
                            case "1" -> row.add(prize.getOldInventory() + "");
                            case "2" -> row.add(prize.getNewInventory() + getClearText(prize));
                            case "5" -> row.add(prize.getOldInventory() + getClearText(prize));
                        }
                        tableData.add(row);
                    }else{
                        row = tableData.get(tableData.size() - 1);
                        // operate 1:原始数据 2:清洗后数据 3:对比数据 4:清洗提示
                        switch(operate){
                            case "1" -> row.add(prize.getOldInventory() + "");
                            case "2" -> row.add(prize.getNewInventory() + getClearText(prize));
                            case "5" -> row.add(prize.getOldInventory() + getClearText(prize));
                        }
                    }
                }
            }

            if(operate.equals("5")){
                tableData = tableData.stream().filter(o -> {
                    String str =  StringUtils.join(",", o);
                    return str.contains("数据错误") || str.contains("规则清理");
                }).collect(Collectors.toList());
            }
        }
        HashMap<String, Object> res = new HashMap<>();
        res.put("tableHeader", tableHeader);
        res.put("tableData", tableData);
        return res;
    }

    public String getClearText(GatherStInventory prize){
        if(prize.getClear1().intValue() == 1){
            return "(数据错误)";
        } else if(prize.getClear2().intValue() == 1){
            return "(规则清理)";
        } else {
            return "";
        }
    }
}

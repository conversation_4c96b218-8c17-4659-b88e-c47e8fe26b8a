package com.hz.gather.service;

import java.util.HashMap;
import java.util.List;

import com.hz.gather.domain.GatherStPrice;

/**
 * 调研价格Service接口
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface IGatherStPriceService{
    /**
     * 查询调研价格
     *
     * @param id 调研价格主键
     * @return 调研价格
     */
    GatherStPrice selectById(Long id);

    /**
     * 查询调研价格列表
     *
     * @param price 调研价格
     * @return 调研价格集合
     */
    List<GatherStPrice> selectList(GatherStPrice price);

    /**
     * 新增调研价格
     *
     * @param price 调研价格
     * @return 结果
     */
    int insert(GatherStPrice price);

    /**
     * 批量插入调研价格
     *
     * @param priceList 调研价格列表
     * @return 插入成功的记录数
     */
    int insertBatch(List<GatherStPrice> priceList);

    /**
     * 修改调研价格
     *
     * @param price 调研价格
     * @return 结果
     */
    int update(GatherStPrice price);

    /**
     * 批量删除调研价格
     *
     * @param ids 需要删除的调研价格主键集合
     * @return 结果
     */
    int deleteByIds(Long[] ids);

    /**
     * 删除调研价格信息
     *
     * @param id 调研价格主键
     * @return 结果
     */
    int deleteById(Long id);

    /** 根据任务ID删除调研价格信息
     *
     * @param taskId 任务ID
     * @return 结果
     */
    int deleteByTaskId(long taskId);

    /**
     * 批量更新
     *
     * @param ids
     * @param clear2
     */
    void batchUpdateClear2(List<Long> ids, Integer clear2);

    /**
     * 获取调研任务的数据
     *
     * @param taskId  任务ID
     * @param operate 1:原始数据 2:清洗后数据 3:对比数据 4:清洗提示 5：清洗商户
     * @return 表格数据
     */
    HashMap<String, Object> getTableData(String taskId,String operate);
}

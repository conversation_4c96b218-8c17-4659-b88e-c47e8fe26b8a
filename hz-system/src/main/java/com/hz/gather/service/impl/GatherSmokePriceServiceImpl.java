package com.hz.gather.service.impl;

import com.alibaba.fastjson2.JSON;
import com.hz.common.exception.ServiceException;
import com.hz.common.utils.DateUtils;
import com.hz.common.utils.SecurityUtils;
import com.hz.common.utils.StringUtils;
import com.hz.gather.constant.GatherRedisConstant;
import com.hz.gather.domain.GatherSmokePrice;
import com.hz.gather.mapper.GatherSmokePriceMapper;
import com.hz.gather.service.IGatherSmokePriceService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 卷烟价格Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class GatherSmokePriceServiceImpl implements IGatherSmokePriceService{

    private final Logger logger = LoggerFactory.getLogger(GatherSmokePriceServiceImpl.class);
    @Resource
    private GatherSmokePriceMapper smokePriceMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 查询卷烟价格
     *
     * @param id 卷烟价格主键
     * @return 卷烟价格
     */
    @Override
    public GatherSmokePrice selectById(Long id){
        String key = GatherRedisConstant.SMOKE_PRICE_INFO_byId + id;
        if(stringRedisTemplate.hasKey(key)){
            String smokePriceJson = stringRedisTemplate.opsForValue().get(key);
            return JSON.parseObject(smokePriceJson, GatherSmokePrice.class);
        }else{
            GatherSmokePrice smokePrice = smokePriceMapper.selectById(id);
            stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(smokePrice), 20, TimeUnit.MINUTES);
            return smokePrice;
        }
    }

    /**
     * 根据名称查询卷烟价格
     *
     * @param smokeName 卷烟名称
     * @return GatherSmokePrice
     */
    @Override
    public GatherSmokePrice selectByName(String smokeName){
        String key = GatherRedisConstant.SMOKE_PRICE_INFO_byName + smokeName;
        if(stringRedisTemplate.hasKey(key)){
            String smokePriceJson = stringRedisTemplate.opsForValue().get(key);
            return JSON.parseObject(smokePriceJson, GatherSmokePrice.class);
        }else{
            List<GatherSmokePrice> list = smokePriceMapper.selectList(new GatherSmokePrice(){{
                setName(smokeName);
            }});
            GatherSmokePrice smokePrice;
            if(!list.isEmpty()){
                smokePrice = list.get(0);
                stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(smokePrice), 20, TimeUnit.MINUTES);
            }else{
                smokePrice = null;
            }
            return smokePrice;
        }
    }

    /**
     * 查询卷烟价格列表
     *
     * @param smokePrice 卷烟价格
     * @return 卷烟价格
     */
    @Override
    public List<GatherSmokePrice> selectList(GatherSmokePrice smokePrice){
        return smokePriceMapper.selectList(smokePrice);
    }

    /**
     * 该次任务下的卷烟品规(价格、品规)
     *
     * @param smokePrice 卷烟价格
     * @return 卷烟价格集合
     */
    @Override
    public List<GatherSmokePrice> gaicSmokePrice(GatherSmokePrice smokePrice){
        return smokePriceMapper.gaicSmokePrice(smokePrice);
    }

    /**
     * 累计任务下的卷烟品规(价格、品规)
     *
     * @param classify
     * @param stIds
     * @return 卷烟价格集合
     */
    @Override
    public List<GatherSmokePrice> leijSmokePrice(String classify, String stIds){
        return smokePriceMapper.leijSmokePrice(classify, stIds);
    }

    /**
     * 新增卷烟价格
     *
     * @param smokePrice 卷烟价格
     * @return 结果
     */
    @Override
    public int insert(GatherSmokePrice smokePrice){
        check(smokePrice);
        smokePrice.setCreateId(SecurityUtils.getUserId());
        smokePrice.setCreateBy(SecurityUtils.getUsername());
        smokePrice.setCreateTime(DateUtils.getNowDate());
        int i = smokePriceMapper.insert(smokePrice);
        stringRedisTemplate.delete(GatherRedisConstant.SMOKE_PRICE_INFO_byId + smokePrice.getId());
        stringRedisTemplate.delete(GatherRedisConstant.SMOKE_PRICE_INFO_byName + smokePrice.getName());
        return i;
    }

    /**
     * 修改卷烟价格
     *
     * @param smokePrice 卷烟价格
     * @return 结果
     */
    @Override
    public int update(GatherSmokePrice smokePrice){
        check(smokePrice);
        smokePrice.setUpdateId(SecurityUtils.getUserId());
        smokePrice.setUpdateBy(SecurityUtils.getUsername());
        smokePrice.setUpdateTime(DateUtils.getNowDate());
        int i = smokePriceMapper.update(smokePrice);
        stringRedisTemplate.delete(GatherRedisConstant.SMOKE_PRICE_INFO_byId + smokePrice.getId());
        stringRedisTemplate.delete(GatherRedisConstant.SMOKE_PRICE_INFO_byName + smokePrice.getName());
        return i;
    }

    /**
     * 批量删除卷烟价格
     *
     * @param ids 需要删除的卷烟价格主键
     * @return 结果
     */
    @Override
    public int deleteByIds(Long[] ids){
        for(Long id : ids){
            stringRedisTemplate.delete(GatherRedisConstant.SMOKE_PRICE_INFO_byId + id);
        }
        return smokePriceMapper.deleteByIds(ids);
    }

    /**
     * 删除卷烟价格信息
     *
     * @param id 卷烟价格主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id){
        stringRedisTemplate.delete(GatherRedisConstant.SMOKE_PRICE_INFO_byId + id);
        return smokePriceMapper.deleteById(id);
    }

    /**
     * 导入香烟品规价格
     *
     * @param list
     */
    @Override
    public Map<String, String> importSmokePrice(List<GatherSmokePrice> list){
        if(CollectionUtils.isEmpty(list)){
            throw new ServiceException("文件无数据，请填充数据再导入");
        }

        // 已有列表
        List<GatherSmokePrice> sps = smokePriceMapper.selectList(new GatherSmokePrice());

        // 校验
        int row = 2;
        StringBuilder failureMsg = new StringBuilder();
        List<GatherSmokePrice> sss = new ArrayList<>();
        for (GatherSmokePrice excel : list) {
            // 校验：空值
            if(StringUtils.isEmpty(excel.getName())){
                failureMsg.append("<br/>第" + row + "行品规名称为空，请补充");
            }
            // 校验：空值
            if(excel.getPfPrice() == null){
                failureMsg.append("<br/>第" + row + "行批发价为空，请补充");
            }
            // 校验：空值
            if(excel.getLsPrice() == null){
                failureMsg.append("<br/>第" + row + "行零售价为空，请补充");
            }

            // 封装
            GatherSmokePrice first = sps.stream().filter(o -> o.getName().equals(excel.getName())).findFirst().orElse(null);
            GatherSmokePrice entity = new GatherSmokePrice();
            if(first != null){
                entity = first;
                entity.setName(excel.getName());
                entity.setCode(excel.getCode());
                entity.setLsPrice(excel.getLsPrice());
                entity.setPfPrice(excel.getPfPrice());
                entity.setCategory(excel.getCategory());
                entity.setUpdateId(SecurityUtils.getUserId());
                entity.setUpdateBy(SecurityUtils.getUsername());
                entity.setUpdateTime(DateUtils.getNowDate());
            } else {
                entity.setName(excel.getName());
                entity.setCode(excel.getCode());
                entity.setLsPrice(excel.getLsPrice());
                entity.setPfPrice(excel.getPfPrice());
                entity.setCategory(excel.getCategory());
                entity.setCreateId(SecurityUtils.getUserId());
                entity.setCreateBy(SecurityUtils.getUsername());
                entity.setCreateTime(DateUtils.getNowDate());
            }
            sss.add(entity);
        }

        // 校验未通过
        if(!failureMsg.isEmpty()){
            failureMsg.insert(0, "导入失败！部分数据格式不正确，错误如下：");
            Map<String, String> res = new HashMap<>();
            res.put("error", failureMsg.toString());
            return res;
        }

        // 插入、更新集合
        List<GatherSmokePrice> insertList = sss.stream().filter(o -> o.getId() == null).toList();
        List<GatherSmokePrice> updateList = sss.stream().filter(o -> o.getId() != null).toList();

        // 入库
        for (GatherSmokePrice entity : insertList) {
            smokePriceMapper.insert(entity);
        }
        for (GatherSmokePrice entity : updateList) {
            smokePriceMapper.update(entity);
        }
        return null;
    }

    /**
     * 校验
     * @param smokePrice
     */
    private void check(GatherSmokePrice smokePrice){
        List<GatherSmokePrice> sas = smokePriceMapper.selectList(new GatherSmokePrice());
        if(CollectionUtils.isNotEmpty(sas)){
            Long id = smokePrice.getId() != null ? smokePrice.getId() : -1L;
            List<GatherSmokePrice> filter1 = sas.stream().filter(o -> o.getName().equals(smokePrice.getName()) && !o.getId().equals(id)).toList();
            if(CollectionUtils.isNotEmpty(filter1)){
                throw new ServiceException("名称已存在，请修改名称");
            }
        }
    }
}

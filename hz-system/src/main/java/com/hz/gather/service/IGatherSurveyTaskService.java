package com.hz.gather.service;

import com.hz.gather.domain.GatherSurveyTask;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;

/**
 * 调研任务Service接口
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface IGatherSurveyTaskService{
    /**
     * 查询调研任务
     *
     * @param id 调研任务主键
     * @return 调研任务
     */
    GatherSurveyTask selectById(Long id);

    /**
     * 查询调研任务列表
     *
     * @param gatherSurveyTask 调研任务
     * @return 调研任务集合
     */
    List<GatherSurveyTask> selectList(GatherSurveyTask gatherSurveyTask);

    /**
     * 新增调研任务
     *
     * @param gatherSurveyTask 调研任务
     * @return 结果
     */
    int insert(GatherSurveyTask gatherSurveyTask);

    /**
     * 修改调研任务
     *
     * @param gatherSurveyTask 调研任务
     * @return 结果
     */
    int update(GatherSurveyTask gatherSurveyTask);

    /**
     * 批量删除调研任务
     *
     * @param ids 需要删除的调研任务主键集合
     * @return 结果
     */
    int deleteByIds(Long[] ids);

    /**
     * 删除调研任务信息
     *
     * @param id 调研任务主键
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * <h1>导入价格调研数据</h1>
     * @param file   文件
     * @param tickId 任务ID
     * @return 插入条数
     */
    int importPriceData(MultipartFile file, long tickId) throws IOException;

    /**
     * <h1>导入库存调研数据</h1>
     * @param file   文件
     * @param tickId 任务ID
     * @return 插入条数
     */
    int importInventoryData(MultipartFile file, long tickId) throws IOException;

    /**
     * <h1>导入价格调研数据</h1>
     * @param file   文件
     * @param tickId 任务ID
     * @return 插入条数
     */
    int importLinkData(MultipartFile file, long tickId) throws IOException;

    /**
     * <h1>获取调研数据报表</h1>
     * @param taskId 任务ID
     * @param type 数据类型 1:价格 2:库存 3:四员联动
     * @param operate 1:原始数据 2:清洗后数据 3:对比数据 4:清洗提示 5：清洗商户
     * @return 调研数据报表
     */
    HashMap<String, Object> getStatisticTableData(String taskId, String type, String operate);

    HashMap<String, Object> getStatisticTableData(Long id, int i, int i1);

    /**
     * 按规则清理价格
     *
     * @param taskId        调研任务ID
     * @param type          数据类型 1:价格 2:库存
     * @param cleanRulesIds 规则id集合
     * @param spNames       规则id集合
     * @return
     */
    public int cleanGatherStPrice(Long taskId, String type, List<String> cleanRulesIds, List<String> spNames);

    /**
     * 按规则清理库存
     *
     * @param taskId        调研任务ID
     * @param type          数据类型 1:价格 2:库存
     * @param cleanRulesIds 规则id集合
     * @param spNames       规则id集合
     * @return
     */
    public int cleanGatherStInventory(Long taskId, String type, List<String> cleanRulesIds, List<String> spNames);
}

package com.hz.gather.service;

import com.hz.gather.domain.GatherSmokePrice;

import java.util.List;
import java.util.Map;

/**
 * 卷烟价格Service接口
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface IGatherSmokePriceService{

    /**
     * 查询卷烟价格
     *
     * @param id 卷烟价格主键
     * @return 卷烟价格
     */
    GatherSmokePrice selectById(Long id);

    /**
     * 根据名称查询卷烟价格
     *
     * @param smokeName 卷烟名称
     * @return GatherSmokePrice
     */
    GatherSmokePrice selectByName(String smokeName);

    /**
     * 查询卷烟价格列表
     *
     * @param smokePrice 卷烟价格
     * @return 卷烟价格集合
     */
    List<GatherSmokePrice> selectList(GatherSmokePrice smokePrice);

    /**
     * 该次任务下的卷烟品规(价格、品规)
     *
     * @param smokePrice 卷烟价格
     * @return 卷烟价格集合
     */
    List<GatherSmokePrice> gaicSmokePrice(GatherSmokePrice smokePrice);

    /**
     * 累计任务下的卷烟品规(价格、品规)
     *
     * @param classify
     * @param stIds
     * @return 卷烟价格集合
     */
    List<GatherSmokePrice> leijSmokePrice(String classify, String stIds);

    /**
     * 新增卷烟价格
     *
     * @param smokePrice 卷烟价格
     * @return 结果
     */
    int insert(GatherSmokePrice smokePrice);

    /**
     * 修改卷烟价格
     *
     * @param smokePrice 卷烟价格
     * @return 结果
     */
    int update(GatherSmokePrice smokePrice);

    /**
     * 批量删除卷烟价格
     *
     * @param ids 需要删除的卷烟价格主键集合
     * @return 结果
     */
    int deleteByIds(Long[] ids);

    /**
     * 删除卷烟价格信息
     *
     * @param id 卷烟价格主键
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 导入香烟品规价格
     */
    Map<String, String> importSmokePrice(List<GatherSmokePrice> list);

}

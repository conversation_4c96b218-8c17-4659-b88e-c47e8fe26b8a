package com.hz.gather.service.impl;

import cn.hutool.core.date.DateUtil;
import com.hz.gather.domain.GatherStLink;
import com.hz.gather.mapper.GatherStLinkMapper;
import com.hz.gather.service.IGatherStLinkService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 调研四员联动Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class GatherStLinkServiceImpl implements IGatherStLinkService{
    @Resource
    private GatherStLinkMapper linkMapper;

    /**
     * 查询调研四员联动
     *
     * @param id 调研四员联动主键
     * @return 调研四员联动
     */
    @Override
    public GatherStLink selectById(Long id){
        return linkMapper.selectById(id);
    }

    /**
     * 查询调研四员联动列表
     *
     * @param link 调研四员联动
     * @return 调研四员联动
     */
    @Override
    public List<GatherStLink> selectList(GatherStLink link){
        return linkMapper.selectList(link);
    }

    /**
     * 新增调研四员联动
     *
     * @param link 调研四员联动
     * @return 结果
     */
    @Override
    public int insert(GatherStLink link){
        return linkMapper.insert(link);
    }

    /**
     * 批量插入
     *
     * @param linkList
     */
    @Override
    public int insertBatch(List<GatherStLink> linkList){
        int batchSize = 100;
        int totalInserted = 0;
        for(int i = 0; i < linkList.size(); i += batchSize){
            int endIndex = Math.min(i + batchSize, linkList.size());
            List<GatherStLink> batch = linkList.subList(i, endIndex);

            try{
                totalInserted += linkMapper.insertBatch(batch);
            }catch(Exception e){
                e.printStackTrace();
            }
        }
        return totalInserted;
    }

    /**
     * 修改调研四员联动
     *
     * @param link 调研四员联动
     * @return 结果
     */
    @Override
    public int update(GatherStLink link){
        return linkMapper.update(link);
    }

    /**
     * 批量删除调研四员联动
     *
     * @param ids 需要删除的调研四员联动主键
     * @return 结果
     */
    @Override
    public int deleteByIds(Long[] ids){
        return linkMapper.deleteByIds(ids);
    }

    /**
     * 删除调研四员联动信息
     *
     * @param id 调研四员联动主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id){
        return linkMapper.deleteById(id);
    }

    /**
     * 根据任务ID删除调研库存信息
     *
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    public int deleteByTaskId(long taskId){
        return linkMapper.deleteByTaskId(taskId);
    }

    /**
     * 获取调研任务的数据
     *
     * @param taskId  任务ID
     * @param operate 1:原始数据 2:清洗后数据 3:对比数据 4:清洗提示
     * @return 表格数据
     */
    @Override
    public HashMap<String, Object> getTableData(String taskId, String operate){
        List<String> tableHeader = new ArrayList<>(){{
            add("提交时间");
            add("提交人");
            add("内容详情");
        }};
        List<List<String>> tableData = new ArrayList<>();
        List<GatherStLink> linkList = linkMapper.selectList(new GatherStLink(){{
            setStId(Long.valueOf(taskId));
        }});
        if(linkList != null && linkList.size() > 0){
            for(int i = 0; i < linkList.size(); i++){
                GatherStLink link = linkList.get(i);
                List<String> row = new ArrayList<>();
                row.add(DateUtil.format(link.getTjsj(), "yyyy-MM-dd HH:mm:ss"));
                row.add(link.getTjr());
                row.add(link.getContent());
                tableData.add(row);
            }
        }
        HashMap<String, Object> res = new HashMap<>();
        res.put("tableHeader", tableHeader);
        res.put("tableData", tableData);
        return res;
    }
}

package com.hz.gather.service.impl;

import com.hz.common.utils.DateUtils;
import com.hz.common.utils.SecurityUtils;
import com.hz.gather.domain.GatherCleanRules;
import com.hz.gather.mapper.GatherCleanRulesMapper;
import com.hz.gather.service.IGatherCleanRulesService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 香烟数据清理规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
public class GatherCleanRulesServiceImpl implements IGatherCleanRulesService{

    @Resource
    private GatherCleanRulesMapper rulesMapper;

    /**
     * 查询香烟数据清理规则
     *
     * @param id 香烟数据清理规则主键
     * @return 香烟数据清理规则
     */
    @Override
    public GatherCleanRules selectById(Long id){
        return rulesMapper.selectById(id);
    }

    /**
     * 查询香烟数据清理规则列表
     *
     * @param rules 香烟数据清理规则
     * @return 香烟数据清理规则
     */
    @Override
    public List<GatherCleanRules> selectList(GatherCleanRules rules){
        return rulesMapper.selectList(rules);
    }

    /**
     * 新增香烟数据清理规则
     *
     * @param rules 香烟数据清理规则
     * @return 结果
     */
    @Override
    public int insert(GatherCleanRules rules){
        rules.setCreateId(SecurityUtils.getUserId());
        rules.setCreateBy(SecurityUtils.getUsername());
        rules.setCreateTime(DateUtils.getNowDate());
        return rulesMapper.insert(rules);
    }

    /**
     * 修改香烟数据清理规则
     *
     * @param rules 香烟数据清理规则
     * @return 结果
     */
    @Override
    public int update(GatherCleanRules rules){
        rules.setUpdateId(SecurityUtils.getUserId());
        rules.setUpdateBy(SecurityUtils.getUsername());
        rules.setUpdateTime(DateUtils.getNowDate());
        return rulesMapper.update(rules);
    }

    /**
     * 批量删除香烟数据清理规则
     *
     * @param ids 需要删除的香烟数据清理规则主键
     * @return 结果
     */
    @Override
    public int deleteByIds(Long[] ids){
        return rulesMapper.deleteByIds(ids);
    }

    /**
     * 删除香烟数据清理规则信息
     *
     * @param id 香烟数据清理规则主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id){
        return rulesMapper.deleteById(id);
    }
}

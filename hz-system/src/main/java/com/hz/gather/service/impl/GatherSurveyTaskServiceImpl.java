package com.hz.gather.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.hz.common.utils.*;
import com.hz.gather.constant.GatherSpecialShopEnum;
import com.hz.gather.domain.*;
import com.hz.gather.mapper.GatherSurveyTaskMapper;
import com.hz.gather.service.*;
import com.hz.merchant.domain.MerchantInfo;
import com.hz.merchant.service.IMerchantInfoService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 调研任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class GatherSurveyTaskServiceImpl implements IGatherSurveyTaskService{

    private final Logger logger = LoggerFactory.getLogger(GatherSurveyTaskServiceImpl.class);

    @Resource
    private GatherSurveyTaskMapper taskMapper;
    @Resource
    private IGatherStPriceService stPriceService;
    @Resource
    private IGatherStInventoryService stInventoryService;
    @Resource
    private IGatherStLinkService stLinkService;
    @Resource
    private IMerchantInfoService merchantInfoService;
    @Resource
    private IGatherSmokePriceService smokePriceService;
    @Resource
    private IGatherCleanRulesService cleanRulesService;

    /**
     * 查询调研任务
     *
     * @param id 调研任务主键
     * @return 调研任务
     */
    @Override
    public GatherSurveyTask selectById(Long id){
        return taskMapper.selectById(id);
    }

    /**
     * 查询调研任务列表
     *
     * @param task 调研任务
     * @return 调研任务
     */
    @Override
    public List<GatherSurveyTask> selectList(GatherSurveyTask task){
        return taskMapper.selectList(task);
    }

    /**
     * 新增调研任务
     *
     * @param task 调研任务
     * @return 结果
     */
    @Override
    public int insert(GatherSurveyTask task){
        task.setCreateId(SecurityUtils.getUserId());
        task.setCreateBy(SecurityUtils.getUsername());
        task.setCreateTime(DateUtils.getNowDate());
        return taskMapper.insert(task);
    }

    /**
     * 修改调研任务
     *
     * @param task 调研任务
     * @return 结果
     */
    @Override
    public int update(GatherSurveyTask task){
        task.setUpdateId(SecurityUtils.getUserId());
        task.setUpdateBy(SecurityUtils.getUsername());
        task.setUpdateTime(DateUtils.getNowDate());
        return taskMapper.update(task);
    }

    /**
     * 批量删除调研任务
     *
     * @param ids 需要删除的调研任务主键
     * @return 结果
     */
    @Override
    public int deleteByIds(Long[] ids){
        for(Long taskId : ids){
            stPriceService.deleteByTaskId(taskId);
            stInventoryService.deleteByTaskId(taskId);
            stLinkService.deleteByTaskId(taskId);
        }
        return taskMapper.deleteByIds(ids);
    }

    /**
     * 删除调研任务信息
     *
     * @param id 调研任务主键
     * @return 结果
     */
    @Override
    public int deleteById(Long id){
        return taskMapper.deleteById(id);
    }

    /**
     * <h1>导入 价格 调研数据</h1>
     *
     * @param file   文件
     * @param taskId 任务ID
     * @return
     */
    @Override
    public int importPriceData(MultipartFile file, long taskId) throws IOException{
        // 清理原有数据
        stPriceService.deleteByTaskId(taskId);

        // 香烟品规
        List<GatherSmokePrice> sps = smokePriceService.selectList(new GatherSmokePrice());
        Map<String, Long> spMap = sps.stream().collect(Collectors.toMap(GatherSmokePrice::getName, GatherSmokePrice::getId));

        int tjsjIndex = -1;       // 提交时间
        int shopCardIndex = -1;   // 商家许可证号
        int shopBossIndex = -1;   // 商家法人
        int shopLineIndex = -1;   // 所属线路
        int smokePriceIndex = -1; // 香烟价格开始
        List<List<Object>> excelData = cn.hutool.poi.excel.ExcelUtil.getReader(file.getInputStream()).read();

        // 遍历表头，找到商家许可证号、商家法人、所属线路的列索引
        List<Object> headerRow = excelData.get(0);
        for(int i = 0; i < headerRow.size(); i++){
            Object cellValue = headerRow.get(i);
            if(cellValue != null && "提交时间".equals(cellValue.toString())){
                tjsjIndex = i;
            }else if(cellValue != null && "许可证号".equals(cellValue.toString())){
                shopCardIndex = i;
            }else if(cellValue != null && "许可证号:法人".equals(cellValue.toString())){
                shopBossIndex = i;
            }else if(cellValue != null && "许可证号:营销线".equals(cellValue.toString())){
                shopLineIndex = i;
            }
        }
        smokePriceIndex = Math.max(shopCardIndex, Math.max(shopBossIndex, shopLineIndex)) + 1;

        // 遍历表头，对香烟品规进行校验操作
        for(int i = smokePriceIndex; i < headerRow.size(); i++){
            Object cellValue = headerRow.get(i);
            String smokeName = cellValue.toString(); // 香烟名称
            if(StringUtils.isNotBlank(smokeName)){
                Long spId = spMap.get(smokeName);
                if(spId == null){
                    // 若香烟品规不存在，抛出运行时异常并给出提示
                    throw new RuntimeException("香烟品规 " + smokeName + " 不存在");
                }
            }
        }

        // 遍历数据行，获取商家许可证号、商家法人、所属线路、香烟价格等信息
        List<GatherStPrice> priceList = new ArrayList<>();
        // 判断调拨价, 将六个地区的调拨价保存到活动表中
        JSONObject dbjJson = new JSONObject();
        for(int i = 1; i < excelData.size(); i++){
            List<Object> row = excelData.get(i);

            String shopLicence = row.get(shopCardIndex).toString();   // 许可证号

            // 判断调拨价, 将六个地区的调拨价保存到活动表中
            if(GatherSpecialShopEnum.hasLicence(shopLicence)){
                String shopName = GatherSpecialShopEnum.gatNameByLicence(shopLicence);

                JSONObject dbjItem = new JSONObject();
                List<Object> prics = row.subList(smokePriceIndex, headerRow.size());
                dbjItem.put("shopName", shopName);
                dbjItem.put("price", prics);

                for(int j = 0; j < prics.size(); j++){
                    String smokeName = headerRow.get(j + smokePriceIndex).toString();
                    Long spId = spMap.get(smokeName);
                    if(spId != null){
                        dbjItem.put(String.valueOf(spId), prics.get(j));
                    }
                }

                dbjJson.put(shopLicence, dbjItem);
                continue;
            }

            for(int j = smokePriceIndex; j < headerRow.size(); j++){   // 根据表头进行循环, 防止中间有值为空的情况
                Object cellValue = row.get(j);
                String smokeName = headerRow.get(j).toString();

                // 商铺信息
                MerchantInfo shop = merchantInfoService.selectMerchantInfoByLicence(shopLicence);

                // 没有值、非数字、小于等于0 都是clear1 = 1
                int clear1 = 0;
                String cell = "";
                if(row.get(j) != null){
                    cell = cellValue.toString();
                    if(!StringUtils.isDouble(cell)){
                        throw new RuntimeException("香烟品规【" + smokeName + "】下存在非数字数据，请更正");
                    } else {
                        Double d = Double.parseDouble(cell);
                        if(d.compareTo(0D) <= 0){
                            clear1 = 1;
                            cell = "0.0";
                        }
                    }
                } else {
                    clear1 = 1;
                    cell = "0.0";
                }

                // 封装
                GatherStPrice stPrice = new GatherStPrice();
                stPrice.setStId(taskId);                                // 任务ID
                stPrice.setShXkz(shop.getLicence().toString());         // 商户许可证号
                stPrice.setShDw(shop.getDangwei());                     // 商户档位
                stPrice.setShCz(shop.getMarketType());                  // 商户城镇
                stPrice.setShYt(shop.getYetai());                       // 商户业态
                stPrice.setShYxx(shop.getYingxiaoxian());               // 商户营销线
                stPrice.setSpId(spMap.get(smokeName));                    // 香烟品规ID
                stPrice.setTjsj(DateUtils.parseDate(row.get(tjsjIndex).toString()));
                stPrice.setOldPrice(Double.parseDouble(cell));
                stPrice.setNewPrice(Double.parseDouble(cell));
                stPrice.setClear1(clear1);
                stPrice.setClear2(0);
                priceList.add(stPrice);
            }
        }

        // 调货价校验
        if(CollectionUtil.isNotEmpty(priceList)){
            Set<String> sss = priceList.stream().map(GatherStPrice::getShXkz).collect(Collectors.toSet());
            if(sss.contains("111111")){
                throw new RuntimeException("调货价不存在，请补充");
            }
        }

        if(!dbjJson.isEmpty()){
            this.update(new GatherSurveyTask(){{
                setId(taskId);
                setRemark(dbjJson.toJSONString());
            }});
        }
        return stPriceService.insertBatch(priceList);
    }

    /**
     * <h1>导入 库存 调研数据</h1>
     *
     * @param file   文件
     * @param taskId 任务ID
     * @return
     */
    @Override
    public int importInventoryData(MultipartFile file, long taskId) throws IOException{
        // 清理原有数据
        stInventoryService.deleteByTaskId(taskId);

        int tjsjIndex = -1;           // 提交时间
        int shopCardIndex = -1;       // 商家许可证号
        int shopBossIndex = -1;       // 商家法人
        int shopLineIndex = -1;       // 所属线路
        int smokeInventoryIndex = -1; // 香烟价格开始
        List<List<Object>> excelData = cn.hutool.poi.excel.ExcelUtil.getReader(file.getInputStream()).read();
        // 遍历表头，找到商家许可证号、商家法人、所属线路的列索引
        List<Object> headerRow = excelData.get(0);
        for(int i = 0; i < headerRow.size(); i++){
            Object cellValue = headerRow.get(i);
            if(cellValue != null && "提交时间".equals(cellValue.toString())){
                tjsjIndex = i;
            }else if(cellValue != null && "许可证号".equals(cellValue.toString())){
                shopCardIndex = i;
            }else if(cellValue != null && cellValue.toString().contains("法人")){
                shopBossIndex = i;
            }else if(cellValue != null && cellValue.toString().contains("营销线")){
                shopLineIndex = i;
            }
        }
        smokeInventoryIndex = Math.max(shopCardIndex, Math.max(shopBossIndex, shopLineIndex)) + 1;

        for(int i = smokeInventoryIndex; i < headerRow.size(); i++){
            Object cellValue = headerRow.get(i);
            String smokeName = cellValue.toString(); // 香烟名称
            if(StringUtils.isNotBlank(smokeName)){
                GatherSmokePrice smokePrice = smokePriceService.selectByName(smokeName);
                if(smokePrice == null){
                    // 若香烟品规不存在，抛出运行时异常并给出提示
                    throw new RuntimeException("香烟品规 " + smokeName + " 不存在");
                }
            }
        }
        // 遍历数据行，获取商家许可证号、商家法人、所属线路、香烟价格等信息
        List<GatherStInventory> inventoryList = new ArrayList<>();
        for(int i = 1; i < excelData.size(); i++){
            List<Object> row = excelData.get(i);
            for(int j = smokeInventoryIndex; j < headerRow.size(); j++){   // 根据表头进行循环, 防止中间有值为空的情况
                Object cellValue = row.get(j);
                String smokeName = headerRow.get(j).toString();

                // 商铺信息
                String shopLicence = row.get(shopCardIndex).toString();   // 许可证号
                MerchantInfo shop = merchantInfoService.selectMerchantInfoByLicence(shopLicence);

                // 香烟品规信息
                GatherSmokePrice smokePrice = smokePriceService.selectByName(smokeName);

                // 没有值、非数字、小于0 都是clear1 = 1
                int clear1 = 0;
                String cell = "";
                if(row.get(j) != null){
                    cell = cellValue.toString();
                    if(!StringUtils.isDouble(cell)){
                        throw new RuntimeException("香烟品规【" + smokeName + "】下存在非数字数据，请更正");
                    } else {
                        Double d = Double.parseDouble(cell);
                        if(d.compareTo(0D) < 0){
                            clear1 = 1;
                            cell = "0.0";
                        }
                    }
                } else {
                    clear1 = 1;
                    cell = "0.0";
                }

                GatherStInventory stInventory = new GatherStInventory();
                stInventory.setStId(taskId);                                // 任务ID
                stInventory.setShXkz(shop.getLicence().toString());         // 商户许可证号
                stInventory.setShDw(shop.getDangwei());                     // 商户档位
                stInventory.setShCz(shop.getMarketType());                  // 商户城镇
                stInventory.setShYt(shop.getYetai());                       // 商户业态
                stInventory.setShYxx(shop.getYingxiaoxian());               // 商户营销线
                stInventory.setSpId(smokePrice.getId());                    // 香烟品规ID
                stInventory.setTjsj(DateUtils.parseDate(row.get(tjsjIndex).toString()));
                stInventory.setOldInventory(Double.parseDouble(cell));
                stInventory.setNewInventory(Double.parseDouble(cell));
                stInventory.setClear1(clear1);
                stInventory.setClear2(0);
                inventoryList.add(stInventory);
            }
        }

        return stInventoryService.insertBatch(inventoryList);
    }

    /**
     * <h1>导入 四员联动 调研数据</h1>
     *
     * @param file   文件
     * @param taskId 任务ID
     * @return
     */
    @Override
    public int importLinkData(MultipartFile file, long taskId) throws IOException{
        // 清理原有数据
        stLinkService.deleteByTaskId(taskId);
        // 读取Excel文件内容
        List<List<Object>> excelData = cn.hutool.poi.excel.ExcelUtil.getReader(file.getInputStream()).read();

        int shopCodeIndex = -1;        // 商户许可证号
        int commitUserNameIndex = -1;  // 提交人
        int contentIndex = -1;         // 内容详情
        int commitTimeIndex = -1;      // 提交时间
        List<Object> headerRow = excelData.get(0);
        for(int i = 0; i < headerRow.size(); i++){
            Object cellValue = headerRow.get(i);
            if(cellValue != null && cellValue.toString().contains("许可证号")){
                shopCodeIndex = i;
            }else if(cellValue != null && cellValue.toString().contains("提交人")){
                commitUserNameIndex = i;
            }else if(cellValue != null && cellValue.toString().contains("内容")){
                contentIndex = i;
            }else if(cellValue != null && cellValue.toString().contains("提交时间")){
                commitTimeIndex = i;
            }
        }
        // 遍历数据行，获取商家许可证号、提交人、内容详情、提交时间等信息
        List<GatherStLink> linkList = new ArrayList<>();
        for(int i = 1; i < excelData.size(); i++){
            List<Object> row = excelData.get(i);

            GatherStLink link = new GatherStLink();
            link.setStId(taskId);
            // link.setShXkz(row.get(shopCodeIndex).toString());
            link.setTjr(row.get(commitUserNameIndex).toString());
            link.setContent(row.get(contentIndex).toString());
            link.setTjsj(DateUtils.parseDate(row.get(commitTimeIndex).toString()));
            linkList.add(link);
        }
        return stLinkService.insertBatch(linkList);
    }

    /**
     * <h1>获取调研数据报表</h1>
     *
     * @param taskId  任务ID
     * @param type    数据类型 1:价格 2:库存 3:四员联动
     * @param operate 1:原始数据 2:清洗后数据 3:对比数据 4:清洗提示 5：清洗商户
     * @return 调研数据报表
     */
    @Override
    public HashMap<String, Object> getStatisticTableData(String taskId, String type, String operate){
        switch(type){
            case "1" -> {
                return stPriceService.getTableData(taskId, operate);
            }
            case "2" -> {
                return stInventoryService.getTableData(taskId, operate);
            }
            case "3" -> {
                return stLinkService.getTableData(taskId, operate);
            }
        }
        return null;
    }

    @Override
    public HashMap<String, Object> getStatisticTableData(Long taskId, int type, int operate){
        return this.getStatisticTableData(String.valueOf(taskId), String.valueOf(type), String.valueOf(operate));
    }

    /**
     * 按规则清理价格
     *
     * @param taskId        调研任务ID
     * @param type          数据类型 1:价格 2:库存
     * @param cleanRulesIds 规则id集合
     * @param spNames       规则id集合
     * @return
     */
    @Transactional
    @Override
    public int cleanGatherStPrice(Long taskId, String type, List<String> cleanRulesIds, List<String> spNames){
        int resNum = 0;
        // 1.@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ 查询数据 @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
        List<GatherStPrice> priceList = stPriceService.selectList(new GatherStPrice(){{
            setStId(taskId);
        }});

        // 2.@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ 数据清理 @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
        for(int i = 0; i < cleanRulesIds.size(); i++){
            String ruleId = cleanRulesIds.get(i);
            if(ruleId != null && Long.parseLong(ruleId) <= 0)
                continue;
            // 查询规则
            GatherCleanRules cleanRules = cleanRulesService.selectById(Long.valueOf(ruleId));
            // 查询香烟价格
            String spName = spNames.get(i);
            GatherSmokePrice smokePrice = smokePriceService.selectByName(spName);

            // 某品规下的数据，且第一次未被清洗
            List<GatherStPrice> filter = priceList.stream().filter(o -> o.getSpId().equals(smokePrice.getId()) && o.getClear1().compareTo(0) == 0).toList();

            // 清洗数据
            List<GatherStPrice> updateList = new ArrayList<>();
            if(cleanRules.getType().equals("1")){
                // 计算上下限
                double floorNum = (Double.parseDouble(cleanRules.getFloorPfPriceEnable()) * smokePrice.getPfPrice() + Double.parseDouble(cleanRules.getFloorLsPriceEnable()) * smokePrice.getLsPrice() ) * cleanRules.getFloorRatio() / 100  + cleanRules.getFloorNum();
                double ceilNum = (Double.parseDouble(cleanRules.getCeilPfPriceEnable()) * smokePrice.getPfPrice() + Double.parseDouble(cleanRules.getCeilLsPriceEnable()) * smokePrice.getLsPrice() ) * cleanRules.getCeilRatio() / 100 + cleanRules.getCeilNum();
                logger.error(floorNum + " ~ " + ceilNum);
                // 更新
                for(GatherStPrice price : filter){
                    if((price.getNewPrice() < floorNum || price.getNewPrice() > ceilNum)){
                        price.setClear2(1);
                        updateList.add(price);
                    }
                }
            } else if(cleanRules.getType().equals("3")) {
                // 计算IQR区间
                Double[] doubles = filter.stream().map(GatherStPrice::getNewPrice).toArray(Double[]::new);
                IQRResult res = IQRCalculator.calculateIQR(doubles, cleanRules.getFloorRatio(), cleanRules.getCeilRatio());
                List<GatherStPrice> updated = filter.stream().filter(o -> o.getNewPrice().compareTo(res.lowerBound) < 0 || o.getNewPrice().compareTo(res.upperBound) > 0).toList();
                for (GatherStPrice price : updated) {
                    price.setClear2(1);
                    updateList.add(price);
                }
            }

            // 反向处理
            List<Long> updateIds = updateList.stream().map(GatherStPrice::getId).toList();
            List<Long> fanxIds = filter.stream().filter(o -> !updateIds.contains(o.getId())).map(GatherStPrice::getId).collect(Collectors.toList());

            // 批量更新
            stPriceService.batchUpdateClear2(updateIds, 1);
            stPriceService.batchUpdateClear2(fanxIds, 0);
        }
        return resNum;
    }

    /**
     * 按规则清理库存
     *
     * @param taskId        调研任务ID
     * @param type          数据类型 1:价格 2:库存
     * @param cleanRulesIds 规则id集合
     * @param spNames       规则id集合
     * @return
     */
    @Transactional
    @Override
    public int cleanGatherStInventory(Long taskId, String type, List<String> cleanRulesIds, List<String> spNames){
        int resNum = 0;
        // 1.@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ 查询数据 @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
        List<GatherStInventory> inventoryList = stInventoryService.selectList(new GatherStInventory(){{
            setStId(taskId);
        }});

        // 2.@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ 数据清理 @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
        for(int i = 0; i < cleanRulesIds.size(); i++){
            String ruleId = cleanRulesIds.get(i);
            if(ruleId != null && Long.parseLong(ruleId) <= 0)
                continue;
            // 查询规则
            GatherCleanRules cleanRules = cleanRulesService.selectById(Long.valueOf(ruleId));
            // 查询香烟价格
            String spName = spNames.get(i);
            GatherSmokePrice smokePrice = smokePriceService.selectByName(spName);

            //
            List<GatherStInventory> filter = inventoryList.stream().filter(o -> o.getSpId().equals(smokePrice.getId()) && o.getClear1().compareTo(0) == 0).toList();

            // 清洗数据
            List<GatherStInventory> updateList = new ArrayList<>();
            if(cleanRules.getType().equals("2")){
                // 计算上下限
                double floorNum = (Double.parseDouble(cleanRules.getFloorPfPriceEnable()) * smokePrice.getPfPrice() + Double.parseDouble(cleanRules.getFloorLsPriceEnable()) * smokePrice.getLsPrice()) * cleanRules.getFloorRatio() + cleanRules.getFloorNum();
                double ceilNum = (Double.parseDouble(cleanRules.getCeilPfPriceEnable()) * smokePrice.getPfPrice() + Double.parseDouble(cleanRules.getCeilLsPriceEnable()) * smokePrice.getLsPrice()) * cleanRules.getCeilRatio() + cleanRules.getCeilNum();
                // 清洗数据
                for(GatherStInventory inventory : inventoryList){
                    if(inventory.getSpId().equals(smokePrice.getId())){
                        if((inventory.getNewInventory() < floorNum || inventory.getNewInventory() > ceilNum) && inventory.getNewInventory() != 0){
                            inventory.setClear2(1);
                            updateList.add(inventory);
                        }
                    }
                }
            } else if(cleanRules.getType().equals("3")) {
                // 计算IQR区间
                Double[] doubles = filter.stream().map(GatherStInventory::getNewInventory).toArray(Double[]::new);
                IQRResult res = IQRCalculator.calculateIQR(doubles, cleanRules.getFloorRatio(), cleanRules.getCeilRatio());
                List<GatherStInventory> updated = filter.stream().filter(o -> o.getNewInventory().compareTo(res.lowerBound) < 0 || o.getNewInventory().compareTo(res.upperBound) > 0).toList();
                for (GatherStInventory inventory : updated) {
                    inventory.setClear2(1);
                    updateList.add(inventory);
                }
            }

            // 反向处理
            List<Long> updateIds = updateList.stream().map(GatherStInventory::getId).toList();
            List<Long> fanxIds = filter.stream().filter(o -> !updateIds.contains(o.getId())).map(GatherStInventory::getId).collect(Collectors.toList());

            // 批量更新
            stInventoryService.batchUpdateClear2(updateIds, 1);
            stInventoryService.batchUpdateClear2(fanxIds, 0);
        }
        return resNum;
    }

}

package com.hz.gather.service;

import com.hz.gather.domain.GatherCleanRules;

import java.util.List;

/**
 * 香烟数据清理规则Service接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface IGatherCleanRulesService{

    /**
     * 查询香烟数据清理规则
     *
     * @param id 香烟数据清理规则主键
     * @return 香烟数据清理规则
     */
    public GatherCleanRules selectById(Long id);

    /**
     * 查询香烟数据清理规则列表
     *
     * @param rules 香烟数据清理规则
     * @return 香烟数据清理规则集合
     */
    public List<GatherCleanRules> selectList(GatherCleanRules rules);

    /**
     * 新增香烟数据清理规则
     *
     * @param rules 香烟数据清理规则
     * @return 结果
     */
    public int insert(GatherCleanRules rules);

    /**
     * 修改香烟数据清理规则
     *
     * @param rules 香烟数据清理规则
     * @return 结果
     */
    public int update(GatherCleanRules rules);

    /**
     * 批量删除香烟数据清理规则
     *
     * @param ids 需要删除的香烟数据清理规则主键集合
     * @return 结果
     */
    public int deleteByIds(Long[] ids);

    /**
     * 删除香烟数据清理规则信息
     *
     * @param id 香烟数据清理规则主键
     * @return 结果
     */
    public int deleteById(Long id);
}

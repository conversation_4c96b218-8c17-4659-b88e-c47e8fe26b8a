package com.hz.gather.service;

import java.util.HashMap;
import java.util.List;

import com.hz.gather.domain.GatherStLink;

/**
 * 调研四员联动Service接口
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface IGatherStLinkService{
    /**
     * 查询调研四员联动
     *
     * @param id 调研四员联动主键
     * @return 调研四员联动
     */
    GatherStLink selectById(Long id);

    /**
     * 查询调研四员联动列表
     *
     * @param link 调研四员联动
     * @return 调研四员联动集合
     */
    List<GatherStLink> selectList(GatherStLink link);

    /**
     * 新增调研四员联动
     *
     * @param link 调研四员联动
     * @return 结果
     */
    int insert(GatherStLink link);

    /** 批量插入 */
    int insertBatch(List<GatherStLink> linkList);

    /**
     * 修改调研四员联动
     *
     * @param link 调研四员联动
     * @return 结果
     */
    int update(GatherStLink link);

    /**
     * 批量删除调研四员联动
     *
     * @param ids 需要删除的调研四员联动主键集合
     * @return 结果
     */
    int deleteByIds(Long[] ids);

    /**
     * 删除调研四员联动信息
     *
     * @param id 调研四员联动主键
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 根据任务ID删除调研库存信息
     *
     * @param taskId 任务ID
     * @return 结果
     */
    int deleteByTaskId(long taskId);

    /**
     * 获取调研任务的数据
     *
     * @param taskId  任务ID
     * @param operate 1:原始数据 2:清洗后数据 3:对比数据 4:清洗提示
     * @return 表格数据
     */
    HashMap<String, Object> getTableData(String taskId, String operate);
}

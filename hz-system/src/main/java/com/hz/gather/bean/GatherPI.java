package com.hz.gather.bean;

import com.hz.gather.domain.GatherStInventory;
import com.hz.gather.domain.GatherStPrice;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class GatherPI {

    private String shXkz;
    private String shDw;
    private String shCz;
    private String shYt;
    private Long spId;
    private Double value;

    public GatherPI(GatherStPrice price){
        this.shXkz = price.getShXkz();
        this.shDw = price.getShDw();
        this.shCz = price.getShCz();
        this.shYt = price.getShYt();
        this.spId = price.getSpId();
        this.value = price.getNewPrice();
    }

    public GatherPI(GatherStInventory inventory){
        this.shXkz = inventory.getShXkz();
        this.shDw = inventory.getShDw();
        this.shCz = inventory.getShCz();
        this.shYt = inventory.getShYt();
        this.spId = inventory.getSpId();
        this.value = inventory.getNewInventory();
    }
}

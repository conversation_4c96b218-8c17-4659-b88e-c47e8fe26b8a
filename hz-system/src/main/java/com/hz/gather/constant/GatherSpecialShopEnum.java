package com.hz.gather.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * <h1>特殊商户</h1>
 * <AUTHOR>
 * @date 2025/6/30 11:35
 */
public enum GatherSpecialShopEnum{

    PUYANG_SHI("濮阳市", "000000"),
    HUALONG_QU("华龙区", "111111"),
    QINGFENG_XIAN("清丰县", "222222"),
    NANLE_XIAN("南乐县", "333333"),
    FAN_XIAN("范县", "444444"),
    TAIQIAN_XIAN("台前县", "555555"),
    PUYANG_XIAN("濮阳县", "666666"),
    ;

    private String chineseName;
    private String licence;

    public static boolean hasLicence(String shopLicence){
        if(StringUtils.isBlank(shopLicence)){
            return false;
        }
        for(GatherSpecialShopEnum shop : GatherSpecialShopEnum.values()){
            if(shopLicence.equals(shop.getLicence())){
                return true;
            }
        }
        return false;
    }

    public static String gatNameByLicence(String shopLicence){
        if(StringUtils.isBlank(shopLicence)){
            return "";
        }
        for(GatherSpecialShopEnum shop : GatherSpecialShopEnum.values()){
            if(shopLicence.equals(shop.getLicence())){
                return shop.getChineseName();
            }
        }
        return "";
    }

    GatherSpecialShopEnum(String chineseName, String licence){
        this.chineseName = chineseName;
        this.licence = licence;
    }

    public String getChineseName(){return chineseName;}

    public void setChineseName(String chineseName){this.chineseName = chineseName;}

    public String getLicence(){return licence;}

    public void setLicence(String licence){this.licence = licence;}
}

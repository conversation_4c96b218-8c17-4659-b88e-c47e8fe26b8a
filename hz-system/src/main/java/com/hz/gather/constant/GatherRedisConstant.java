package com.hz.gather.constant;

/**
 * Gather redis 常量
 * <AUTHOR>
 * @date 2025/6/21 11:59
 */
public class GatherRedisConstant{

    // ############################# smokePrice 香烟价格体系 #############################
    /** 香烟价格体系 信息 */
    public static final String SMOKE_PRICE_INFO_byId = "gather:smokePrice:info:id:";
    public static final String SMOKE_PRICE_INFO_byName = "gather:smokePrice:info:name:";


    // ############################# IQR #############################
    /** 库存信息 */
    public static final String IQR_PRICE = "gather:IQR:price:";
    public static final String IQR_INVENTORY = "gather:IQR:inventory:";

}

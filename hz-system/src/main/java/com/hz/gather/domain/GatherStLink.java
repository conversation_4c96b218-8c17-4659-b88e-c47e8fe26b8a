package com.hz.gather.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hz.common.annotation.Excel;
import com.hz.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 调研四员联动对象 gather_st_link
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GatherStLink extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;
    /** 调研任务id */
    @Excel(name = "调研任务id")
    private Long stId;
    /** 商户许可证号 */
    @Excel(name = "商户许可证号")
    private String shXkz;
    /** 提交人 */
    @Excel(name = "提交人")
    private String tjr;
    /** 提交时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date tjsj;
    /** 内容详情 */
    @Excel(name = "内容详情")
    private String content;

}

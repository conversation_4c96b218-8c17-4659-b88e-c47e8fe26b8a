package com.hz.gather.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hz.common.annotation.Excel;
import com.hz.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 调研库存对象 gather_st_inventory
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GatherStInventory extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;
    /** 调研任务id */
    @Excel(name = "调研任务id")
    private Long stId;
    /** 商户许可证号 */
    @Excel(name = "商户许可证号")
    private String shXkz;
    /** 商户档位 */
    @Excel(name = "商户档位")
    private String shDw;
    /** 商户城镇 */
    @Excel(name = "商户城镇")
    private String shCz;
    /** 商户业态 */
    @Excel(name = "商户业态")
    private String shYt;
    /** 商户营销线 */
    @Excel(name = "商户营销线")
    private String shYxx;
    /** 卷烟品规id */
    @Excel(name = "卷烟品规id")
    private Long spId;
    /** 原始库存 */
    @Excel(name = "原始库存")
    private Double oldInventory;
    /** 最新库存 */
    @Excel(name = "最新库存")
    private Double newInventory;
    /** 第一次清洗（是否：1是，0：否） */
    @Excel(name = "第一次清洗", readConverterExp = "是=否：1是，2：否")
    private Integer clear1;
    /** 第二次清洗（是否：1是，0：否） */
    @Excel(name = "第二次清洗", readConverterExp = "是=否：1是，2：否")
    private Integer clear2;

    /** 提交时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date tjsj;

}

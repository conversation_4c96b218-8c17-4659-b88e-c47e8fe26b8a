package com.hz.gather.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hz.common.annotation.Excel;
import com.hz.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 调研任务对象 gather_survey_task
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GatherSurveyTask extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;
    /** 任务名称 */
    @Excel(name = "任务名称")
    private String name;
    /** 年 */
    private String year;
    /** 月 */
    private String month;
    /** 周 */
    private String week;
    /** 年月周year-month-week组合 */
    @Excel(name = "年月周")
    private String yearMonthWeek;
    /** 价格调研 导入情况（0：未导入，1：已导入） */
    @Excel(name = "价格调研", readConverterExp = "0=未导入,1=已导入")
    private String importStatusPrice;
    /** 库存调研 导入情况（0：未导入，1：已导入） */
    @Excel(name = "库存调研", readConverterExp = "0=未导入,1=已导入")
    private String importStatusInventory;
    /** 四员联动 导入情况（0：未导入，1：已导入） */
    @Excel(name = "四员联动", readConverterExp = "0=未导入,1=已导入")
    private String importStatusLink;

    /** 价格调研 导入时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "价格调研 导入时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date importTimePrice;
    /** 库存调研 导入时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "库存调研 导入时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date importTimeInventory;
    /** 四员联动 导入时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "四员联动 导入时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date importTimeLink;

    /*价格调研  导入文件*/
    private String importFilePrice;
    /*库存调研  导入文件*/
    private String importFileInventory;
    /*四员联动  导入文件*/
    private String importFileLink;
    /** 说明 */
    @Excel(name = "说明")
    private String description;

    /** 开始时间 查询条件 */
    private String startTime;
    /** 结束时间 查询条件 */
    private String endTime;

    // 非数据库字段
    /** 清理方法 */
    private List<String> crIds;
    /** 商品名称 */
    private List<String> spNames;
    private String type;
}

package com.hz.gather.domain;

import com.hz.common.annotation.Excel;
import com.hz.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 卷烟价格对象 gather_smoke_price
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GatherSmokePrice extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;
    /** 品规名称 */
    @Excel(name = "品规名称")
    private String name;
    /** 品规编码 */
    @Excel(name = "品规编码")
    private String code;
    /** 建议零售价 */
    @Excel(name = "建议零售价")
    private Double lsPrice;
    /** 批发价 */
    @Excel(name = "批发价")
    private Double pfPrice;
    /** 品规价类(一类, 二类, 三类, 四类,五类) */
    @Excel(name = "品规价类")
    private String category;

    // 非数据库字段
    private String classify;
    private Long stId;
    private String stIds;
}

package com.hz.gather.domain;

import com.hz.common.annotation.Excel;
import com.hz.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 香烟数据清理规则对象 gather_clean_rules
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GatherCleanRules extends BaseEntity{

    private static final long serialVersionUID = 1L;

    private Long id;
    /** 规则名称 */
    @Excel(name = "规则名称")
    private String name;
    /** 规则类型 1价格 2库存 */
    @Excel(name = "规则类型 1价格 2库存")
    private String type;

    /** 下限 批发价是否生效 0不生效 1生效 */
    @Excel(name = "下限 批发价是否生效 0不生效 1生效")
    private String floorPfPriceEnable;
    /** 下限 零售价格是否生效 0不生效 1生效 */
    @Excel(name = "下限 零售价格是否生效 0不生效 1生效")
    private String floorLsPriceEnable;
    /** 下限 百分比 (零售价格 * floor_pf_price_enable * ratio + 批发价 * floor_pf_price_enable * ratio) */
    @Excel(name = "下限 百分比 (零售价格 * floor_pf_price_enable * ratio + 批发价 * floor_pf_price_enable * ratio)")
    private Double floorRatio;
    /** 下限价格(和零售价/批发价百分比互斥, 优先级低于百分比) */
    @Excel(name = "下限价格(和零售价/批发价百分比互斥, 优先级低于百分比)")
    private Long floorNum;

    /** 上限 批发价是否生效 0不生效 1生效 */
    @Excel(name = "上限 批发价是否生效 0不生效 1生效")
    private String ceilPfPriceEnable;
    /** 上限 零售价格是否生效 0不生效 1生效 */
    @Excel(name = "上限 零售价格是否生效 0不生效 1生效")
    private String ceilLsPriceEnable;
    /** 上限 百分比 (零售价格 * floor_pf_price_enable * ratio + 批发价 * floor_pf_price_enable * ratio) */
    @Excel(name = "上限 百分比 (零售价格 * floor_pf_price_enable * ratio + 批发价 * floor_pf_price_enable * ratio)")
    private Double ceilRatio;
    /** 上限价格(和零售价/批发价百分比互斥, 优先级低于百分比) */
    @Excel(name = "上限价格(和零售价/批发价百分比互斥, 优先级低于百分比)")
    private Long ceilNum;

    /** 是否默认 1是 0不是 */
    @Excel(name = "是否默认 1是 0不是")
    private Long isDefault;
    /** 删除标志（0代表存在 2代表删除） */
    @Excel(name = "删除标记 0正常 1删除")
    private String delFlag;
    /** 是否禁用 0正常 1禁用 */
    @Excel(name = "是否禁用 0正常 1禁用")
    private String unenable;

}

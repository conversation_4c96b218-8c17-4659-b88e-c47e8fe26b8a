package com.hz.gather.mapper;

import java.util.List;

import com.hz.gather.domain.GatherStLink;
import org.apache.ibatis.annotations.Delete;

/**
 * 调研四员联动Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface GatherStLinkMapper{
    /**
     * 查询调研四员联动
     *
     * @param id 调研四员联动主键
     * @return 调研四员联动
     */
    GatherStLink selectById(Long id);

    /**
     * 查询调研四员联动列表
     *
     * @param link 调研四员联动
     * @return 调研四员联动集合
     */
    List<GatherStLink> selectList(GatherStLink link);

    /**
     * 新增调研四员联动
     *
     * @param link 调研四员联动
     * @return 结果
     */
    int insert(GatherStLink link);

    /**
     * 修改调研四员联动
     *
     * @param link 调研四员联动
     * @return 结果
     */
    int update(GatherStLink link);

    /**
     * 删除调研四员联动
     *
     * @param id 调研四员联动主键
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 批量删除调研四员联动
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteByIds(Long[] ids);

    int insertBatch(List<GatherStLink> batch);


    /**
     * 根据任务ID删除调研库存信息
     *
     * @param taskId 任务ID
     * @return 结果
     */
    int deleteByTaskId(long taskId);
}

package com.hz.gather.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.hz.gather.domain.GatherStPrice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 调研价格Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface GatherStPriceMapper{
    /**
     * 查询调研价格
     *
     * @param id 调研价格主键
     * @return 调研价格
     */
    GatherStPrice selectById(Long id);

    /**
     * 查询调研价格列表
     *
     * @param price 调研价格
     * @return 调研价格集合
     */
    List<GatherStPrice> selectList(GatherStPrice price);

    /**
     * 新增调研价格
     *
     * @param price 调研价格
     * @return 结果
     */
    int insert(GatherStPrice price);

    /**
     * 批量插入调研价格
     *
     * @param batch 调研价格列表
     * @return 插入成功的记录数
     */
    int insertBatch(List<GatherStPrice> batch);

    /**
     * 修改调研价格
     *
     * @param price 调研价格
     * @return 结果
     */
    int update(GatherStPrice price);

    /**
     * 删除调研价格
     *
     * @param id 调研价格主键
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 批量删除调研价格
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteByIds(Long[] ids);

    /**
     * 根据任务ID删除调研价格信息
     *
     * @param taskId 任务ID
     * @return 结果
     */
    int deleteByTaskId(@Param(value = "taskId") long taskId);

    /**
     * 批量更新
     *
     * @param list
     * @param clear2
     */
    void batchUpdateClear2(@Param("list") List<Long> list, @Param("clear2") int clear2);

    /**
     * <h1>统计每个营销线上调研数据错误的商户数量</h1>
     *
     * @param taskId 任务ID
     * @return 调研价格信息
     */
    List<JSONObject> countYxxShopNumOfErrorByTaskId(@Param(value = "taskId") String taskId);
}

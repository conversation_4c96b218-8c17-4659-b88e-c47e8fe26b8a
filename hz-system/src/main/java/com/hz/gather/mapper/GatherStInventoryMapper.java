package com.hz.gather.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.hz.gather.domain.GatherStInventory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 调研库存Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface GatherStInventoryMapper{
    /**
     * 查询调研库存
     *
     * @param id 调研库存主键
     * @return 调研库存
     */
    GatherStInventory selectById(Long id);

    /**
     * 查询调研库存列表
     *
     * @param inventory 调研库存
     * @return 调研库存集合
     */
    List<GatherStInventory> selectList(GatherStInventory inventory);

    /**
     * 新增调研库存
     *
     * @param inventory 调研库存
     * @return 结果
     */
    int insert(GatherStInventory inventory);

    /**
     * 批量插入调研库存
     *
     * @param batch 调研库存列表
     * @return 插入成功的记录数
     */
    int insertBatch(List<GatherStInventory> batch);
    /**
     * 修改调研库存
     *
     * @param inventory 调研库存
     * @return 结果
     */
    int update(GatherStInventory inventory);

    /**
     * 删除调研库存
     *
     * @param id 调研库存主键
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 批量删除调研库存
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteByIds(Long[] ids);

    /**
     * 根据任务ID删除调研库存信息
     *
     * @param taskId 任务ID
     * @return 结果
     */
    int deleteByTaskId(@Param("taskId") long taskId);

    /**
     * 批量更新
     *
     * @param list
     * @param clear2
     */
    void batchUpdateClear2(@Param("list") List<Long> list, @Param("clear2") int clear2);

    /**
     * <h1>统计每个营销线上调研数据错误的商户数量</h1>
     *
     * @param taskId 任务ID
     * @return 调研价格信息
     */
    List<JSONObject> countYxxShopNumOfErrorByTaskId(String taskId);

}

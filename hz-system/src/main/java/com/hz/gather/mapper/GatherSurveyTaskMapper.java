package com.hz.gather.mapper;

import java.util.List;

import com.hz.gather.domain.GatherSurveyTask;

/**
 * 调研任务Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface GatherSurveyTaskMapper{
    /**
     * 查询调研任务
     *
     * @param id 调研任务主键
     * @return 调研任务
     */
    GatherSurveyTask selectById(Long id);

    /**
     * 查询调研任务列表
     *
     * @param gatherSurveyTask 调研任务
     * @return 调研任务集合
     */
    List<GatherSurveyTask> selectList(GatherSurveyTask gatherSurveyTask);

    /**
     * 新增调研任务
     *
     * @param gatherSurveyTask 调研任务
     * @return 结果
     */
    int insert(GatherSurveyTask gatherSurveyTask);

    /**
     * 修改调研任务
     *
     * @param gatherSurveyTask 调研任务
     * @return 结果
     */
    int update(GatherSurveyTask gatherSurveyTask);

    /**
     * 删除调研任务
     *
     * @param id 调研任务主键
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 批量删除调研任务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteByIds(Long[] ids);
}

package com.hz.gather.mapper;

import com.hz.gather.domain.GatherSmokePrice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 卷烟价格Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface GatherSmokePriceMapper{
    /**
     * 查询卷烟价格
     *
     * @param id 卷烟价格主键
     * @return 卷烟价格
     */
    GatherSmokePrice selectById(Long id);

    /**
     * 查询卷烟价格列表
     *
     * @param smokePrice 卷烟价格
     * @return 卷烟价格集合
     */
    List<GatherSmokePrice> selectList(GatherSmokePrice smokePrice);

    /**
     * 该次任务下的卷烟品规(价格、品规)
     *
     * @param smokePrice 卷烟价格
     * @return 卷烟价格集合
     */
    List<GatherSmokePrice> gaicSmokePrice(GatherSmokePrice smokePrice);

    /**
     * 累计任务下的卷烟品规(价格、品规)
     *
     * @param classify
     * @param stIds
     * @return 卷烟价格集合
     */
    List<GatherSmokePrice> leijSmokePrice(@Param("classify") String classify, @Param("stIds") String stIds);

    /**
     * 新增卷烟价格
     *
     * @param smokePrice 卷烟价格
     * @return 结果
     */
    int insert(GatherSmokePrice smokePrice);

    /**
     * 修改卷烟价格
     *
     * @param smokePrice 卷烟价格
     * @return 结果
     */
    int update(GatherSmokePrice smokePrice);

    /**
     * 删除卷烟价格
     *
     * @param id 卷烟价格主键
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 批量删除卷烟价格
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteByIds(Long[] ids);

}

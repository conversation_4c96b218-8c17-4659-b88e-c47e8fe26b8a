package com.hz.gather.mapper;

import com.hz.gather.domain.GatherCleanRules;

import java.util.List;

/**
 * 香烟数据清理规则Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface GatherCleanRulesMapper{

    /**
     * 查询香烟数据清理规则
     *
     * @param id 香烟数据清理规则主键
     * @return 香烟数据清理规则
     */
    GatherCleanRules selectById(Long id);

    /**
     * 查询香烟数据清理规则列表
     *
     * @param rules 香烟数据清理规则
     * @return 香烟数据清理规则集合
     */
    List<GatherCleanRules> selectList(GatherCleanRules rules);

    /**
     * 新增香烟数据清理规则
     *
     * @param rules 香烟数据清理规则
     * @return 结果
     */
    int insert(GatherCleanRules rules);

    /**
     * 修改香烟数据清理规则
     *
     * @param rules 香烟数据清理规则
     * @return 结果
     */
    int update(GatherCleanRules rules);

    /**
     * 删除香烟数据清理规则
     *
     * @param id 香烟数据清理规则主键
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 批量删除香烟数据清理规则
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteByIds(Long[] ids);
}

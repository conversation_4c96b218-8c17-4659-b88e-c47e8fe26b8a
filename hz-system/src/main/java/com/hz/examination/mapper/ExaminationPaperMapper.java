package com.hz.examination.mapper;

import java.util.List;

import com.hz.examination.domain.ExaminationPaper;
import org.apache.ibatis.annotations.Param;

/**
 * 考核Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface ExaminationPaperMapper 
{
    /**
     * 查询考核
     * 
     * @param id 考核主键
     * @return 考核
     */
    public ExaminationPaper selectExaminationPaperById(Long id);

    /**
     * 查询考核列表
     * 
     * @param examinationPaper 考核
     * @return 考核集合
     */
    public List<ExaminationPaper> selectExaminationPaperList(ExaminationPaper examinationPaper);

    public List<ExaminationPaper> selectExaminationPaperListByMonthlyId(Long monthlyId);

    public List<ExaminationPaper> selectExaminationPaperListByTypeAndBrand(@Param("type") String type, @Param("brand") String brand);

    /**
     * 新增考核
     * 
     * @param examinationPaper 考核
     * @return 结果
     */
    public int insertExaminationPaper(ExaminationPaper examinationPaper);

    public int batchInsertExaminationPaper(List<ExaminationPaper> examinationPapers);

    /**
     * 修改考核
     * 
     * @param examinationPaper 考核
     * @return 结果
     */
    public int updateExaminationPaper(ExaminationPaper examinationPaper);

    /**
     * 删除考核
     * 
     * @param id 考核主键
     * @return 结果
     */
    public int deleteExaminationPaperById(Long id);

    public int deleteExaminationPaperByMonthlyId(Long monthlyId);

    /**
     * 批量删除考核
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExaminationPaperByIds(Long[] ids);

    /**
     * 根据季度考核ID统计考核数量
     * 
     * @param quarterlyAssessmentId 季度考核ID
     * @return 考核数量
     */
    public int countExaminationPaperByQuarterlyAssessmentId(String quarterlyAssessmentId);
}

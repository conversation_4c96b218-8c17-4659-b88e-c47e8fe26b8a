package com.hz.examination.mapper;

import java.util.List;

import com.hz.examination.domain.ExaminationReplyMerchant;
import org.apache.ibatis.annotations.Param;

/**
 * 考核题目商户回答Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface ExaminationReplyMerchantMapper 
{
    /**
     * 查询考核题目商户回答
     * 
     * @param id 考核题目商户回答主键
     * @return 考核题目商户回答
     */
    public ExaminationReplyMerchant selectExaminationReplyMerchantById(Long id);

    public int selectExaminationReplyMerchantByPaperId(@Param("paperId") Long paperId, @Param("merchantId") Long merchantId);

    /**
     * 查询考核题目商户回答列表
     * 
     * @param examinationReplyMerchant 考核题目商户回答
     * @return 考核题目商户回答集合
     */
    public List<ExaminationReplyMerchant> selectExaminationReplyMerchantList(ExaminationReplyMerchant examinationReplyMerchant);

    /**
     * 新增考核题目商户回答
     * 
     * @param examinationReplyMerchant 考核题目商户回答
     * @return 结果
     */
    public int insertExaminationReplyMerchant(ExaminationReplyMerchant examinationReplyMerchant);

    public int batchInsertExaminationReplyMerchant(List<ExaminationReplyMerchant> examinationReplyMerchants);

    /**
     * 修改考核题目商户回答
     * 
     * @param examinationReplyMerchant 考核题目商户回答
     * @return 结果
     */
    public int updateExaminationReplyMerchant(ExaminationReplyMerchant examinationReplyMerchant);

    /**
     * 删除考核题目商户回答
     * 
     * @param id 考核题目商户回答主键
     * @return 结果
     */
    public int deleteExaminationReplyMerchantById(Long id);

    public int batchDeleteExaminationReplyMerchantById(@Param("paperId") Long paperId, @Param("merchantId") Long merchantId);

    /**
     * 批量删除考核题目商户回答
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExaminationReplyMerchantByIds(Long[] ids);
}

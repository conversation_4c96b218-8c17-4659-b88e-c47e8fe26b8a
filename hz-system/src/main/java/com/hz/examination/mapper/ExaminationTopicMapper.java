package com.hz.examination.mapper;

import java.util.List;

import com.hz.examination.domain.ExaminationTopic;
import org.apache.ibatis.annotations.Param;

/**
 * 考核题目Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface ExaminationTopicMapper 
{
    /**
     * 查询考核题目
     * 
     * @param id 考核题目主键
     * @return 考核题目
     */
    public ExaminationTopic selectExaminationTopicById(Long id);

    public List<ExaminationTopic> selectExaminationTopicByPaperId(Long paperId);

    public List<ExaminationTopic> selectExaminationTopicOfMerchantByPaperId(@Param("paperId") Long paperId, @Param("merchantId") Long merchantId);

    /**
     * 查询考核题目列表
     * 
     * @param examinationTopic 考核题目
     * @return 考核题目集合
     */
    public List<ExaminationTopic> selectExaminationTopicList(ExaminationTopic examinationTopic);

    /**
     * 新增考核题目
     * 
     * @param examinationTopic 考核题目
     * @return 结果
     */
    public int insertExaminationTopic(ExaminationTopic examinationTopic);

    public int batchInsertExaminationTopic(List<ExaminationTopic> examinationTopics);

    /**
     * 修改考核题目
     * 
     * @param examinationTopic 考核题目
     * @return 结果
     */
    public int updateExaminationTopic(ExaminationTopic examinationTopic);

    /**
     * 删除考核题目
     * 
     * @param id 考核题目主键
     * @return 结果
     */
    public int deleteExaminationTopicById(Long id);

    public int batchDeleteExaminationTopicById(Long paperId);

    /**
     * 批量删除考核题目
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExaminationTopicByIds(Long[] ids);
}

package com.hz.examination.mapper;

import com.hz.examination.domain.ExaminationMessage;

import java.util.List;

/**
 * 考核消息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface ExaminationMessageMapper 
{
    /**
     * 查询考核消息
     * 
     * @param msgId 考核消息主键
     * @return 考核消息
     */
    public ExaminationMessage selectExaminationMessageByMsgId(Long msgId);

    /**
     * 查询考核消息列表
     * 
     * @param examinationMessage 考核消息
     * @return 考核消息集合
     */
    public List<ExaminationMessage> selectExaminationMessageList(ExaminationMessage examinationMessage);

    public int getCountUnRead(Long createId);

    /**
     * 新增考核消息
     * 
     * @param examinationMessage 考核消息
     * @return 结果
     */
    public int insertExaminationMessage(ExaminationMessage examinationMessage);

    /**
     * 修改考核消息
     * 
     * @param examinationMessage 考核消息
     * @return 结果
     */
    public int updateExaminationMessage(ExaminationMessage examinationMessage);

    /**
     * 删除考核消息
     * 
     * @param msgId 考核消息主键
     * @return 结果
     */
    public int deleteExaminationMessageByMsgId(Long msgId);

    /**
     * 批量删除考核消息
     * 
     * @param msgIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExaminationMessageByMsgIds(Long[] msgIds);
}

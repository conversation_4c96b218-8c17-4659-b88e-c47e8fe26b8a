package com.hz.examination.domain;

import com.hz.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hz.common.annotation.Excel;

import java.math.BigDecimal;

/**
 * 考核题目回答对象 examination_reply
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public class ExaminationReply extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    private Long paperId;

    private Integer topicNum;

    /** 回答 */
    @Excel(name = "回答")
    private String name;

    /** 分数 */
    @Excel(name = "分数")
    private BigDecimal score;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public Long getPaperId() {
        return paperId;
    }

    public void setPaperId(Long paperId) {
        this.paperId = paperId;
    }

    public void setTopicNum(Integer topicNum)
    {
        this.topicNum = topicNum;
    }

    public Integer getTopicNum()
    {
        return topicNum;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setScore(BigDecimal score)
    {
        this.score = score;
    }

    public BigDecimal getScore()
    {
        return score;
    }

    public void setSort(Long sort) 
    {
        this.sort = sort;
    }

    public Long getSort() 
    {
        return sort;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("topicNum", getTopicNum())
            .append("name", getName())
            .append("score", getScore())
            .append("sort", getSort())
            .append("createId", getCreateId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("remark", getRemark())
            .toString();
    }
}

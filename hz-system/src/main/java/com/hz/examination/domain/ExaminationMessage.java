package com.hz.examination.domain;

import com.hz.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hz.common.annotation.Excel;

/**
 * 考核消息对象 examination_message
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
public class ExaminationMessage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 消息ID */
    private Long msgId;

    /** 申诉ID */
    private Long parentId;

    private Long paperId;

    private Long merchantId;

    private Long monthlyAssessmentsId;

    /** 消息标题 */
    private String msgTitle;

    /** 消息类型（1申诉 2回复） */
    private String msgType;

    /** 消息内容 */
    private String msgContent;

    private String msgImage;

    private String msgReply;

    /** 消息状态（1未读 2已读 3已回） */
    private String status;

    /** 接收人 */
    private String receiverId;

    private String paperName;
    private String licence;
    private String merchantName;
    private String county;

    private String createTimeStr;

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public String getLicence() {
        return licence;
    }

    public void setLicence(String licence) {
        this.licence = licence;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public void setMsgId(Long msgId)
    {
        this.msgId = msgId;
    }

    public Long getMsgId() 
    {
        return msgId;
    }

    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }

    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    public String getMsgImage() {
        return msgImage;
    }

    public void setMsgImage(String msgImage) {
        this.msgImage = msgImage;
    }

    public String getMsgReply() {
        return msgReply;
    }

    public void setMsgReply(String msgReply) {
        this.msgReply = msgReply;
    }

    public Long getMonthlyAssessmentsId() {
        return monthlyAssessmentsId;
    }

    public void setMonthlyAssessmentsId(Long monthlyAssessmentsId) {
        this.monthlyAssessmentsId = monthlyAssessmentsId;
    }

    public Long getPaperId() {
        return paperId;
    }

    public void setPaperId(Long paperId) {
        this.paperId = paperId;
    }

    public void setMsgTitle(String msgTitle)
    {
        this.msgTitle = msgTitle;
    }

    public String getMsgTitle() 
    {
        return msgTitle;
    }

    public void setMsgType(String msgType) 
    {
        this.msgType = msgType;
    }

    public String getMsgType() 
    {
        return msgType;
    }

    public void setMsgContent(String msgContent) 
    {
        this.msgContent = msgContent;
    }

    public String getMsgContent() 
    {
        return msgContent;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setReceiverId(String receiverId) 
    {
        this.receiverId = receiverId;
    }

    public String getReceiverId() 
    {
        return receiverId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("msgId", getMsgId())
            .append("parentId", getParentId())
            .append("msgTitle", getMsgTitle())
            .append("msgType", getMsgType())
            .append("msgContent", getMsgContent())
            .append("status", getStatus())
            .append("receiverId", getReceiverId())
            .append("createId", getCreateId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateId", getUpdateId())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

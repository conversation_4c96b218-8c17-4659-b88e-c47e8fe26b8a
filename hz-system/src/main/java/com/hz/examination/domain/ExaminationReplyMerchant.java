package com.hz.examination.domain;

import com.hz.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hz.common.annotation.Excel;

import java.math.BigDecimal;

/**
 * 考核题目商户回答对象 examination_reply_merchant
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public class ExaminationReplyMerchant extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    private Long paperId;

    /** MERCHANT_ID */
    @Excel(name = "MERCHANT_ID")
    private Long merchantId;

    private Integer topicNum;

    /** 是否作答(0作答 1未作答) */
    @Excel(name = "是否作答(0作答 1未作答)")
    private String isAnswer;

    /** 回答 */
    @Excel(name = "回答")
    private String name;

    private String blanks;

    /** 分数 */
    @Excel(name = "分数")
    private BigDecimal score;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public Long getPaperId() {
        return paperId;
    }

    public void setPaperId(Long paperId) {
        this.paperId = paperId;
    }

    public void setMerchantId(Long merchantId)
    {
        this.merchantId = merchantId;
    }

    public Long getMerchantId() 
    {
        return merchantId;
    }

    public void setTopicNum(Integer topicNum)
    {
        this.topicNum = topicNum;
    }

    public Integer getTopicNum()
    {
        return topicNum;
    }

    public void setIsAnswer(String isAnswer) 
    {
        this.isAnswer = isAnswer;
    }

    public String getIsAnswer() 
    {
        return isAnswer;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public String getBlanks() {
        return blanks;
    }

    public void setBlanks(String blanks) {
        this.blanks = blanks;
    }

    public void setScore(BigDecimal score)
    {
        this.score = score;
    }

    public BigDecimal getScore()
    {
        return score;
    }

    public void setSort(Long sort) 
    {
        this.sort = sort;
    }

    public Long getSort() 
    {
        return sort;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("merchantId", getMerchantId())
            .append("topicNum", getTopicNum())
            .append("isAnswer", getIsAnswer())
            .append("name", getName())
            .append("score", getScore())
            .append("sort", getSort())
            .append("createId", getCreateId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("remark", getRemark())
            .toString();
    }
}

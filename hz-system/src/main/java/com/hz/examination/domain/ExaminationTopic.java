package com.hz.examination.domain;

import com.hz.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hz.common.annotation.Excel;

import java.util.List;

/**
 * 考核题目对象 examination_topic
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public class ExaminationTopic extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** PAPER_ID */
    @Excel(name = "PAPER_ID")
    private Long paperId;

    private Integer num;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 是否必填（0必填 1非必填） */
    @Excel(name = "是否必填", readConverterExp = "0=必填,1=非必填")
    private String isRequired;

    /** 提示 */
    @Excel(name = "提示")
    private String prompt;

    /** 题目大类 */
    @Excel(name = "题目大类")
    private String majorType;

    /** 题目大类名称 */
    @Excel(name = "题目大类名称")
    private String majorTypeName;

    /** 题目小类 */
    @Excel(name = "题目小类")
    private String smallType;

    /** 题目小类名称 */
    @Excel(name = "题目小类名称")
    private String smallTypeName;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    private List<ExaminationReply> examinationReplyList;

    private List<ExaminationReplyMerchant> examinationReplyMerchantList;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setPaperId(Long paperId) 
    {
        this.paperId = paperId;
    }

    public Long getPaperId() 
    {
        return paperId;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }

    public void setIsRequired(String isRequired) 
    {
        this.isRequired = isRequired;
    }

    public String getIsRequired() 
    {
        return isRequired;
    }

    public void setPrompt(String prompt) 
    {
        this.prompt = prompt;
    }

    public String getPrompt() 
    {
        return prompt;
    }

    public void setMajorType(String majorType) 
    {
        this.majorType = majorType;
    }

    public String getMajorType() 
    {
        return majorType;
    }

    public void setMajorTypeName(String majorTypeName) 
    {
        this.majorTypeName = majorTypeName;
    }

    public String getMajorTypeName() 
    {
        return majorTypeName;
    }

    public void setSmallType(String smallType) 
    {
        this.smallType = smallType;
    }

    public String getSmallType() 
    {
        return smallType;
    }

    public void setSmallTypeName(String smallTypeName) 
    {
        this.smallTypeName = smallTypeName;
    }

    public String getSmallTypeName() 
    {
        return smallTypeName;
    }

    public void setSort(Long sort) 
    {
        this.sort = sort;
    }

    public Long getSort() 
    {
        return sort;
    }

    public List<ExaminationReply> getExaminationReplyList() {
        return examinationReplyList;
    }

    public void setExaminationReplyList(List<ExaminationReply> examinationReplyList) {
        this.examinationReplyList = examinationReplyList;
    }

    public List<ExaminationReplyMerchant> getExaminationReplyMerchantList() {
        return examinationReplyMerchantList;
    }

    public void setExaminationReplyMerchantList(List<ExaminationReplyMerchant> examinationReplyMerchantList) {
        this.examinationReplyMerchantList = examinationReplyMerchantList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("paperId", getPaperId())
            .append("title", getTitle())
            .append("isRequired", getIsRequired())
            .append("prompt", getPrompt())
            .append("majorType", getMajorType())
            .append("majorTypeName", getMajorTypeName())
            .append("smallType", getSmallType())
            .append("smallTypeName", getSmallTypeName())
            .append("sort", getSort())
            .append("createId", getCreateId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("remark", getRemark())
            .toString();
    }
}

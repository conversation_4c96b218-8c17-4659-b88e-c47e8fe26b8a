package com.hz.examination.domain;

import com.hz.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hz.common.annotation.Excel;

import java.math.BigDecimal;

/**
 * 考核对象 examination_paper
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public class ExaminationPaper extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    private Long monthlyId;

    /** 考核名称 */
    @Excel(name = "考核名称")
    private String name;

    /** 考核类型 */
    @Excel(name = "考核类型")
    private String type;

    /** 考核类型名称 */
    @Excel(name = "考核类型名称")
    private String typeName;

    /** 考核终端 */
    @Excel(name = "考核终端")
    private String brand;

    /** 考核终端名称 */
    @Excel(name = "考核终端名称")
    private String brandName;

    /** 考核状态 */
    @Excel(name = "考核状态")
    private String status;

    /** 考核是否禁用(0正常 1禁用) */
    @Excel(name = "考核是否禁用(0正常 1禁用)")
    private String isDisable;

    private int topicCount;

    private BigDecimal sumScore;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    public BigDecimal getSumScore() {
        return sumScore;
    }

    public void setSumScore(BigDecimal sumScore) {
        this.sumScore = sumScore;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public Long getMonthlyId() {
        return monthlyId;
    }

    public void setMonthlyId(Long monthlyId) {
        this.monthlyId = monthlyId;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }

    public void setTypeName(String typeName) 
    {
        this.typeName = typeName;
    }

    public String getTypeName() 
    {
        return typeName;
    }

    public void setBrand(String brand) 
    {
        this.brand = brand;
    }

    public String getBrand() 
    {
        return brand;
    }

    public void setBrandName(String brandName) 
    {
        this.brandName = brandName;
    }

    public String getBrandName() 
    {
        return brandName;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setIsDisable(String isDisable) 
    {
        this.isDisable = isDisable;
    }

    public String getIsDisable() 
    {
        return isDisable;
    }

    public int getTopicCount() {
        return topicCount;
    }

    public void setTopicCount(int topicCount) {
        this.topicCount = topicCount;
    }

    public void setSort(Long sort)
    {
        this.sort = sort;
    }

    public Long getSort() 
    {
        return sort;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("type", getType())
            .append("typeName", getTypeName())
            .append("brand", getBrand())
            .append("brandName", getBrandName())
            .append("status", getStatus())
            .append("isDisable", getIsDisable())
            .append("sort", getSort())
            .append("createId", getCreateId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateId", getUpdateId())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

package com.hz.examination.service;

import java.util.List;

import com.hz.examination.domain.ExaminationTopic;

/**
 * 考核题目Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface IExaminationTopicService 
{
    /**
     * 查询考核题目
     * 
     * @param id 考核题目主键
     * @return 考核题目
     */
    public ExaminationTopic selectExaminationTopicById(Long id);

    public List<ExaminationTopic> selectWxExaminationTopicByPaperId(Long paperId, Long merchantId);

    public List<ExaminationTopic> selectExaminationTopicByPaperId(Long paperId);

    public List<ExaminationTopic> selectExaminationTopicOfMerchantByPaperId(Long paperId, Long merchantId);

    /**
     * 查询考核题目列表
     * 
     * @param examinationTopic 考核题目
     * @return 考核题目集合
     */
    public List<ExaminationTopic> selectExaminationTopicList(ExaminationTopic examinationTopic);

    /**
     * 新增考核题目
     * 
     * @param examinationTopic 考核题目
     * @return 结果
     */
    public int insertExaminationTopic(ExaminationTopic examinationTopic);

    public int batchInsertExaminationTopic(List<ExaminationTopic> examinationTopics, Long paperId, Long createId, String createBy);

    public int batchInsertExaminationTopicByWx(List<ExaminationTopic> examinationTopics, Long monthlyId, Long paperId, Long merchantId, Long createId, String createBy, boolean isMonopoly);

    public int batchInsertExaminationTopicByWeb(List<ExaminationTopic> examinationTopics, Long paperId, Long merchantId, Long createId, String createBy);

    /**
     * 修改考核题目
     * 
     * @param examinationTopic 考核题目
     * @return 结果
     */
    public int updateExaminationTopic(ExaminationTopic examinationTopic);

    /**
     * 批量删除考核题目
     * 
     * @param ids 需要删除的考核题目主键集合
     * @return 结果
     */
    public int deleteExaminationTopicByIds(Long[] ids);

    /**
     * 删除考核题目信息
     * 
     * @param id 考核题目主键
     * @return 结果
     */
    public int deleteExaminationTopicById(Long id);
}

package com.hz.examination.service;

import java.util.List;

import com.hz.examination.domain.ExaminationReply;

/**
 * 考核题目回答Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface IExaminationReplyService 
{
    /**
     * 查询考核题目回答
     * 
     * @param id 考核题目回答主键
     * @return 考核题目回答
     */
    public ExaminationReply selectExaminationReplyById(Long id);

    /**
     * 查询考核题目回答列表
     * 
     * @param examinationReply 考核题目回答
     * @return 考核题目回答集合
     */
    public List<ExaminationReply> selectExaminationReplyList(ExaminationReply examinationReply);

    /**
     * 新增考核题目回答
     * 
     * @param examinationReply 考核题目回答
     * @return 结果
     */
    public int insertExaminationReply(ExaminationReply examinationReply);

    /**
     * 修改考核题目回答
     * 
     * @param examinationReply 考核题目回答
     * @return 结果
     */
    public int updateExaminationReply(ExaminationReply examinationReply);

    /**
     * 批量删除考核题目回答
     * 
     * @param ids 需要删除的考核题目回答主键集合
     * @return 结果
     */
    public int deleteExaminationReplyByIds(Long[] ids);

    /**
     * 删除考核题目回答信息
     * 
     * @param id 考核题目回答主键
     * @return 结果
     */
    public int deleteExaminationReplyById(Long id);
}

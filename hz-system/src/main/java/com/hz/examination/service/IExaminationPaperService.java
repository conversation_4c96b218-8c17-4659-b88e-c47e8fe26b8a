package com.hz.examination.service;

import java.util.List;
import java.util.Map;

import com.hz.examination.domain.ExaminationPaper;

/**
 * 考核Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface IExaminationPaperService 
{
    /**
     * 查询考核
     * 
     * @param id 考核主键
     * @return 考核
     */
    public ExaminationPaper selectExaminationPaperById(Long id);

    /**
     * 查询考核列表
     * 
     * @param examinationPaper 考核
     * @return 考核集合
     */
    public List<ExaminationPaper> selectExaminationPaperList(ExaminationPaper examinationPaper);

    public List<ExaminationPaper> selectExaminationPaperList(Long monthlyId);

    public List<ExaminationPaper> selectExaminationPaperListByTypeAndBrand(String type, String brand);

    /**
     * 新增考核
     * 
     * @param examinationPaper 考核
     * @return 结果
     */
    public int insertExaminationPaper(ExaminationPaper examinationPaper);

    public int batchInsertExaminationPaper(Map<Integer, Integer> monthlyMap, Long createId, String createBy);

    /**
     * 修改考核
     * 
     * @param examinationPaper 考核
     * @return 结果
     */
    public int updateExaminationPaper(ExaminationPaper examinationPaper);

    /**
     * 批量删除考核
     * 
     * @param ids 需要删除的考核主键集合
     * @return 结果
     */
    public int deleteExaminationPaperByIds(Long[] ids);

    /**
     * 删除考核信息
     * 
     * @param id 考核主键
     * @return 结果
     */
    public int deleteExaminationPaperById(Long id);

    public int deleteExaminationPaperByMonthlyId(Long monthlyId);

    /**
     * 根据季度考核ID统计考核数量
     * 
     * @param quarterlyAssessmentId 季度考核ID
     * @return 考核数量
     */
    public int countExaminationPaperByQuarterlyAssessmentId(String quarterlyAssessmentId);
}

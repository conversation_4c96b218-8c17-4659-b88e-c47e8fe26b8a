package com.hz.examination.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.hz.common.core.domain.entity.SysDictData;
import com.hz.common.utils.DateUtils;
import com.hz.common.utils.DictUtils;
import com.hz.examination.domain.ExaminationPaper;
import com.hz.examination.mapper.ExaminationPaperMapper;
import com.hz.examination.service.IExaminationPaperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 考核Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
public class ExaminationPaperServiceImpl implements IExaminationPaperService
{
    @Autowired
    private ExaminationPaperMapper examinationPaperMapper;

    /**
     * 查询考核
     * 
     * @param id 考核主键
     * @return 考核
     */
    @Override
    public ExaminationPaper selectExaminationPaperById(Long id)
    {
        return examinationPaperMapper.selectExaminationPaperById(id);
    }

    /**
     * 查询考核列表
     * 
     * @param examinationPaper 考核
     * @return 考核
     */
    @Override
    public List<ExaminationPaper> selectExaminationPaperList(ExaminationPaper examinationPaper)
    {
        return examinationPaperMapper.selectExaminationPaperList(examinationPaper);
    }

    @Override
    public List<ExaminationPaper> selectExaminationPaperList(Long monthlyId)
    {
        return examinationPaperMapper.selectExaminationPaperListByMonthlyId(monthlyId);
    }

    @Override
    public List<ExaminationPaper> selectExaminationPaperListByTypeAndBrand(String type, String brand)
    {
        return examinationPaperMapper.selectExaminationPaperListByTypeAndBrand(type, brand);
    }

    /**
     * 新增考核
     * 
     * @param examinationPaper 考核
     * @return 结果
     */
    @Override
    public int insertExaminationPaper(ExaminationPaper examinationPaper)
    {
        examinationPaper.setCreateTime(DateUtils.getNowDate());
        return examinationPaperMapper.insertExaminationPaper(examinationPaper);
    }

    @Override
    public int batchInsertExaminationPaper(Map<Integer, Integer> monthlyMap, Long createId, String createBy)
    {
        if(monthlyMap.size()!=3){
            return 0;
        }
        List<SysDictData> zdlx = DictUtils.getDictCache("merchant_zdlx");
        List<SysDictData> khlx = DictUtils.getDictCache("merchant_khlx");
        List<ExaminationPaper> examinationPapers = new ArrayList<>();
        Date nowDate = DateUtils.getNowDate();
        monthlyMap.forEach((kk,vv)->{
            final Long[] sort = {1l};
            zdlx.forEach(z ->
                    khlx.forEach(k -> {
                        ExaminationPaper examinationPaper = new ExaminationPaper();
                        examinationPaper.setMonthlyId(vv.longValue());
                        examinationPaper.setName(kk+"月"+k.getDictLabel()+"现场考核项");
                        examinationPaper.setBrand(z.getDictValue());
                        examinationPaper.setBrandName(z.getDictLabel());
                        examinationPaper.setType(k.getDictValue());
                        examinationPaper.setTypeName(k.getDictLabel());
                        examinationPaper.setSort(sort[0]);
                        sort[0] +=1;
                        examinationPaper.setCreateBy(createBy);
                        examinationPaper.setCreateId(createId);
                        examinationPaper.setCreateTime(nowDate);
                        examinationPapers.add(examinationPaper);
                    })
            );
        });
        if(examinationPapers.size()>0){
            return examinationPaperMapper.batchInsertExaminationPaper(examinationPapers);
        }else{
            return 0;
        }
    }

    /**
     * 修改考核
     * 
     * @param examinationPaper 考核
     * @return 结果
     */
    @Override
    public int updateExaminationPaper(ExaminationPaper examinationPaper)
    {
        examinationPaper.setUpdateTime(DateUtils.getNowDate());
        return examinationPaperMapper.updateExaminationPaper(examinationPaper);
    }

    /**
     * 批量删除考核
     * 
     * @param ids 需要删除的考核主键
     * @return 结果
     */
    @Override
    public int deleteExaminationPaperByIds(Long[] ids)
    {
        return examinationPaperMapper.deleteExaminationPaperByIds(ids);
    }

    /**
     * 删除考核信息
     * 
     * @param id 考核主键
     * @return 结果
     */
    @Override
    public int deleteExaminationPaperById(Long id)
    {
        return examinationPaperMapper.deleteExaminationPaperById(id);
    }

    @Override
    public int deleteExaminationPaperByMonthlyId(Long monthlyId)
    {
        return examinationPaperMapper.deleteExaminationPaperByMonthlyId(monthlyId);
    }

    @Override
    public int countExaminationPaperByQuarterlyAssessmentId(String quarterlyAssessmentId)
    {
        return examinationPaperMapper.countExaminationPaperByQuarterlyAssessmentId(quarterlyAssessmentId);
    }
}

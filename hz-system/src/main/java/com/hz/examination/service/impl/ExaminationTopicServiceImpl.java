package com.hz.examination.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import com.hz.common.utils.DateUtils;
import com.hz.examination.domain.ExaminationReply;
import com.hz.examination.domain.ExaminationReplyMerchant;
import com.hz.examination.domain.ExaminationTopic;
import com.hz.examination.mapper.ExaminationReplyMapper;
import com.hz.examination.mapper.ExaminationReplyMerchantMapper;
import com.hz.examination.mapper.ExaminationTopicMapper;
import com.hz.examination.service.IExaminationTopicService;
import com.hz.quarterlyassessments.mapper.MonthlyAssessmentMerchantMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 考核题目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
public class ExaminationTopicServiceImpl implements IExaminationTopicService
{
    @Autowired
    private ExaminationTopicMapper examinationTopicMapper;

    @Autowired
    private ExaminationReplyMapper examinationReplyMapper;

    @Autowired
    private ExaminationReplyMerchantMapper examinationReplyMerchantMapper;

    @Autowired
    private MonthlyAssessmentMerchantMapper monthlyAssessmentMerchantMapper;

    /**
     * 查询考核题目
     * 
     * @param id 考核题目主键
     * @return 考核题目
     */
    @Override
    public ExaminationTopic selectExaminationTopicById(Long id)
    {
        return examinationTopicMapper.selectExaminationTopicById(id);
    }

    @Override
    public List<ExaminationTopic> selectWxExaminationTopicByPaperId(Long paperId, Long merchantId){
        int c = examinationReplyMerchantMapper.selectExaminationReplyMerchantByPaperId(paperId, merchantId);
        if(c>0){
            return examinationTopicMapper.selectExaminationTopicOfMerchantByPaperId(paperId,merchantId);
        }else{
            return examinationTopicMapper.selectExaminationTopicByPaperId(paperId);
        }
    }

    @Override
    public List<ExaminationTopic> selectExaminationTopicByPaperId(Long paperId){
        return examinationTopicMapper.selectExaminationTopicByPaperId(paperId);
    }

    @Override
    public List<ExaminationTopic> selectExaminationTopicOfMerchantByPaperId(Long paperId, Long merchantId){
        return examinationTopicMapper.selectExaminationTopicOfMerchantByPaperId(paperId,merchantId);
    }

    /**
     * 查询考核题目列表
     * 
     * @param examinationTopic 考核题目
     * @return 考核题目
     */
    @Override
    public List<ExaminationTopic> selectExaminationTopicList(ExaminationTopic examinationTopic)
    {
        return examinationTopicMapper.selectExaminationTopicList(examinationTopic);
    }

    /**
     * 新增考核题目
     * 
     * @param examinationTopic 考核题目
     * @return 结果
     */
    @Override
    public int insertExaminationTopic(ExaminationTopic examinationTopic)
    {
        examinationTopic.setCreateTime(DateUtils.getNowDate());
        return examinationTopicMapper.insertExaminationTopic(examinationTopic);
    }

    @Override
    public int batchInsertExaminationTopic(List<ExaminationTopic> examinationTopics, Long paperId, Long createId, String createBy)
    {
        examinationReplyMapper.batchDeleteExaminationReplyById(paperId);
        examinationTopicMapper.batchDeleteExaminationTopicById(paperId);
        Date createTime = DateUtils.getNowDate();
        List<ExaminationReply> examinationReplyList = new ArrayList<>();
        examinationTopics.forEach(examinationTopic -> {
            examinationTopic.setPaperId(paperId);
            examinationTopic.setCreateId(createId);
            examinationTopic.setCreateBy(createBy);
            examinationTopic.setCreateTime(createTime);
            examinationTopic.getExaminationReplyList().forEach(examinationReply -> {
                examinationReply.setPaperId(paperId);
                examinationReply.setTopicNum(examinationTopic.getNum());
                examinationReply.setCreateId(createId);
                examinationReply.setCreateBy(createBy);
                examinationReply.setCreateTime(createTime);
                examinationReplyList.add(examinationReply);
            });
        });
        examinationReplyMapper.batchInsertExaminationReply(examinationReplyList);
        return examinationTopicMapper.batchInsertExaminationTopic(examinationTopics);
    }

    @Override
    public int batchInsertExaminationTopicByWx(List<ExaminationTopic> examinationTopics, Long monthlyId, Long paperId, Long merchantId, Long createId, String createBy, boolean isMonopoly)
    {
        examinationReplyMerchantMapper.batchDeleteExaminationReplyMerchantById(paperId, merchantId);
        Date createTime = DateUtils.getNowDate();
        List<ExaminationReplyMerchant> examinationReplyMerchantList = new ArrayList<>();
        examinationTopics.forEach(examinationTopic -> {
            examinationTopic.getExaminationReplyMerchantList().forEach(examinationReplyMerchant -> {
                examinationReplyMerchant.setPaperId(paperId);
                examinationReplyMerchant.setMerchantId(merchantId);
                examinationReplyMerchant.setCreateId(createId);
                examinationReplyMerchant.setCreateBy(createBy);
                examinationReplyMerchant.setCreateTime(createTime);
                examinationReplyMerchantList.add(examinationReplyMerchant);
            });
        });
        examinationReplyMerchantMapper.batchInsertExaminationReplyMerchant(examinationReplyMerchantList);
        // 根据角色更新对应的状态
        if (isMonopoly) {
            return monthlyAssessmentMerchantMapper.updateZhuanmaiCheckStatus(monthlyId, "2", null, null, createId, createBy, createTime);
        } else {
            return monthlyAssessmentMerchantMapper.updateYingxiaoCheckStatus(monthlyId, "2", null, null, createId, createBy, createTime);
        }
    }

    @Override
    public int batchInsertExaminationTopicByWeb(List<ExaminationTopic> examinationTopics, Long paperId, Long merchantId, Long createId, String createBy)
    {
        examinationReplyMerchantMapper.batchDeleteExaminationReplyMerchantById(paperId, merchantId);
        Date createTime = DateUtils.getNowDate();
        List<ExaminationReplyMerchant> examinationReplyMerchantList = new ArrayList<>();
        examinationTopics.forEach(examinationTopic -> {
            examinationTopic.getExaminationReplyMerchantList().forEach(examinationReplyMerchant -> {
                examinationReplyMerchant.setPaperId(paperId);
                examinationReplyMerchant.setMerchantId(merchantId);
                examinationReplyMerchant.setCreateId(createId);
                examinationReplyMerchant.setCreateBy(createBy);
                examinationReplyMerchant.setCreateTime(createTime);
                examinationReplyMerchantList.add(examinationReplyMerchant);
            });
        });
        return  examinationReplyMerchantMapper.batchInsertExaminationReplyMerchant(examinationReplyMerchantList);
    }

    /**
     * 修改考核题目
     * 
     * @param examinationTopic 考核题目
     * @return 结果
     */
    @Override
    public int updateExaminationTopic(ExaminationTopic examinationTopic)
    {
        return examinationTopicMapper.updateExaminationTopic(examinationTopic);
    }

    /**
     * 批量删除考核题目
     * 
     * @param ids 需要删除的考核题目主键
     * @return 结果
     */
    @Override
    public int deleteExaminationTopicByIds(Long[] ids)
    {
        return examinationTopicMapper.deleteExaminationTopicByIds(ids);
    }

    /**
     * 删除考核题目信息
     * 
     * @param id 考核题目主键
     * @return 结果
     */
    @Override
    public int deleteExaminationTopicById(Long id)
    {
        return examinationTopicMapper.deleteExaminationTopicById(id);
    }
}

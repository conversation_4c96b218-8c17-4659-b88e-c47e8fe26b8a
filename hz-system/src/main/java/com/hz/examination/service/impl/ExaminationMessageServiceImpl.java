package com.hz.examination.service.impl;

import java.util.List;
import com.hz.common.utils.DateUtils;
import com.hz.examination.domain.ExaminationMessage;
import com.hz.examination.mapper.ExaminationMessageMapper;
import com.hz.examination.service.IExaminationMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 考核消息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
public class ExaminationMessageServiceImpl implements IExaminationMessageService
{
    @Autowired
    private ExaminationMessageMapper examinationMessageMapper;

    /**
     * 查询考核消息
     * 
     * @param msgId 考核消息主键
     * @return 考核消息
     */
    @Override
    public ExaminationMessage selectExaminationMessageByMsgId(Long msgId)
    {
        return examinationMessageMapper.selectExaminationMessageByMsgId(msgId);
    }

    /**
     * 查询考核消息列表
     * 
     * @param examinationMessage 考核消息
     * @return 考核消息
     */
    @Override
    public List<ExaminationMessage> selectExaminationMessageList(ExaminationMessage examinationMessage)
    {
        return examinationMessageMapper.selectExaminationMessageList(examinationMessage);
    }

    @Override
    public int getCountUnRead(Long createId)
    {
        return examinationMessageMapper.getCountUnRead(createId);
    }

    /**
     * 新增考核消息
     * 
     * @param examinationMessage 考核消息
     * @return 结果
     */
    @Override
    public int insertExaminationMessage(ExaminationMessage examinationMessage)
    {
        examinationMessage.setCreateTime(DateUtils.getNowDate());
        return examinationMessageMapper.insertExaminationMessage(examinationMessage);
    }

    /**
     * 修改考核消息
     * 
     * @param examinationMessage 考核消息
     * @return 结果
     */
    @Override
    public int updateExaminationMessage(ExaminationMessage examinationMessage)
    {
        examinationMessage.setUpdateTime(DateUtils.getNowDate());
        return examinationMessageMapper.updateExaminationMessage(examinationMessage);
    }

    /**
     * 批量删除考核消息
     * 
     * @param msgIds 需要删除的考核消息主键
     * @return 结果
     */
    @Override
    public int deleteExaminationMessageByMsgIds(Long[] msgIds)
    {
        return examinationMessageMapper.deleteExaminationMessageByMsgIds(msgIds);
    }

    /**
     * 删除考核消息信息
     * 
     * @param msgId 考核消息主键
     * @return 结果
     */
    @Override
    public int deleteExaminationMessageByMsgId(Long msgId)
    {
        return examinationMessageMapper.deleteExaminationMessageByMsgId(msgId);
    }
}

package com.hz.examination.service.impl;

import java.util.List;
import com.hz.common.utils.DateUtils;
import com.hz.examination.domain.ExaminationReply;
import com.hz.examination.mapper.ExaminationReplyMapper;
import com.hz.examination.service.IExaminationReplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 考核题目回答Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
public class ExaminationReplyServiceImpl implements IExaminationReplyService
{
    @Autowired
    private ExaminationReplyMapper examinationReplyMapper;

    /**
     * 查询考核题目回答
     * 
     * @param id 考核题目回答主键
     * @return 考核题目回答
     */
    @Override
    public ExaminationReply selectExaminationReplyById(Long id)
    {
        return examinationReplyMapper.selectExaminationReplyById(id);
    }

    /**
     * 查询考核题目回答列表
     * 
     * @param examinationReply 考核题目回答
     * @return 考核题目回答
     */
    @Override
    public List<ExaminationReply> selectExaminationReplyList(ExaminationReply examinationReply)
    {
        return examinationReplyMapper.selectExaminationReplyList(examinationReply);
    }

    /**
     * 新增考核题目回答
     * 
     * @param examinationReply 考核题目回答
     * @return 结果
     */
    @Override
    public int insertExaminationReply(ExaminationReply examinationReply)
    {
        examinationReply.setCreateTime(DateUtils.getNowDate());
        return examinationReplyMapper.insertExaminationReply(examinationReply);
    }

    /**
     * 修改考核题目回答
     * 
     * @param examinationReply 考核题目回答
     * @return 结果
     */
    @Override
    public int updateExaminationReply(ExaminationReply examinationReply)
    {
        return examinationReplyMapper.updateExaminationReply(examinationReply);
    }

    /**
     * 批量删除考核题目回答
     * 
     * @param ids 需要删除的考核题目回答主键
     * @return 结果
     */
    @Override
    public int deleteExaminationReplyByIds(Long[] ids)
    {
        return examinationReplyMapper.deleteExaminationReplyByIds(ids);
    }

    /**
     * 删除考核题目回答信息
     * 
     * @param id 考核题目回答主键
     * @return 结果
     */
    @Override
    public int deleteExaminationReplyById(Long id)
    {
        return examinationReplyMapper.deleteExaminationReplyById(id);
    }
}

package com.hz.examination.service.impl;

import java.util.List;
import com.hz.common.utils.DateUtils;
import com.hz.examination.domain.ExaminationReplyMerchant;
import com.hz.examination.mapper.ExaminationReplyMerchantMapper;
import com.hz.examination.service.IExaminationReplyMerchantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 考核题目商户回答Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
public class ExaminationReplyMerchantServiceImpl implements IExaminationReplyMerchantService
{
    @Autowired
    private ExaminationReplyMerchantMapper examinationReplyMerchantMapper;

    /**
     * 查询考核题目商户回答
     * 
     * @param id 考核题目商户回答主键
     * @return 考核题目商户回答
     */
    @Override
    public ExaminationReplyMerchant selectExaminationReplyMerchantById(Long id)
    {
        return examinationReplyMerchantMapper.selectExaminationReplyMerchantById(id);
    }

    /**
     * 查询考核题目商户回答列表
     * 
     * @param examinationReplyMerchant 考核题目商户回答
     * @return 考核题目商户回答
     */
    @Override
    public List<ExaminationReplyMerchant> selectExaminationReplyMerchantList(ExaminationReplyMerchant examinationReplyMerchant)
    {
        return examinationReplyMerchantMapper.selectExaminationReplyMerchantList(examinationReplyMerchant);
    }

    /**
     * 新增考核题目商户回答
     * 
     * @param examinationReplyMerchant 考核题目商户回答
     * @return 结果
     */
    @Override
    public int insertExaminationReplyMerchant(ExaminationReplyMerchant examinationReplyMerchant)
    {
        examinationReplyMerchant.setCreateTime(DateUtils.getNowDate());
        return examinationReplyMerchantMapper.insertExaminationReplyMerchant(examinationReplyMerchant);
    }

    /**
     * 修改考核题目商户回答
     * 
     * @param examinationReplyMerchant 考核题目商户回答
     * @return 结果
     */
    @Override
    public int updateExaminationReplyMerchant(ExaminationReplyMerchant examinationReplyMerchant)
    {
        return examinationReplyMerchantMapper.updateExaminationReplyMerchant(examinationReplyMerchant);
    }

    /**
     * 批量删除考核题目商户回答
     * 
     * @param ids 需要删除的考核题目商户回答主键
     * @return 结果
     */
    @Override
    public int deleteExaminationReplyMerchantByIds(Long[] ids)
    {
        return examinationReplyMerchantMapper.deleteExaminationReplyMerchantByIds(ids);
    }

    /**
     * 删除考核题目商户回答信息
     * 
     * @param id 考核题目商户回答主键
     * @return 结果
     */
    @Override
    public int deleteExaminationReplyMerchantById(Long id)
    {
        return examinationReplyMerchantMapper.deleteExaminationReplyMerchantById(id);
    }
}

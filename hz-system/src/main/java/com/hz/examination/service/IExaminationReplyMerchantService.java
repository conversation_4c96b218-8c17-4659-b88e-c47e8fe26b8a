package com.hz.examination.service;

import java.util.List;

import com.hz.examination.domain.ExaminationReplyMerchant;

/**
 * 考核题目商户回答Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface IExaminationReplyMerchantService 
{
    /**
     * 查询考核题目商户回答
     * 
     * @param id 考核题目商户回答主键
     * @return 考核题目商户回答
     */
    public ExaminationReplyMerchant selectExaminationReplyMerchantById(Long id);

    /**
     * 查询考核题目商户回答列表
     * 
     * @param examinationReplyMerchant 考核题目商户回答
     * @return 考核题目商户回答集合
     */
    public List<ExaminationReplyMerchant> selectExaminationReplyMerchantList(ExaminationReplyMerchant examinationReplyMerchant);

    /**
     * 新增考核题目商户回答
     * 
     * @param examinationReplyMerchant 考核题目商户回答
     * @return 结果
     */
    public int insertExaminationReplyMerchant(ExaminationReplyMerchant examinationReplyMerchant);

    /**
     * 修改考核题目商户回答
     * 
     * @param examinationReplyMerchant 考核题目商户回答
     * @return 结果
     */
    public int updateExaminationReplyMerchant(ExaminationReplyMerchant examinationReplyMerchant);

    /**
     * 批量删除考核题目商户回答
     * 
     * @param ids 需要删除的考核题目商户回答主键集合
     * @return 结果
     */
    public int deleteExaminationReplyMerchantByIds(Long[] ids);

    /**
     * 删除考核题目商户回答信息
     * 
     * @param id 考核题目商户回答主键
     * @return 结果
     */
    public int deleteExaminationReplyMerchantById(Long id);
}

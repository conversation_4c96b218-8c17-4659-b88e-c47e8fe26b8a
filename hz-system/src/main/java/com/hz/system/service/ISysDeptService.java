package com.hz.system.service;

import java.util.List;
import com.hz.common.core.domain.TreeSelect;
import com.hz.common.core.domain.entity.SysDept;

/**
 * 地区管理 服务层
 * 
 * <AUTHOR>
 */
public interface ISysDeptService
{
    /**
     * 查询地区管理数据
     * 
     * @param dept 地区信息
     * @return 地区信息集合
     */
    public List<SysDept> selectDeptList(SysDept dept);

    /**
     * 查询地区树结构信息
     * 
     * @param dept 地区信息
     * @return 地区树信息集合
     */
    public List<TreeSelect> selectDeptTreeList(SysDept dept);

    /**
     * 构建前端所需要树结构
     * 
     * @param depts 地区列表
     * @return 树结构列表
     */
    public List<SysDept> buildDeptTree(List<SysDept> depts);

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param depts 地区列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts);

    /**
     * 根据角色ID查询地区树信息
     * 
     * @param roleId 角色ID
     * @return 选中地区列表
     */
    public List<Long> selectDeptListByRoleId(Long roleId);

    /**
     * 根据地区ID查询信息
     * 
     * @param deptId 地区ID
     * @return 地区信息
     */
    public SysDept selectDeptById(Long deptId);

    /**
     * 根据ID查询所有子地区（正常状态）
     * 
     * @param deptId 地区ID
     * @return 子地区数
     */
    public int selectNormalChildrenDeptById(Long deptId);

    /**
     * 是否存在地区子节点
     * 
     * @param deptId 地区ID
     * @return 结果
     */
    public boolean hasChildByDeptId(Long deptId);

    /**
     * 查询地区是否存在用户
     * 
     * @param deptId 地区ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean checkDeptExistUser(Long deptId);

    /**
     * 校验地区名称是否唯一
     * 
     * @param dept 地区信息
     * @return 结果
     */
    public boolean checkDeptNameUnique(SysDept dept);

    /**
     * 校验地区别名是否唯一
     *
     * @param deptNameAlias 地区别名
     * @return 结果
     */
    public boolean checkDeptNameAliasUnique(String deptNameAlias);

    /**
     * 校验地区是否有数据权限
     * 
     * @param deptId 地区id
     */
    public void checkDeptDataScope(Long deptId);

    /**
     * 新增保存地区信息
     * 
     * @param dept 地区信息
     * @return 结果
     */
    public int insertDept(SysDept dept);

    /**
     * 修改保存地区信息
     * 
     * @param dept 地区信息
     * @return 结果
     */
    public int updateDept(SysDept dept);

    /**
     * 删除地区管理信息
     * 
     * @param deptId 地区ID
     * @return 结果
     */
    public int deleteDeptById(Long deptId);
}

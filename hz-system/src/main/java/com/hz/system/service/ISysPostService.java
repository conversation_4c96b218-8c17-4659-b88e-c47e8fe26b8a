package com.hz.system.service;

import java.util.List;
import com.hz.system.domain.SysPost;

/**
 * 处室信息 服务层
 * 
 * <AUTHOR>
 */
public interface ISysPostService
{
    /**
     * 查询处室信息集合
     * 
     * @param post 处室信息
     * @return 处室列表
     */
    public List<SysPost> selectPostList(SysPost post);

    /**
     * 查询所有处室
     * 
     * @return 处室列表
     */
    public List<SysPost> selectPostAll();

    /**
     * 通过处室ID查询处室信息
     * 
     * @param postId 处室ID
     * @return 处室信息
     */
    public SysPost selectPostById(Long postId);

    /**
     * 根据用户ID获取处室选择框列表
     * 
     * @param userId 用户ID
     * @return 选中处室ID列表
     */
    public List<Long> selectPostListByUserId(Long userId);

    /**
     * 校验处室名称
     * 
     * @param post 处室信息
     * @return 结果
     */
    public boolean checkPostNameUnique(SysPost post);

    /**
     * 校验处室编码
     * 
     * @param post 处室信息
     * @return 结果
     */
    public boolean checkPostCodeUnique(SysPost post);

    /**
     * 通过处室ID查询处室使用数量
     * 
     * @param postId 处室ID
     * @return 结果
     */
    public int countUserPostById(Long postId);

    /**
     * 删除处室信息
     * 
     * @param postId 处室ID
     * @return 结果
     */
    public int deletePostById(Long postId);

    /**
     * 批量删除处室信息
     * 
     * @param postIds 需要删除的处室ID
     * @return 结果
     */
    public int deletePostByIds(Long[] postIds);

    /**
     * 新增保存处室信息
     * 
     * @param post 处室信息
     * @return 结果
     */
    public int insertPost(SysPost post);

    /**
     * 修改保存处室信息
     * 
     * @param post 处室信息
     * @return 结果
     */
    public int updatePost(SysPost post);
}

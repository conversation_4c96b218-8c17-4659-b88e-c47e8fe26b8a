package com.hz.system.mapper;

import java.util.List;
import com.hz.system.domain.SysRoleDept;

/**
 * 角色与地区关联表 数据层
 * 
 * <AUTHOR>
 */
public interface SysRoleDeptMapper
{
    /**
     * 通过角色ID删除角色和地区关联
     * 
     * @param roleId 角色ID
     * @return 结果
     */
    public int deleteRoleDeptByRoleId(Long roleId);

    /**
     * 批量删除角色地区关联信息
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteRoleDept(Long[] ids);

    /**
     * 查询地区使用数量
     * 
     * @param deptId 地区ID
     * @return 结果
     */
    public int selectCountRoleDeptByDeptId(Long deptId);

    /**
     * 批量新增角色地区信息
     * 
     * @param roleDeptList 角色地区列表
     * @return 结果
     */
    public int batchRoleDept(List<SysRoleDept> roleDeptList);
}

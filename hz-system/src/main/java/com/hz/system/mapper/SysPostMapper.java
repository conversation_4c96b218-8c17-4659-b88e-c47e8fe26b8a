package com.hz.system.mapper;

import java.util.List;
import com.hz.system.domain.SysPost;

/**
 * 处室信息 数据层
 * 
 * <AUTHOR>
 */
public interface SysPostMapper
{
    /**
     * 查询处室数据集合
     * 
     * @param post 处室信息
     * @return 处室数据集合
     */
    public List<SysPost> selectPostList(SysPost post);

    /**
     * 查询所有处室
     * 
     * @return 处室列表
     */
    public List<SysPost> selectPostAll();

    /**
     * 通过处室ID查询处室信息
     * 
     * @param postId 处室ID
     * @return 角色对象信息
     */
    public SysPost selectPostById(Long postId);

    /**
     * 根据用户ID获取处室选择框列表
     * 
     * @param userId 用户ID
     * @return 选中处室ID列表
     */
    public List<Long> selectPostListByUserId(Long userId);

    /**
     * 查询用户所属处室组
     * 
     * @param userName 用户名
     * @return 结果
     */
    public List<SysPost> selectPostsByUserName(String userName);

    /**
     * 删除处室信息
     * 
     * @param postId 处室ID
     * @return 结果
     */
    public int deletePostById(Long postId);

    /**
     * 批量删除处室信息
     * 
     * @param postIds 需要删除的处室ID
     * @return 结果
     */
    public int deletePostByIds(Long[] postIds);

    /**
     * 修改处室信息
     * 
     * @param post 处室信息
     * @return 结果
     */
    public int updatePost(SysPost post);

    /**
     * 新增处室信息
     * 
     * @param post 处室信息
     * @return 结果
     */
    public int insertPost(SysPost post);

    /**
     * 校验处室名称
     * 
     * @param postName 处室名称
     * @return 结果
     */
    public SysPost checkPostNameUnique(String postName);

    /**
     * 校验处室编码
     * 
     * @param postCode 处室编码
     * @return 结果
     */
    public SysPost checkPostCodeUnique(String postCode);
}

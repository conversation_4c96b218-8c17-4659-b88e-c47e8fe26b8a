package com.hz.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.hz.common.core.domain.entity.SysDept;

/**
 * 地区管理 数据层
 * 
 * <AUTHOR>
 */
public interface SysDeptMapper
{
    /**
     * 查询地区管理数据
     * 
     * @param dept 地区信息
     * @return 地区信息集合
     */
    public List<SysDept> selectDeptList(SysDept dept);

    /**
     * 根据角色ID查询地区树信息
     * 
     * @param roleId 角色ID
     * @param deptCheckStrictly 地区树选择项是否关联显示
     * @return 选中地区列表
     */
    public List<Long> selectDeptListByRoleId(@Param("roleId") Long roleId, @Param("deptCheckStrictly") boolean deptCheckStrictly);

    /**
     * 根据地区ID查询信息
     * 
     * @param deptId 地区ID
     * @return 地区信息
     */
    public SysDept selectDeptById(Long deptId);

    /**
     * 根据ID查询所有子地区
     * 
     * @param deptId 地区ID
     * @return 地区列表
     */
    public List<SysDept> selectChildrenDeptById(Long deptId);

    /**
     * 根据ID查询所有子地区（正常状态）
     * 
     * @param deptId 地区ID
     * @return 子地区数
     */
    public int selectNormalChildrenDeptById(Long deptId);

    /**
     * 是否存在子节点
     * 
     * @param deptId 地区ID
     * @return 结果
     */
    public int hasChildByDeptId(Long deptId);

    /**
     * 查询地区是否存在用户
     * 
     * @param deptId 地区ID
     * @return 结果
     */
    public int checkDeptExistUser(Long deptId);

    /**
     * 校验地区名称是否唯一
     * 
     * @param deptName 地区名称
     * @param parentId 父地区ID
     * @return 结果
     */
    public SysDept checkDeptNameUnique(@Param("deptName") String deptName, @Param("parentId") Long parentId);

    /**
     * 校验地区别名是否唯一
     *
     * @param deptNameAlias 地区别名
     * @return 结果
     */
    public int checkDeptNameAliasUnique(String deptNameAlias);

    /**
     * 新增地区信息
     * 
     * @param dept 地区信息
     * @return 结果
     */
    public int insertDept(SysDept dept);

    /**
     * 修改地区信息
     * 
     * @param dept 地区信息
     * @return 结果
     */
    public int updateDept(SysDept dept);

    /**
     * 修改所在地区正常状态
     * 
     * @param deptIds 地区ID组
     */
    public void updateDeptStatusNormal(Long[] deptIds);

    /**
     * 修改子元素关系
     * 
     * @param depts 子元素
     * @return 结果
     */
    public int updateDeptChildren(@Param("depts") List<SysDept> depts);

    /**
     * 删除地区管理信息
     * 
     * @param deptId 地区ID
     * @return 结果
     */
    public int deleteDeptById(Long deptId);
}

package com.hz.system.mapper;

import java.util.List;
import com.hz.system.domain.SysUserPost;

/**
 * 用户与处室关联表 数据层
 * 
 * <AUTHOR>
 */
public interface SysUserPostMapper
{
    /**
     * 通过用户ID删除用户和处室关联
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteUserPostByUserId(Long userId);

    /**
     * 通过处室ID查询处室使用数量
     * 
     * @param postId 处室ID
     * @return 结果
     */
    public int countUserPostById(Long postId);

    /**
     * 批量删除用户和处室关联
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteUserPost(Long[] ids);

    /**
     * 批量新增用户处室信息
     * 
     * @param userPostList 用户处室列表
     * @return 结果
     */
    public int batchUserPost(List<SysUserPost> userPostList);
}

package com.hz.quarterlyassessments.domain.dto;

import com.hz.examination.domain.ExaminationPaper;

import java.util.List;

/**
 * 月度商户考核
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
public class MonthlyMerchantAssessmentDto
{
    /** 商户ID */
    private Long merchantId;
    
    /** 商户名称 */
    private String merchantName;
    
    /** 商户类型 */
    private String selectionType;
    
    /** 法人姓名 */
    private String legalName;
    
    /** 联系电话 */
    private String phoneNumber;
    
    /** 县区 */
    private String county;
    
    /** 开店状态 */
    private String assessmentStatus;
    
    /** 专卖签到签退状态 */
    private String zhuanmaiCheckStatus;
    
    /** 营销签到签退状态 */
    private String yingxiaoCheckStatus;

    /** 商户照片 */
    private String photo;

    /** 经营地址 */
    private String businessAddress;

    /** 备注 */
    private String remark;

    /** 终端层级 */
    private String zhongduancengji;

    private List<ExaminationPaper> examinationPaperList;

    public MonthlyMerchantAssessmentDto() {
    }

    public List<ExaminationPaper> getExaminationPaperList() {
        return examinationPaperList;
    }

    public void setExaminationPaperList(List<ExaminationPaper> examinationPaperList) {
        this.examinationPaperList = examinationPaperList;
    }

    // Getters and Setters
    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getSelectionType() {
        return selectionType;
    }

    public void setSelectionType(String selectionType) {
        this.selectionType = selectionType;
    }

    public String getLegalName() {
        return legalName;
    }

    public void setLegalName(String legalName) {
        this.legalName = legalName;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getAssessmentStatus() {
        return assessmentStatus;
    }

    public void setAssessmentStatus(String assessmentStatus) {
        this.assessmentStatus = assessmentStatus;
    }

    public String getZhuanmaiCheckStatus() {
        return zhuanmaiCheckStatus;
    }

    public void setZhuanmaiCheckStatus(String zhuanmaiCheckStatus) {
        this.zhuanmaiCheckStatus = zhuanmaiCheckStatus;
    }

    public String getYingxiaoCheckStatus() {
        return yingxiaoCheckStatus;
    }

    public void setYingxiaoCheckStatus(String yingxiaoCheckStatus) {
        this.yingxiaoCheckStatus = yingxiaoCheckStatus;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getBusinessAddress() {
        return businessAddress;
    }

    public void setBusinessAddress(String businessAddress) {
        this.businessAddress = businessAddress;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getZhongduancengji() {
        return zhongduancengji;
    }

    public void setZhongduancengji(String zhongduancengji) {
        this.zhongduancengji = zhongduancengji;
    }
} 
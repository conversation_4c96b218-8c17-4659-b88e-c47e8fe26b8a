package com.hz.quarterlyassessments.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hz.common.core.domain.BaseEntity;
import com.hz.examination.domain.ExaminationPaper;
import com.hz.merchant.domain.MerchantInfo;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hz.common.annotation.Excel;

/**
 * 月度考核商户关联对象 monthly_assessment_merchants
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
public class MonthlyAssessmentMerchant extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 月度考核ID */
    @Excel(name = "月度考核ID")
    private Long monthlyAssessmentId;

    /** 商户ID */
    @Excel(name = "商户ID")
    private Long merchantId;

    /** 商户类型  0考核商户，1自主添加商户，2自主添加无证户*/
    @Excel(name = "商户类型", dictType = "merchant_type")
    private String selectionType;

    /** 抽取顺序 */
    @Excel(name = "抽取顺序")
    private Integer selectionOrder;

    /** 到下一个商户的距离(公里) */
    @Excel(name = "到下一个商户的距离")
    private BigDecimal distanceToNext;

    /** 开店状态：0-正常，1-已闭店*/
    @Excel(name = "开店状态", dictType = "assessment_status")
    private String assessmentStatus;

    /** 考核得分 */
    @Excel(name = "考核得分")
    private BigDecimal assessmentScore;

    /** 考核日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "考核日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date assessmentDate;

    /** 考核人员ID */
    @Excel(name = "考核人员ID")
    private Long assessorId;

    /** 商户信息（关联查询） */
    private MerchantInfo merchantInfo;

    /** 考核人员姓名（关联查询） */
    private String assessorName;

    /** 商户类型标签（用于前端显示） */
    private String selectionTypeLabel;

    /** 专卖签到签退状态：0-未签到，1-已签到，2-已提交表单，3-已签退 */
    @Excel(name = "专卖签到签退状态", dictType = "zhuanmai_check_status")
    private String zhuanmaiCheckStatus;

    /** 专卖签到时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "专卖签到时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date zhuanmaiCheckInTime;

    /** 专卖签退时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "专卖签退时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date zhuanmaiCheckOutTime;

    /** 营销签到签退状态：0-未签到，1-已签到，2-已提交表单，3-已签退 */
    @Excel(name = "营销签到签退状态", dictType = "yingxiao_check_status")
    private String yingxiaoCheckStatus;

    /** 营销签到时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "营销签到时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date yingxiaoCheckInTime;

    /** 营销签退时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "营销签退时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date yingxiaoCheckOutTime;

    private List<ExaminationPaper> examinationPaperList;

    public List<ExaminationPaper> getExaminationPaperList() {
        return examinationPaperList;
    }

    public void setExaminationPaperList(List<ExaminationPaper> examinationPaperList) {
        this.examinationPaperList = examinationPaperList;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setMonthlyAssessmentId(Long monthlyAssessmentId) 
    {
        this.monthlyAssessmentId = monthlyAssessmentId;
    }

    public Long getMonthlyAssessmentId() 
    {
        return monthlyAssessmentId;
    }

    public void setMerchantId(Long merchantId) 
    {
        this.merchantId = merchantId;
    }

    public Long getMerchantId() 
    {
        return merchantId;
    }

    public void setSelectionType(String selectionType) 
    {
        this.selectionType = selectionType;
    }

    public String getSelectionType() 
    {
        return selectionType;
    }

    public void setSelectionOrder(Integer selectionOrder) 
    {
        this.selectionOrder = selectionOrder;
    }

    public Integer getSelectionOrder() 
    {
        return selectionOrder;
    }

    public void setDistanceToNext(BigDecimal distanceToNext) 
    {
        this.distanceToNext = distanceToNext;
    }

    public BigDecimal getDistanceToNext() 
    {
        return distanceToNext;
    }

    public void setAssessmentStatus(String assessmentStatus) 
    {
        this.assessmentStatus = assessmentStatus;
    }

    public String getAssessmentStatus() 
    {
        return assessmentStatus;
    }

    public void setAssessmentScore(BigDecimal assessmentScore) 
    {
        this.assessmentScore = assessmentScore;
    }

    public BigDecimal getAssessmentScore() 
    {
        return assessmentScore;
    }

    public void setAssessmentDate(Date assessmentDate) 
    {
        this.assessmentDate = assessmentDate;
    }

    public Date getAssessmentDate() 
    {
        return assessmentDate;
    }

    public void setAssessorId(Long assessorId) 
    {
        this.assessorId = assessorId;
    }

    public Long getAssessorId() 
    {
        return assessorId;
    }

    public MerchantInfo getMerchantInfo() 
    {
        return merchantInfo;
    }

    public void setMerchantInfo(MerchantInfo merchantInfo) 
    {
        this.merchantInfo = merchantInfo;
    }

    public String getAssessorName() 
    {
        return assessorName;
    }

    public void setAssessorName(String assessorName) 
    {
        this.assessorName = assessorName;
    }

    public String getSelectionTypeLabel() 
    {
        return selectionTypeLabel;
    }

    public void setSelectionTypeLabel(String selectionTypeLabel) 
    {
        this.selectionTypeLabel = selectionTypeLabel;
    }

    public String getZhuanmaiCheckStatus() 
    {
        return zhuanmaiCheckStatus;
    }

    public void setZhuanmaiCheckStatus(String zhuanmaiCheckStatus) 
    {
        this.zhuanmaiCheckStatus = zhuanmaiCheckStatus;
    }

    public Date getZhuanmaiCheckInTime() 
    {
        return zhuanmaiCheckInTime;
    }

    public void setZhuanmaiCheckInTime(Date zhuanmaiCheckInTime) 
    {
        this.zhuanmaiCheckInTime = zhuanmaiCheckInTime;
    }

    public Date getZhuanmaiCheckOutTime() 
    {
        return zhuanmaiCheckOutTime;
    }

    public void setZhuanmaiCheckOutTime(Date zhuanmaiCheckOutTime) 
    {
        this.zhuanmaiCheckOutTime = zhuanmaiCheckOutTime;
    }

    public String getYingxiaoCheckStatus() 
    {
        return yingxiaoCheckStatus;
    }

    public void setYingxiaoCheckStatus(String yingxiaoCheckStatus) 
    {
        this.yingxiaoCheckStatus = yingxiaoCheckStatus;
    }

    public Date getYingxiaoCheckInTime() 
    {
        return yingxiaoCheckInTime;
    }

    public void setYingxiaoCheckInTime(Date yingxiaoCheckInTime) 
    {
        this.yingxiaoCheckInTime = yingxiaoCheckInTime;
    }

    public Date getYingxiaoCheckOutTime() 
    {
        return yingxiaoCheckOutTime;
    }

    public void setYingxiaoCheckOutTime(Date yingxiaoCheckOutTime) 
    {
        this.yingxiaoCheckOutTime = yingxiaoCheckOutTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("monthlyAssessmentId", getMonthlyAssessmentId())
            .append("merchantId", getMerchantId())
            .append("selectionType", getSelectionType())
            .append("selectionOrder", getSelectionOrder())
            .append("distanceToNext", getDistanceToNext())
            .append("assessmentStatus", getAssessmentStatus())
            .append("assessmentScore", getAssessmentScore())
            .append("assessmentDate", getAssessmentDate())
            .append("assessorId", getAssessorId())
            .append("zhuanmaiCheckStatus", getZhuanmaiCheckStatus())
            .append("zhuanmaiCheckInTime", getZhuanmaiCheckInTime())
            .append("zhuanmaiCheckOutTime", getZhuanmaiCheckOutTime())
            .append("yingxiaoCheckStatus", getYingxiaoCheckStatus())
            .append("yingxiaoCheckInTime", getYingxiaoCheckInTime())
            .append("yingxiaoCheckOutTime", getYingxiaoCheckOutTime())
            .append("createId", getCreateId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateId", getUpdateId())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 
package com.hz.quarterlyassessments.domain.dto;

import com.hz.common.core.domain.entity.SysUser;
import java.util.List;

/**
 * 按角色更新人员请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
public class UpdateStaffByRoleRequest {
    
    /** 月度考核ID */
    private Long monthlyAssessmentId;
    
    /** 角色ID (104L-专卖人员, 105L-营销人员) */
    private Long roleId;
    
    /** 人员列表 */
    private List<SysUser> staffList;

    public Long getMonthlyAssessmentId() {
        return monthlyAssessmentId;
    }

    public void setMonthlyAssessmentId(Long monthlyAssessmentId) {
        this.monthlyAssessmentId = monthlyAssessmentId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public List<SysUser> getStaffList() {
        return staffList;
    }

    public void setStaffList(List<SysUser> staffList) {
        this.staffList = staffList;
    }
}

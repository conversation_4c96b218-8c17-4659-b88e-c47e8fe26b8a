package com.hz.quarterlyassessments.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hz.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hz.common.annotation.Excel;

/**
 * 月度考核人员抽取历史记录对象 monthly_assessment_history
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
public class MonthlyAssessmentHistory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 角色ID */
    @Excel(name = "角色ID")
    private Long roleId;

    /** 月度考核ID */
    @Excel(name = "月度考核ID")
    private Long monthlyAssessmentId;

    /** 年份 */
    @Excel(name = "年份")
    private String year;

    /** 半年标识：H1上半年，H2下半年 */
    @Excel(name = "半年标识")
    private String halfYear;

    /** 签字状态：0-未签字，1-已签字 */
    @Excel(name = "签字状态")
    private String signatureStatus;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setRoleId(Long roleId) 
    {
        this.roleId = roleId;
    }

    public Long getRoleId() 
    {
        return roleId;
    }

    public void setMonthlyAssessmentId(Long monthlyAssessmentId) 
    {
        this.monthlyAssessmentId = monthlyAssessmentId;
    }

    public Long getMonthlyAssessmentId() 
    {
        return monthlyAssessmentId;
    }

    public void setYear(String year) 
    {
        this.year = year;
    }

    public String getYear() 
    {
        return year;
    }

    public void setHalfYear(String halfYear) 
    {
        this.halfYear = halfYear;
    }

    public String getHalfYear() 
    {
        return halfYear;
    }

    public void setSignatureStatus(String signatureStatus) 
    {
        this.signatureStatus = signatureStatus;
    }

    public String getSignatureStatus() 
    {
        return signatureStatus;
    }

    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("roleId", getRoleId())
            .append("monthlyAssessmentId", getMonthlyAssessmentId())
            .append("year", getYear())
            .append("halfYear", getHalfYear())
            .append("signatureStatus", getSignatureStatus())
            .append("createdAt", getCreatedAt())
            .toString();
    }
} 
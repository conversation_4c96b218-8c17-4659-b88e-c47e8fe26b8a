package com.hz.quarterlyassessments.domain.dto;

import com.hz.merchant.domain.MerchantInfo;
import java.util.List;

/**
 * 保存选定商户请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
public class SaveSelectedMerchantsRequest {
    
    /** 月度考核ID */
    private Long monthlyAssessmentId;
    
    /** 选中的商户列表 */
    private List<MerchantInfo> selectedMerchants;
    
    /** 抽取类型 */
    private String selectionType;
    
    /** 总距离 */
    private Double totalDistance;

    public Long getMonthlyAssessmentId() {
        return monthlyAssessmentId;
    }

    public void setMonthlyAssessmentId(Long monthlyAssessmentId) {
        this.monthlyAssessmentId = monthlyAssessmentId;
    }

    public List<MerchantInfo> getSelectedMerchants() {
        return selectedMerchants;
    }

    public void setSelectedMerchants(List<MerchantInfo> selectedMerchants) {
        this.selectedMerchants = selectedMerchants;
    }

    public String getSelectionType() {
        return selectionType;
    }

    public void setSelectionType(String selectionType) {
        this.selectionType = selectionType;
    }

    public Double getTotalDistance() {
        return totalDistance;
    }

    public void setTotalDistance(Double totalDistance) {
        this.totalDistance = totalDistance;
    }
} 
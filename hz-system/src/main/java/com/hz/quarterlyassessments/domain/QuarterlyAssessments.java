package com.hz.quarterlyassessments.domain;

import java.util.List;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hz.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hz.common.annotation.Excel;

/**
 * 季度考核对象 quarterly_assessments
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public class QuarterlyAssessments extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private String id;

    /** 考核名称 */
    @Excel(name = "考核名称")
    private String name;

    /** 所属年份 */
    @Excel(name = "所属年份")
    private String year;

    /** 所属季度 */
    @Excel(name = "所属季度")
    private Long quarter;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 计划指定人 */
    @Excel(name = "计划指定人")
    private String plannerUserId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    /** 月度考核信息 */
    private List<MonthlyAssessments> monthlyAssessmentsList;

    /** 考核数量 */
    @Excel(name = "考核数量")
    private Integer assessmentCount;

    /** 状态文本描述 */
    private String statusText;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setYear(String year) 
    {
        this.year = year;
    }

    public String getYear() 
    {
        return year;
    }

    public void setQuarter(Long quarter) 
    {
        this.quarter = quarter;
    }

    public Long getQuarter() 
    {
        return quarter;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setPlannerUserId(String plannerUserId) 
    {
        this.plannerUserId = plannerUserId;
    }

    public String getPlannerUserId() 
    {
        return plannerUserId;
    }

    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }

    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }

    public List<MonthlyAssessments> getMonthlyAssessmentsList()
    {
        return monthlyAssessmentsList;
    }

    public void setMonthlyAssessmentsList(List<MonthlyAssessments> monthlyAssessmentsList)
    {
        this.monthlyAssessmentsList = monthlyAssessmentsList;
    }

    public Integer getAssessmentCount()
    {
        return assessmentCount;
    }

    public void setAssessmentCount(Integer assessmentCount)
    {
        this.assessmentCount = assessmentCount;
    }

    public String getStatusText()
    {
        return getStatusTextByCode(this.status);
    }

    public void setStatusText(String statusText)
    {
        this.statusText = statusText;
    }

    /**
     * 根据状态码获取状态文本描述
     * @param statusCode 状态码
     * @return 状态文本描述
     */
    private String getStatusTextByCode(String statusCode)
    {
        if (statusCode == null) {
            return "未知状态";
        }
        switch (statusCode) {
            case "0":
                return "已提交"; // 兼容旧数据，0-待开始 映射为 已提交
            case "1":
                return "已提交";
            case "2":
                return "进行中";
            case "3":
                return "未完成";
            case "4":
                return "已完成";
            default:
                return "未知状态";
        }
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("year", getYear())
            .append("quarter", getQuarter())
            .append("status", getStatus())
            .append("plannerUserId", getPlannerUserId())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .append("assessmentCount", getAssessmentCount())
            .append("monthlyAssessmentsList", getMonthlyAssessmentsList())
            .toString();
    }
}

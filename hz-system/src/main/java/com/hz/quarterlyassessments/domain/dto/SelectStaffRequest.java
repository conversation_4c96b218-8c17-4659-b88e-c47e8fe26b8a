package com.hz.quarterlyassessments.domain.dto;

/**
 * 抽取人员请求DTO基类
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
public class SelectStaffRequest {
    
    /** 月度考核ID */
    private Long monthlyAssessmentId;
    
    /** 年份 */
    private String year;
    
    /** 月份 */
    private int month;
    
    /** 抽取数量 */
    private int count;

    public Long getMonthlyAssessmentId() {
        return monthlyAssessmentId;
    }

    public void setMonthlyAssessmentId(Long monthlyAssessmentId) {
        this.monthlyAssessmentId = monthlyAssessmentId;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    /**
     * 抽取组长请求DTO
     */
    public static class SelectLeaderRequest extends SelectStaffRequest {
        // 继承基类所有属性
    }

    /**
     * 抽取监督人员请求DTO
     */
    public static class SelectSupervisorRequest extends SelectStaffRequest {
        // 继承基类所有属性
    }

    /**
     * 一键抽取所有人员请求DTO
     */
    public static class SelectAllStaffRequest {
        private Long monthlyAssessmentId;
        private String year;
        private int month;
        private int leaderCount;
        private int supervisorCount;

        public Long getMonthlyAssessmentId() {
            return monthlyAssessmentId;
        }

        public void setMonthlyAssessmentId(Long monthlyAssessmentId) {
            this.monthlyAssessmentId = monthlyAssessmentId;
        }

        public String getYear() {
            return year;
        }

        public void setYear(String year) {
            this.year = year;
        }

        public int getMonth() {
            return month;
        }

        public void setMonth(int month) {
            this.month = month;
        }

        public int getLeaderCount() {
            return leaderCount;
        }

        public void setLeaderCount(int leaderCount) {
            this.leaderCount = leaderCount;
        }

        public int getSupervisorCount() {
            return supervisorCount;
        }

        public void setSupervisorCount(int supervisorCount) {
            this.supervisorCount = supervisorCount;
        }
    }
} 
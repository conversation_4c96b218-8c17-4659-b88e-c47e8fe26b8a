package com.hz.quarterlyassessments.domain.dto;

import com.hz.common.core.domain.entity.SysUser;
import java.util.List;

/**
 * 保存选定人员请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
public class SaveSelectedStaffRequest {
    
    /** 月度考核ID */
    private Long monthlyAssessmentId;
    
    /** 年份 */
    private String year;
    
    /** 月份 */
    private int month;
    
    /** 组长列表 */
    private List<SysUser> leaders;
    
    /** 监督人员列表 */
    private List<SysUser> supervisors;
    
    /** 专卖人员列表 */
    private List<SysUser> monopolyStaffs;
    
    /** 营销人员列表 */
    private List<SysUser> marketingStaffs;

    public Long getMonthlyAssessmentId() {
        return monthlyAssessmentId;
    }

    public void setMonthlyAssessmentId(Long monthlyAssessmentId) {
        this.monthlyAssessmentId = monthlyAssessmentId;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public List<SysUser> getLeaders() {
        return leaders;
    }

    public void setLeaders(List<SysUser> leaders) {
        this.leaders = leaders;
    }

    public List<SysUser> getSupervisors() {
        return supervisors;
    }

    public void setSupervisors(List<SysUser> supervisors) {
        this.supervisors = supervisors;
    }

    public List<SysUser> getMonopolyStaffs() {
        return monopolyStaffs;
    }

    public void setMonopolyStaffs(List<SysUser> monopolyStaffs) {
        this.monopolyStaffs = monopolyStaffs;
    }

    public List<SysUser> getMarketingStaffs() {
        return marketingStaffs;
    }

    public void setMarketingStaffs(List<SysUser> marketingStaffs) {
        this.marketingStaffs = marketingStaffs;
    }
} 
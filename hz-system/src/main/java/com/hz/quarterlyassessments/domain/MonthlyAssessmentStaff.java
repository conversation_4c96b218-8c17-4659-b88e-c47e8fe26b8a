package com.hz.quarterlyassessments.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hz.common.core.domain.BaseEntity;
import com.hz.common.core.domain.entity.SysUser;
import com.hz.common.core.domain.entity.SysRole;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.hz.common.annotation.Excel;

/**
 * 月度考核人员中间表对象 monthly_assessment_staff
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public class MonthlyAssessmentStaff extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 月度考核ID */
    @Excel(name = "月度考核ID")
    private Long monthlyAssessmentId;

    /** 人员ID (关联sys_user表) */
    @Excel(name = "人员ID")
    private Long userId;

    /** 角色ID (关联sys_role表) */
    @Excel(name = "角色ID")
    private Long roleId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /** 用户信息（关联查询） */
    private SysUser user;

    /** 角色信息（关联查询） */
    private SysRole role;

    /** 用户名（简化字段） */
    private String userName;

    /** 角色名称（简化字段） */
    private String roleName;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }

    public void setMonthlyAssessmentId(Long monthlyAssessmentId) 
    {
        this.monthlyAssessmentId = monthlyAssessmentId;
    }

    public Long getMonthlyAssessmentId() 
    {
        return monthlyAssessmentId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setRoleId(Long roleId) 
    {
        this.roleId = roleId;
    }

    public Long getRoleId() 
    {
        return roleId;
    }

    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }

    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }

    public void setUser(SysUser user) 
    {
        this.user = user;
    }

    public SysUser getUser() 
    {
        return user;
    }

    public void setRole(SysRole role) 
    {
        this.role = role;
    }

    public SysRole getRole() 
    {
        return role;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setRoleName(String roleName) 
    {
        this.roleName = roleName;
    }

    public String getRoleName() 
    {
        return roleName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("monthlyAssessmentId", getMonthlyAssessmentId())
            .append("userId", getUserId())
            .append("roleId", getRoleId())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .append("user", getUser())
            .append("role", getRole())
            .toString();
    }
} 
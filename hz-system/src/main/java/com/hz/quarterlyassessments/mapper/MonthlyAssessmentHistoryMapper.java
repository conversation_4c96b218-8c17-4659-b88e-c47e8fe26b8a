package com.hz.quarterlyassessments.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.hz.quarterlyassessments.domain.MonthlyAssessmentHistory;

/**
 * 月度考核人员抽取历史记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
public interface MonthlyAssessmentHistoryMapper 
{
    /**
     * 查询已被抽中的用户ID列表
     * 
     * @param year 年份
     * @param roleId 角色ID
     * @param halfYear 半年标识（可为空）
     * @return 用户ID列表
     */
    public List<Long> selectAssignedUserIds(@Param("year") String year, 
                                           @Param("roleId") Long roleId, 
                                           @Param("halfYear") String halfYear);

    /**
     * 查询已被抽中的用户ID列表（排除指定月度考核）
     * 
     * @param year 年份
     * @param roleId 角色ID
     * @param halfYear 半年标识（可为空）
     * @param excludeMonthlyAssessmentId 排除的月度考核ID
     * @return 用户ID列表
     */
    public List<Long> selectAssignedUserIdsExclude(@Param("year") String year, 
                                                  @Param("roleId") Long roleId, 
                                                  @Param("halfYear") String halfYear,
                                                  @Param("excludeMonthlyAssessmentId") Long excludeMonthlyAssessmentId);

    /**
     * 新增月度考核抽取历史记录
     * 
     * @param history 抽取历史记录
     * @return 结果
     */
    public int insertMonthlyAssessmentHistory(MonthlyAssessmentHistory history);

    /**
     * 查询月度考核抽取历史记录列表
     * 
     * @param history 抽取历史记录
     * @return 抽取历史记录集合
     */
    public List<MonthlyAssessmentHistory> selectMonthlyAssessmentHistoryList(MonthlyAssessmentHistory history);

    /**
     * 根据月度考核ID删除历史记录
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @return 结果
     */
    public int deleteByMonthlyAssessmentId(Long monthlyAssessmentId);

    /**
     * 根据季度考核ID删除历史记录
     * 
     * @param quarterlyAssessmentId 季度考核ID
     * @return 结果
     */
    public int deleteByQuarterlyAssessmentId(String quarterlyAssessmentId);



    /**
     * 根据用户ID、月度考核ID和年份查询历史记录
     * 
     * @param userId 用户ID
     * @param monthlyAssessmentId 月度考核ID
     * @param year 年份
     * @return 月度考核抽取历史记录
     */
    public MonthlyAssessmentHistory selectByUserIdAndMonthlyAssessmentIdAndYear(@Param("userId") Long userId, 
                                                                                @Param("monthlyAssessmentId") Long monthlyAssessmentId, 
                                                                                @Param("year") String year);

    /**
     * 根据用户ID、月度考核ID和年份更新签字状态
     * 
     * @param userId 用户ID
     * @param monthlyAssessmentId 月度考核ID
     * @param year 年份
     * @param signatureStatus 签字状态
     * @return 结果
     */
    public int updateSignatureStatusByUserIdAndMonthlyAssessmentIdAndYear(@Param("userId") Long userId, 
                                                                          @Param("monthlyAssessmentId") Long monthlyAssessmentId, 
                                                                          @Param("year") String year, 
                                                                          @Param("signatureStatus") String signatureStatus);

    /**
     * 统计指定月度考核和年份的已签字人数
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @param year 年份
     * @return 已签字人数
     */
    public int countSignedByMonthlyAssessmentIdAndYear(@Param("monthlyAssessmentId") Long monthlyAssessmentId, 
                                                       @Param("year") String year);

    /**
     * 统计指定月度考核的应该签字的总人数（从monthly_assessment_staff表）
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @return 应该签字的总人数
     */
    public int countTotalStaffByMonthlyAssessmentId(@Param("monthlyAssessmentId") Long monthlyAssessmentId);

    /**
     * 根据用户ID和月度考核ID查询历史记录（不依赖年份）
     * 
     * @param userId 用户ID
     * @param monthlyAssessmentId 月度考核ID
     * @return 月度考核抽取历史记录
     */
    public MonthlyAssessmentHistory selectByUserIdAndMonthlyAssessmentId(@Param("userId") Long userId, 
                                                                         @Param("monthlyAssessmentId") Long monthlyAssessmentId);

    /**
     * 根据用户ID和月度考核ID更新签字状态（不依赖年份）
     * 
     * @param userId 用户ID
     * @param monthlyAssessmentId 月度考核ID
     * @param signatureStatus 签字状态
     * @return 结果
     */
    public int updateSignatureStatusByUserIdAndMonthlyAssessmentId(@Param("userId") Long userId, 
                                                                   @Param("monthlyAssessmentId") Long monthlyAssessmentId, 
                                                                   @Param("signatureStatus") String signatureStatus);

    /**
     * 统计指定月度考核的已签字人数（不依赖年份）
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @return 已签字人数
     */
    public int countSignedByMonthlyAssessmentId(@Param("monthlyAssessmentId") Long monthlyAssessmentId);

    /**
     * 根据月度考核ID和角色ID删除历史记录
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @param roleId 角色ID
     * @return 结果
     */
    public int deleteByMonthlyAssessmentIdAndRoleId(@Param("monthlyAssessmentId") Long monthlyAssessmentId, @Param("roleId") Long roleId);
} 
package com.hz.quarterlyassessments.mapper;

import java.util.List;
import com.hz.quarterlyassessments.domain.MonthlyAssessmentStaff;

/**
 * 月度考核人员Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-17
 */
public interface MonthlyAssessmentStaffMapper 
{
    /**
     * 查询月度考核人员
     * 
     * @param id 月度考核人员主键
     * @return 月度考核人员
     */
    public MonthlyAssessmentStaff selectMonthlyAssessmentStaffById(String id);

    /**
     * 查询月度考核人员列表
     * 
     * @param monthlyAssessmentStaff 月度考核人员
     * @return 月度考核人员集合
     */
    public List<MonthlyAssessmentStaff> selectMonthlyAssessmentStaffList(MonthlyAssessmentStaff monthlyAssessmentStaff);

    /**
     * 新增月度考核人员
     * 
     * @param monthlyAssessmentStaff 月度考核人员
     * @return 结果
     */
    public int insertMonthlyAssessmentStaff(MonthlyAssessmentStaff monthlyAssessmentStaff);

    /**
     * 修改月度考核人员
     * 
     * @param monthlyAssessmentStaff 月度考核人员
     * @return 结果
     */
    public int updateMonthlyAssessmentStaff(MonthlyAssessmentStaff monthlyAssessmentStaff);

    /**
     * 删除月度考核人员
     * 
     * @param id 月度考核人员主键
     * @return 结果
     */
    public int deleteMonthlyAssessmentStaffById(String id);

    /**
     * 批量删除月度考核人员
     * 
     * @param ids 需要删除的月度考核人员主键集合
     * @return 结果
     */
    public int deleteMonthlyAssessmentStaffByIds(String[] ids);

    /**
     * 根据月度考核ID删除人员记录
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @return 结果
     */
    public int deleteMonthlyAssessmentStaffByMonthlyId(String monthlyAssessmentId);

    /**
     * 批量新增月度考核人员
     *
     * @param list 月度考核人员列表
     * @return 结果
     */
    public int batchInsertMonthlyAssessmentStaff(List<MonthlyAssessmentStaff> list);

    /**
     * 根据月度考核ID和角色ID删除人员记录
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @param roleId 角色ID
     * @return 结果
     */
    public int deleteMonthlyAssessmentStaffByMonthlyIdAndRoleId(String monthlyAssessmentId, Long roleId);
} 
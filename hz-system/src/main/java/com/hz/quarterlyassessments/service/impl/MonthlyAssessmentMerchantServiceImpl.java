package com.hz.quarterlyassessments.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import com.hz.common.exception.ServiceException;
import com.hz.common.utils.DateUtils;
import com.hz.common.utils.DictUtils;
import com.hz.common.utils.SecurityUtils;
import com.hz.quarterlyassessments.domain.MonthlyAssessmentMerchant;
import com.hz.quarterlyassessments.mapper.MonthlyAssessmentMerchantMapper;
import com.hz.quarterlyassessments.service.IMonthlyAssessmentMerchantService;
import com.hz.merchant.domain.MerchantInfo;
import com.hz.quarterlyassessments.domain.dto.MonthlyMerchantAssessmentDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 月度考核商户关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Service
public class MonthlyAssessmentMerchantServiceImpl implements IMonthlyAssessmentMerchantService 
{
    @Autowired
    private MonthlyAssessmentMerchantMapper monthlyAssessmentMerchantMapper;

    /**
     * 查询月度考核商户关联
     * 
     * @param id 月度考核商户关联主键
     * @return 月度考核商户关联
     */
    @Override
    public MonthlyAssessmentMerchant selectMonthlyAssessmentMerchantById(Long id, String type)
    {
        return monthlyAssessmentMerchantMapper.selectMonthlyAssessmentMerchantById(id, type);
    }

    /**
     * 查询月度考核商户关联列表
     * 
     * @param monthlyAssessmentMerchant 月度考核商户关联
     * @return 月度考核商户关联
     */
    @Override
    public List<MonthlyAssessmentMerchant> selectMonthlyAssessmentMerchantList(MonthlyAssessmentMerchant monthlyAssessmentMerchant)
    {
        return monthlyAssessmentMerchantMapper.selectMonthlyAssessmentMerchantList(monthlyAssessmentMerchant);
    }

    /**
     * 根据月度考核ID查询关联的商户列表（包含商户详细信息）
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @return 月度考核商户关联集合
     */
    @Override
    public List<MonthlyAssessmentMerchant> selectMerchantsByMonthlyAssessmentId(Long monthlyAssessmentId, String selectionType)
    {
        List<MonthlyAssessmentMerchant> list = monthlyAssessmentMerchantMapper.selectMerchantsByMonthlyAssessmentId(monthlyAssessmentId, selectionType);
        
        // 为每个对象设置商户类型标签
        for (MonthlyAssessmentMerchant merchant : list) {
            if (merchant.getSelectionType() != null) {
                String typeLabel = getSelectionTypeLabel(merchant.getSelectionType());
                merchant.setSelectionTypeLabel(typeLabel);
            }
        }
        
        return list;
    }

    /**
     * 新增月度考核商户关联
     * 
     * @param monthlyAssessmentMerchant 月度考核商户关联
     * @return 结果
     */
    @Override
    public int insertMonthlyAssessmentMerchant(MonthlyAssessmentMerchant monthlyAssessmentMerchant)
    {
        // 设置创建时间和创建者信息
        Date now = DateUtils.getNowDate();
        monthlyAssessmentMerchant.setCreateTime(now);
        try {
            Long currentUserId = SecurityUtils.getUserId();
            String currentUserName = SecurityUtils.getUsername();
            monthlyAssessmentMerchant.setCreateId(currentUserId);
            monthlyAssessmentMerchant.setCreateBy(currentUserName);
        } catch (Exception e) {
            // 如果获取当前用户失败，忽略
        }
        
        return monthlyAssessmentMerchantMapper.insertMonthlyAssessmentMerchant(monthlyAssessmentMerchant);
    }

    /**
     * 保存抽取的商户到月度考核
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @param selectedMerchants 抽取的商户列表
     * @param selectionType 抽取类型
     * @param totalDistance 总距离（可选）
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveSelectedMerchants(Long monthlyAssessmentId, List<MerchantInfo> selectedMerchants, 
                                       String selectionType, Double totalDistance)
    {
        if (monthlyAssessmentId == null) {
            throw new ServiceException("月度考核ID不能为空");
        }
        
        if (selectedMerchants == null || selectedMerchants.isEmpty()) {
            throw new ServiceException("抽取的商户列表不能为空");
        }
        
        try {
            // 先删除该月度考核已有的商户关联
            monthlyAssessmentMerchantMapper.deleteByMonthlyAssessmentId(monthlyAssessmentId);
            
            // 准备批量插入的数据
            List<MonthlyAssessmentMerchant> merchantAssociations = new ArrayList<>();
            Date now = DateUtils.getNowDate();
            
            // 获取当前用户信息
            Long currentUserId = null;
            String currentUserName = null;
            try {
                currentUserId = SecurityUtils.getUserId();
                currentUserName = SecurityUtils.getUsername();
            } catch (Exception e) {
                // 如果获取当前用户失败，使用默认值
            }
            
            // 构建商户关联数据
            for (int i = 0; i < selectedMerchants.size(); i++) {
                MerchantInfo merchant = selectedMerchants.get(i);
                
                MonthlyAssessmentMerchant association = new MonthlyAssessmentMerchant();
                association.setMonthlyAssessmentId(monthlyAssessmentId);
                association.setMerchantId(merchant.getId());
                
                // 转换商户类型：如果传入的是中文标签，转换为字典值
                String finalSelectionType = convertSelectionTypeToValue(selectionType);
                association.setSelectionType(finalSelectionType);
                
                association.setSelectionOrder(i + 1); // 抽取顺序从1开始
                association.setAssessmentStatus("0"); // 默认未考核
                
                // 设置默认签到签退状态
                association.setZhuanmaiCheckStatus("0"); // 专卖默认未签到
                association.setYingxiaoCheckStatus("0"); // 营销默认未签到
                
                // 如果有距离信息，设置距离（这里简化处理，实际可能需要计算）
                if (totalDistance != null && selectedMerchants.size() > 1) {
                    // 平均分配距离到各个商户
                    BigDecimal avgDistance = BigDecimal.valueOf(totalDistance / selectedMerchants.size());
                    association.setDistanceToNext(avgDistance);
                }
                
                // 设置创建信息
                association.setCreateTime(now);
                if (currentUserId != null) {
                    association.setCreateId(currentUserId);
                }
                if (currentUserName != null) {
                    association.setCreateBy(currentUserName);
                }
                
                merchantAssociations.add(association);
            }
            
            // 批量插入
            int result = monthlyAssessmentMerchantMapper.batchInsertMonthlyAssessmentMerchants(merchantAssociations);
            
            return result > 0;
        } catch (Exception e) {
            throw new ServiceException("保存抽取的商户失败：" + e.getMessage());
        }
    }

    /**
     * 修改月度考核商户关联
     * 
     * @param monthlyAssessmentMerchant 月度考核商户关联
     * @return 结果
     */
    @Override
    public int updateMonthlyAssessmentMerchant(MonthlyAssessmentMerchant monthlyAssessmentMerchant)
    {
        // 设置更新时间和更新者信息
        Date now = DateUtils.getNowDate();
        monthlyAssessmentMerchant.setUpdateTime(now);
        try {
            Long currentUserId = SecurityUtils.getUserId();
            String currentUserName = SecurityUtils.getUsername();
            monthlyAssessmentMerchant.setUpdateId(currentUserId);
            monthlyAssessmentMerchant.setUpdateBy(currentUserName);
        } catch (Exception e) {
            // 如果获取当前用户失败，忽略
        }
        
        return monthlyAssessmentMerchantMapper.updateMonthlyAssessmentMerchant(monthlyAssessmentMerchant);
    }

    /**
     * 批量删除月度考核商户关联
     * 
     * @param ids 需要删除的月度考核商户关联主键
     * @return 结果
     */
    @Override
    public int deleteMonthlyAssessmentMerchantByIds(Long[] ids)
    {
        return monthlyAssessmentMerchantMapper.deleteMonthlyAssessmentMerchantByIds(ids);
    }

    /**
     * 删除月度考核商户关联信息
     * 
     * @param id 月度考核商户关联主键
     * @return 结果
     */
    @Override
    public int deleteMonthlyAssessmentMerchantById(Long id)
    {
        return monthlyAssessmentMerchantMapper.deleteMonthlyAssessmentMerchantById(id);
    }

    /**
     * 根据月度考核ID删除商户关联
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @return 结果
     */
    @Override
    public int deleteByMonthlyAssessmentId(Long monthlyAssessmentId)
    {
        return monthlyAssessmentMerchantMapper.deleteByMonthlyAssessmentId(monthlyAssessmentId);
    }

    /**
     * 根据季度考核ID删除商户关联
     * 
     * @param quarterlyAssessmentId 季度考核ID
     * @return 结果
     */
    @Override
    public int deleteByQuarterlyAssessmentId(String quarterlyAssessmentId)
    {
        return monthlyAssessmentMerchantMapper.deleteByQuarterlyAssessmentId(quarterlyAssessmentId);
    }

    /**
     * 统计月度考核关联的商户数量
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @return 商户数量
     */
    @Override
    public int countMerchantsByMonthlyAssessmentId(Long monthlyAssessmentId)
    {
        return monthlyAssessmentMerchantMapper.countMerchantsByMonthlyAssessmentId(monthlyAssessmentId);
    }

    /**
     * 检查商户是否已被关联到指定月度考核
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @param merchantId 商户ID
     * @return 是否已关联
     */
    @Override
    public boolean checkMerchantAssociation(Long monthlyAssessmentId, Long merchantId)
    {
        int count = monthlyAssessmentMerchantMapper.checkMerchantAssociation(monthlyAssessmentId, merchantId);
        return count > 0;
    }



    /**
     * 更新商户闭店状态
     * 
     * @param id 月度考核商户关联ID
     * @param assessmentStatus 闭店状态：0-正常营业，1-闭店
     * @return 结果
     */
    @Override
    public int updateAssessmentStatus(Long id, String assessmentStatus)
    {
        return monthlyAssessmentMerchantMapper.updateAssessmentStatus(id, assessmentStatus);
    }



    /**
     * 根据月度考核ID查询商户考核数据（精简字段）
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @return 商户考核数据列表（精简字段）
     */
    @Override
    public List<MonthlyMerchantAssessmentDto> selectMerchantAssessmentsSummaryByMonthlyId(Long monthlyAssessmentId)
    {
        return monthlyAssessmentMerchantMapper.selectMerchantAssessmentsSummaryByMonthlyId(monthlyAssessmentId);
    }

    /**
     * 根据商户类型值获取对应的字典标签
     * 
     * @param selectionType 商户类型值
     * @return 商户类型标签
     */
    private String getSelectionTypeLabel(String selectionType) {
        // 先尝试从字典中获取标签
        String dictLabel = DictUtils.getDictLabel("merchant_type", selectionType);
        
        // 如果字典中没有配置，使用默认映射
        if (dictLabel == null || dictLabel.isEmpty()) {
            switch (selectionType) {
                case "0":
                    return "考核商户";
                case "1":
                    return "自主添加商户";
                case "2":
                    return "自主添加无证户";
                default:
                    return selectionType; // 如果都没有匹配，返回原值
            }
        }
        
        return dictLabel;
    }

    /**
     * 将中文标签转换为字典值
     * 
     * @param selectionType 商户类型（可能是中文标签或字典值）
     * @return 字典值
     */
    private String convertSelectionTypeToValue(String selectionType) {
        if (selectionType == null || selectionType.isEmpty()) {
            return "0"; // 默认为考核商户
        }
        
        // 如果已经是字典值，直接返回
        if ("0".equals(selectionType) || "1".equals(selectionType) || "2".equals(selectionType)) {
            return selectionType;
        }
        
        // 中文标签转换为字典值
        switch (selectionType) {
            case "考核商户":
                return "0";
            case "自主添加商户":
                return "1";
            case "自主添加无证户":
                return "2";
            default:
                return "0"; // 默认为考核商户
        }
    }



    /**
     * 更新专卖签到签退状态
     * 
     * @param id 月度考核商户关联ID
     * @param zhuanmaiCheckStatus 专卖状态值：0-未签到，1-已签到，2-已提交表单，3-已签退
     * @return 结果
     */
    @Override
    public int updateZhuanmaiCheckStatus(Long id, String zhuanmaiCheckStatus)
    {
        Date now = DateUtils.getNowDate();
        
        Long currentUserId = null;
        String currentUserName = null;
        try {
            currentUserId = SecurityUtils.getUserId();
            currentUserName = SecurityUtils.getUsername();
        } catch (Exception e) {
            // 如果获取当前用户失败，忽略
        }
        
        // 根据状态值决定是否记录时间
        Date zhuanmaiCheckInTime = null;
        Date zhuanmaiCheckOutTime = null;
        
        if ("1".equals(zhuanmaiCheckStatus)) {
            // 签到时记录签到时间
            zhuanmaiCheckInTime = now;
        } else if ("3".equals(zhuanmaiCheckStatus)) {
            // 签退时记录签退时间
            zhuanmaiCheckOutTime = now;
        }
        
        return monthlyAssessmentMerchantMapper.updateZhuanmaiCheckStatus(id, zhuanmaiCheckStatus, zhuanmaiCheckInTime, zhuanmaiCheckOutTime, currentUserId, currentUserName, now);
    }

    /**
     * 更新营销签到签退状态
     * 
     * @param id 月度考核商户关联ID
     * @param yingxiaoCheckStatus 营销状态值：0-未签到，1-已签到，2-已提交表单，3-已签退
     * @return 结果
     */
    @Override
    public int updateYingxiaoCheckStatus(Long id, String yingxiaoCheckStatus)
    {
        Date now = DateUtils.getNowDate();
        
        Long currentUserId = null;
        String currentUserName = null;
        try {
            currentUserId = SecurityUtils.getUserId();
            currentUserName = SecurityUtils.getUsername();
        } catch (Exception e) {
            // 如果获取当前用户失败，忽略
        }
        
        // 根据状态值决定是否记录时间
        Date yingxiaoCheckInTime = null;
        Date yingxiaoCheckOutTime = null;
        
        if ("1".equals(yingxiaoCheckStatus)) {
            // 签到时记录签到时间
            yingxiaoCheckInTime = now;
        } else if ("3".equals(yingxiaoCheckStatus)) {
            // 签退时记录签退时间
            yingxiaoCheckOutTime = now;
        }
        
        return monthlyAssessmentMerchantMapper.updateYingxiaoCheckStatus(id, yingxiaoCheckStatus, yingxiaoCheckInTime, yingxiaoCheckOutTime, currentUserId, currentUserName, now);
    }



    /**
     * 根据月度考核ID统计专卖和营销都签退（状态都为3）的商户数量（排除无证户和闭店商户）
     *
     * @param monthlyAssessmentId 月度考核ID
     * @return 专卖和营销都签退的商户数量（不包含selectionType为2和assessmentStatus为1的商户）
     */
    @Override
    public int countBothCheckStatusThreeByMonthlyAssessmentId(Long monthlyAssessmentId)
    {
        return monthlyAssessmentMerchantMapper.countBothCheckStatusThreeByMonthlyAssessmentId(monthlyAssessmentId);
    }

    /**
     * 批量更新指定商户的考核人员ID
     *
     * @param monthlyAssessmentId 月度考核ID
     * @param merchantIds 商户ID列表
     * @param assessorId 考核人员ID
     * @return 更新的记录数
     */
    @Override
    public int updateAssessorIdByMerchantIds(Long monthlyAssessmentId, List<Long> merchantIds, Long assessorId)
    {
        if (monthlyAssessmentId == null || merchantIds == null || merchantIds.isEmpty() || assessorId == null) {
            throw new ServiceException("参数不能为空");
        }

        Date now = DateUtils.getNowDate();
        Long currentUserId = null;
        String currentUserName = null;
        try {
            currentUserId = SecurityUtils.getUserId();
            currentUserName = SecurityUtils.getUsername();
        } catch (Exception e) {
            // 如果获取当前用户失败，忽略
        }

        return monthlyAssessmentMerchantMapper.updateAssessorIdByMerchantIds(
            monthlyAssessmentId, merchantIds, assessorId, currentUserId, currentUserName, now);
    }
}
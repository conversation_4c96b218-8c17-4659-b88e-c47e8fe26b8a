package com.hz.quarterlyassessments.service.impl;

import java.util.List;
import java.util.Collections;
import java.util.Date;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hz.common.exception.ServiceException;
import com.hz.common.utils.DateUtils;
import com.hz.common.utils.StringUtils;
import com.hz.common.utils.uuid.IdUtils;
import com.hz.quarterlyassessments.domain.MonthlyAssessmentStaff;
import com.hz.quarterlyassessments.domain.MonthlyAssessmentHistory;
import com.hz.quarterlyassessments.domain.dto.UpdateMonthlyStaffRequest;
import com.hz.quarterlyassessments.mapper.MonthlyAssessmentStaffMapper;
import com.hz.quarterlyassessments.mapper.MonthlyAssessmentHistoryMapper;
import com.hz.quarterlyassessments.mapper.QuarterlyAssessmentsMapper;
import com.hz.quarterlyassessments.service.IMonthlyAssessmentStaffService;
import com.hz.common.core.domain.entity.SysUser;
import com.hz.system.mapper.SysUserMapper;
import com.hz.system.mapper.SysRoleMapper;

/**
 * 月度考核人员Service业务层处理
 * 
 * <AUTHOR>
 * &#064;date  2024-04-17
 */
@Service
public class MonthlyAssessmentStaffServiceImpl implements IMonthlyAssessmentStaffService 
{
    @Autowired
    private MonthlyAssessmentStaffMapper monthlyAssessmentStaffMapper;

    @Autowired
    private MonthlyAssessmentHistoryMapper monthlyAssessmentHistoryMapper;

    @Autowired
    private QuarterlyAssessmentsMapper quarterlyAssessmentsMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    /**
     * 查询月度考核人员
     * 
     * @param id 月度考核人员主键
     * @return 月度考核人员
     */
    @Override
    public MonthlyAssessmentStaff selectMonthlyAssessmentStaffById(String id)
    {
        return monthlyAssessmentStaffMapper.selectMonthlyAssessmentStaffById(id);
    }

    /**
     * 查询月度考核人员列表
     * 
     * @param monthlyAssessmentStaff 月度考核人员
     * @return 月度考核人员
     */
    @Override
    public List<MonthlyAssessmentStaff> selectMonthlyAssessmentStaffList(MonthlyAssessmentStaff monthlyAssessmentStaff)
    {
        return monthlyAssessmentStaffMapper.selectMonthlyAssessmentStaffList(monthlyAssessmentStaff);
    }

    /**
     * 新增月度考核人员
     * 
     * @param monthlyAssessmentStaff 月度考核人员
     * @return 结果
     */
    @Override
    public int insertMonthlyAssessmentStaff(MonthlyAssessmentStaff monthlyAssessmentStaff)
    {
        if (StringUtils.isEmpty(monthlyAssessmentStaff.getId())) {
            monthlyAssessmentStaff.setId(IdUtils.fastSimpleUUID());
        }
        monthlyAssessmentStaff.setCreatedAt(DateUtils.getNowDate());
        return monthlyAssessmentStaffMapper.insertMonthlyAssessmentStaff(monthlyAssessmentStaff);
    }

    /**
     * 修改月度考核人员
     * 
     * @param monthlyAssessmentStaff 月度考核人员
     * @return 结果
     */
    @Override
    public int updateMonthlyAssessmentStaff(MonthlyAssessmentStaff monthlyAssessmentStaff)
    {
        monthlyAssessmentStaff.setUpdatedAt(DateUtils.getNowDate());
        return monthlyAssessmentStaffMapper.updateMonthlyAssessmentStaff(monthlyAssessmentStaff);
    }

    /**
     * 修改月度考核人员
     * 
     * @param request 月度考核人员
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateMonthlyAssessmentStaff(UpdateMonthlyStaffRequest request) {
        String monthlyAssessmentId = request.getMonthlyAssessmentId().toString();

        // 1. 先删除该月度考核下的所有现有人员关联
        quarterlyAssessmentsMapper.deleteMonthlyAssessmentStaffByMonthlyAssessmentId(monthlyAssessmentId);

        if (request.getStaffList() == null || request.getStaffList().isEmpty()) {
            // 如果新列表为空，则只执行删除操作
            return true;
        }

        // 2. 构建新的待插入人员列表
        List<MonthlyAssessmentStaff> staffToInsert = request.getStaffList().stream()
                .map(staffInfo -> {
                    MonthlyAssessmentStaff newStaff = new MonthlyAssessmentStaff();
                    newStaff.setId(UUID.randomUUID().toString());
                    newStaff.setMonthlyAssessmentId(Long.valueOf(monthlyAssessmentId));
                    newStaff.setUserId(staffInfo.getUserId());
                    newStaff.setRoleId(staffInfo.getRoleId());
                    newStaff.setCreatedAt(new Date());
                    return newStaff;
                })
                .collect(Collectors.toList());


        // 3. 批量插入新的人员关联
        if (!staffToInsert.isEmpty()) {
            monthlyAssessmentStaffMapper.batchInsertMonthlyAssessmentStaff(staffToInsert);
        }

        return true;
    }

    /**
     * 批量删除月度考核人员
     * 
     * @param ids 需要删除的月度考核人员主键
     * @return 结果
     */
    @Override
    public int deleteMonthlyAssessmentStaffByIds(String[] ids)
    {
        return monthlyAssessmentStaffMapper.deleteMonthlyAssessmentStaffByIds(ids);
    }

    /**
     * 删除月度考核人员信息
     * 
     * @param id 月度考核人员主键
     * @return 结果
     */
    @Override
    public int deleteMonthlyAssessmentStaffById(String id)
    {
        return monthlyAssessmentStaffMapper.deleteMonthlyAssessmentStaffById(id);
    }

    /**
     * 随机抽取组长 - 每年只能被抽中一次
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @param year 年份
     * @param count 抽取数量
     * @return 抽取结果
     */
    @Override
    public List<SysUser> selectRandomLeaders(Long monthlyAssessmentId, String year, int count)
    {
        if (monthlyAssessmentId == null || StringUtils.isEmpty(year) || count <= 0) {
            throw new ServiceException("参数错误：月度考核ID、年份不能为空，抽取数量必须大于0");
        }
        
        try {
            // 1. 获取所有组长角色用户（roleId: 102）
            List<SysUser> allLeaders = sysUserMapper.selectUsersByRoleId(102L);
            if (allLeaders == null || allLeaders.isEmpty()) {
                throw new ServiceException("未找到组长角色的用户");
            }
            
            // 2. 查询本年度已经被抽中的组长ID列表（排除当前月度考核的记录）
            List<Long> assignedLeaderIds = monthlyAssessmentHistoryMapper.selectAssignedUserIdsExclude(year, 102L, null, monthlyAssessmentId);
            
            // 3. 过滤出本年度还没有被抽中的组长
            List<SysUser> availableLeaders = allLeaders.stream()
                .filter(user -> !assignedLeaderIds.contains(user.getUserId()))
                .collect(Collectors.toList());
            
            // 4. 检查可用人员数量
            if (availableLeaders.size() < count) {
                throw new ServiceException("可用组长数量不足，需要" + count + "人，可用" + availableLeaders.size() + "人");
            }
            
            // 5. 随机抽取
            Collections.shuffle(availableLeaders);
            List<SysUser> selectedLeaders = availableLeaders.subList(0, count);
            
            // 注意：这里不再记录抽取历史，只有在最终保存时才记录
            return selectedLeaders;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException("抽取组长失败：" + e.getMessage());
        }
    }

    /**
     * 随机抽取监督人员 - 每半年只能被抽中一次
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @param year 年份
     * @param month 月份
     * @param count 抽取数量
     * @return 抽取结果
     */
    @Override
    public List<SysUser> selectRandomSupervisors(Long monthlyAssessmentId, String year, int month, int count)
    {
        if (monthlyAssessmentId == null || StringUtils.isEmpty(year) || month <= 0 || month > 12 || count <= 0) {
            throw new ServiceException("参数错误：月度考核ID、年份不能为空，月份必须在1-12之间，抽取数量必须大于0");
        }
        
        try {
            // 1. 确定半年标识
            String halfYear = month <= 6 ? "H1" : "H2";
            
            // 2. 获取监察处部门的所有用户（假设监察处部门ID为特定值，需要根据实际情况调整）
            // 这里假设监察处部门ID为103，实际使用时需要根据sys_dept表确定
            List<SysUser> allSupervisors = sysUserMapper.selectUsersByRoleId(103L);
            if (allSupervisors == null || allSupervisors.isEmpty()) {
                throw new ServiceException("未找到监察处部门的用户");
            }
            
            // 3. 查询本半年度已经被抽中的监督人员ID列表（排除当前月度考核的记录）
            List<Long> assignedSupervisorIds = monthlyAssessmentHistoryMapper.selectAssignedUserIdsExclude(year, 103L, halfYear, monthlyAssessmentId);
            
            // 4. 过滤出本半年度还没有被抽中的监督人员
            List<SysUser> availableSupervisors = allSupervisors.stream()
                .filter(user -> !assignedSupervisorIds.contains(user.getUserId()))
                .collect(Collectors.toList());
            
            // 5. 检查可用人员数量
            if (availableSupervisors.size() < count) {
                throw new ServiceException("可用监督人员数量不足，需要" + count + "人，可用" + availableSupervisors.size() + "人");
            }
            
            // 6. 随机抽取
            Collections.shuffle(availableSupervisors);
            List<SysUser> selectedSupervisors = availableSupervisors.subList(0, count);
            
            // 注意：这里不再记录抽取历史，只有在最终保存时才记录
            return selectedSupervisors;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException("抽取监督人员失败：" + e.getMessage());
        }
    }

    /**
     * 保存选定的人员到月度考核（仅处理组长和监督人员）
     * 只更新实际传递的非空角色数据，空数组或null的角色保持不变
     * 注意：专卖人员和营销人员的更新已移动到 WxController 中的独立接口
     *
     * @param monthlyAssessmentId 月度考核ID
     * @param leaders 组长列表（null或空数组=不更新，非空=更新）
     * @param supervisors 监督人员列表（null或空数组=不更新，非空=更新）
     * @param monopolyStaffs 专卖人员列表（已废弃，不再处理）
     * @param marketingStaffs 营销人员列表（已废弃，不再处理）
     * @param year 年份
     * @param month 月份
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveSelectedStaff(Long monthlyAssessmentId, List<SysUser> leaders, List<SysUser> supervisors, 
                                   List<SysUser> monopolyStaffs, List<SysUser> marketingStaffs, String year, int month)
    {
        if (monthlyAssessmentId == null) {
            throw new ServiceException("月度考核ID不能为空");
        }
        
        try {
            Date now = DateUtils.getNowDate();
            
            // 更新组长数据
            if (leaders != null && !leaders.isEmpty()) {
                System.out.println("更新组长数据，数量: " + leaders.size());
                // 先删除现有记录
                monthlyAssessmentStaffMapper.deleteMonthlyAssessmentStaffByMonthlyIdAndRoleId(monthlyAssessmentId.toString(), 102L);
                monthlyAssessmentHistoryMapper.deleteByMonthlyAssessmentIdAndRoleId(monthlyAssessmentId, 102L);

                // 插入新记录
                for (SysUser leader : leaders) {
                    MonthlyAssessmentStaff staff = new MonthlyAssessmentStaff();
                    staff.setId(IdUtils.fastSimpleUUID());
                    staff.setMonthlyAssessmentId(monthlyAssessmentId);
                    staff.setUserId(leader.getUserId());
                    staff.setRoleId(102L);
                    staff.setCreatedAt(now);
                    monthlyAssessmentStaffMapper.insertMonthlyAssessmentStaff(staff);

                    // 记录分配历史
                    MonthlyAssessmentHistory history = new MonthlyAssessmentHistory();
                    history.setId(IdUtils.fastSimpleUUID());
                    history.setUserId(leader.getUserId());
                    history.setRoleId(102L);
                    history.setMonthlyAssessmentId(monthlyAssessmentId);
                    history.setYear(year);
                    history.setCreatedAt(now);
                    monthlyAssessmentHistoryMapper.insertMonthlyAssessmentHistory(history);
                }
            }
            
            // 更新监督人员数据
            if (supervisors != null && !supervisors.isEmpty()) {
                System.out.println("更新监督人员数据，数量: " + supervisors.size());
                // 先删除现有记录
                monthlyAssessmentStaffMapper.deleteMonthlyAssessmentStaffByMonthlyIdAndRoleId(monthlyAssessmentId.toString(), 103L);
                monthlyAssessmentHistoryMapper.deleteByMonthlyAssessmentIdAndRoleId(monthlyAssessmentId, 103L);

                // 插入新记录
                String halfYear = month <= 6 ? "H1" : "H2";
                for (SysUser supervisor : supervisors) {
                    MonthlyAssessmentStaff staff = new MonthlyAssessmentStaff();
                    staff.setId(IdUtils.fastSimpleUUID());
                    staff.setMonthlyAssessmentId(monthlyAssessmentId);
                    staff.setUserId(supervisor.getUserId());
                    staff.setRoleId(103L);
                    staff.setCreatedAt(now);
                    monthlyAssessmentStaffMapper.insertMonthlyAssessmentStaff(staff);

                    // 记录分配历史
                    MonthlyAssessmentHistory history = new MonthlyAssessmentHistory();
                    history.setId(IdUtils.fastSimpleUUID());
                    history.setUserId(supervisor.getUserId());
                    history.setRoleId(103L);
                    history.setMonthlyAssessmentId(monthlyAssessmentId);
                    history.setYear(year);
                    history.setHalfYear(halfYear);
                    history.setCreatedAt(now);
                    monthlyAssessmentHistoryMapper.insertMonthlyAssessmentHistory(history);
                }
            }
            
            // 专卖人员和营销人员的更新逻辑已移动到 WxController 中的独立接口
            // 这里只处理组长和监督人员
            
            return true;
        } catch (Exception e) {
            System.err.println("保存人员分配失败: " + e.getMessage());
            e.printStackTrace();
            throw new ServiceException("保存人员分配失败：" + e.getMessage());
        }
    }


} 
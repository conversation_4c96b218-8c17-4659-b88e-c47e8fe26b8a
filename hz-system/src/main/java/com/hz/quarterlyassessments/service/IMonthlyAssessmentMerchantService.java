package com.hz.quarterlyassessments.service;

import java.util.List;
import com.hz.quarterlyassessments.domain.MonthlyAssessmentMerchant;
import com.hz.merchant.domain.MerchantInfo;
import com.hz.quarterlyassessments.domain.dto.MonthlyMerchantAssessmentDto;

/**
 * 月度考核商户关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
public interface IMonthlyAssessmentMerchantService 
{
    /**
     * 查询月度考核商户关联
     * 
     * @param id 月度考核商户关联主键
     * @return 月度考核商户关联
     */
    public MonthlyAssessmentMerchant selectMonthlyAssessmentMerchantById(Long id, String type);

    /**
     * 查询月度考核商户关联列表
     * 
     * @param monthlyAssessmentMerchant 月度考核商户关联
     * @return 月度考核商户关联集合
     */
    public List<MonthlyAssessmentMerchant> selectMonthlyAssessmentMerchantList(MonthlyAssessmentMerchant monthlyAssessmentMerchant);

    /**
     * 根据月度考核ID查询关联的商户列表（包含商户详细信息）
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @return 月度考核商户关联集合
     */
    public List<MonthlyAssessmentMerchant> selectMerchantsByMonthlyAssessmentId(Long monthlyAssessmentId, String selectionType);

    /**
     * 新增月度考核商户关联
     * 
     * @param monthlyAssessmentMerchant 月度考核商户关联
     * @return 结果
     */
    public int insertMonthlyAssessmentMerchant(MonthlyAssessmentMerchant monthlyAssessmentMerchant);

    /**
     * 保存抽取的商户到月度考核
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @param selectedMerchants 抽取的商户列表
     * @param selectionType 抽取类型
     * @param totalDistance 总距离（可选）
     * @return 结果
     */
    public boolean saveSelectedMerchants(Long monthlyAssessmentId, List<MerchantInfo> selectedMerchants, 
                                       String selectionType, Double totalDistance);

    /**
     * 修改月度考核商户关联
     * 
     * @param monthlyAssessmentMerchant 月度考核商户关联
     * @return 结果
     */
    public int updateMonthlyAssessmentMerchant(MonthlyAssessmentMerchant monthlyAssessmentMerchant);

    /**
     * 批量删除月度考核商户关联
     * 
     * @param ids 需要删除的月度考核商户关联主键集合
     * @return 结果
     */
    public int deleteMonthlyAssessmentMerchantByIds(Long[] ids);

    /**
     * 删除月度考核商户关联信息
     * 
     * @param id 月度考核商户关联主键
     * @return 结果
     */
    public int deleteMonthlyAssessmentMerchantById(Long id);

    /**
     * 根据月度考核ID删除商户关联
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @return 结果
     */
    public int deleteByMonthlyAssessmentId(Long monthlyAssessmentId);

    /**
     * 根据季度考核ID删除商户关联
     * 
     * @param quarterlyAssessmentId 季度考核ID
     * @return 结果
     */
    public int deleteByQuarterlyAssessmentId(String quarterlyAssessmentId);

    /**
     * 统计月度考核关联的商户数量
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @return 商户数量
     */
    public int countMerchantsByMonthlyAssessmentId(Long monthlyAssessmentId);

    /**
     * 检查商户是否已被关联到指定月度考核
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @param merchantId 商户ID
     * @return 是否已关联
     */
    public boolean checkMerchantAssociation(Long monthlyAssessmentId, Long merchantId);



    /**
     * 更新商户闭店状态
     * 
     * @param id 月度考核商户关联ID
     * @param assessmentStatus 闭店状态：0-正常营业，1-闭店
     * @return 结果
     */
    public int updateAssessmentStatus(Long id, String assessmentStatus);



    /**
     * 根据月度考核ID查询商户考核数据（精简字段）
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @return 商户考核数据列表（精简字段）
     */
    public List<MonthlyMerchantAssessmentDto> selectMerchantAssessmentsSummaryByMonthlyId(Long monthlyAssessmentId);



    /**
     * 更新专卖签到签退状态
     * 
     * @param id 月度考核商户关联ID
     * @param zhuanmaiCheckStatus 专卖状态值：0-未签到，1-已签到，2-已提交表单，3-已签退
     * @return 结果
     */
    public int updateZhuanmaiCheckStatus(Long id, String zhuanmaiCheckStatus);

    /**
     * 更新营销签到签退状态
     * 
     * @param id 月度考核商户关联ID
     * @param yingxiaoCheckStatus 营销状态值：0-未签到，1-已签到，2-已提交表单，3-已签退
     * @return 结果
     */
    public int updateYingxiaoCheckStatus(Long id, String yingxiaoCheckStatus);



    /**
     * 根据月度考核ID统计专卖和营销都签退（状态都为3）的商户数量（排除无证户和闭店商户）
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @return 专卖和营销都签退的商户数量（不包含selectionType为2和assessmentStatus为1的商户）
     */
    public int countBothCheckStatusThreeByMonthlyAssessmentId(Long monthlyAssessmentId);
} 
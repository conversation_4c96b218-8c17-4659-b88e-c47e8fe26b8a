<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hz.gather.mapper.GatherSmokePriceMapper">
    
    <resultMap type="GatherSmokePrice" id="GatherSmokePriceResult">
        <result property="id"         column="id"          />
        <result property="name"       column="name"        />
        <result property="code"       column="code"        />
        <result property="lsPrice"    column="ls_price"    />
        <result property="pfPrice"    column="pf_price"    />
        <result property="category"    column="category"    />
        <result property="createId"   column="create_id"   />
        <result property="createBy"   column="create_by"   />
        <result property="createTime" column="create_time" />
        <result property="updateId"   column="update_id"   />
        <result property="updateBy"   column="update_by"   />
        <result property="updateTime" column="update_time" />
        <result property="remark"     column="remark"      />
    </resultMap>

    <sql id="selectGatherSmokePriceVo">
        select id, name, code, ls_price, pf_price, category, create_id, create_by, create_time, update_id, update_by, update_time, remark from gather_smoke_price
    </sql>

    <select id="selectList" parameterType="GatherSmokePrice" resultMap="GatherSmokePriceResult">
        <include refid="selectGatherSmokePriceVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="code != null "> and code = #{code}</if>
            <if test="lsPrice != null "> and ls_price = #{lsPrice}</if>
            <if test="pfPrice != null "> and pf_price = #{pfPrice}</if>
            <if test="category != null "> and category = #{category}</if>
        </where>
        order by create_time desc
    </select>

    <!-- 该次任务下的卷烟品规(价格、品规) -->
    <select id="gaicSmokePrice" parameterType="GatherSmokePrice" resultMap="GatherSmokePriceResult">
        select gsp.* from gather_smoke_price gsp
        <where>
            <if test="classify != null and classify == 'price'"> gsp.id in ( select sp_id from gather_st_price where st_id = #{stId} group by sp_id ) </if>
        </where>
        <where>
            <if test="classify != null and classify == 'inventory'"> gsp.id in ( select sp_id from gather_st_inventory where st_id = #{stId} group by sp_id ) </if>
        </where>
        order by gsp.create_time desc
    </select>

    <!-- 累计任务下的卷烟品规(价格、品规) -->
    <select id="leijSmokePrice" parameterType="GatherSmokePrice" resultMap="GatherSmokePriceResult">
        select gsp.* from gather_smoke_price gsp
        <if test="classify != null and classify == 'price'">
            right join ( select sp_id from gather_st_price where st_id in (${stIds}) group by sp_id ) temp on temp.sp_id = gsp.id
        </if>
        <if test="classify != null and classify == 'inventory'">
            right join ( select sp_id from gather_st_inventory where st_id in (${stIds}) group by sp_id ) temp on temp.sp_id = gsp.id
        </if>
        order by gsp.create_time desc
    </select>

    <select id="selectById" parameterType="Long" resultMap="GatherSmokePriceResult">
        <include refid="selectGatherSmokePriceVo"/>
        where id = #{id}
    </select>

    <insert id="insert" parameterType="GatherSmokePrice" useGeneratedKeys="true" keyProperty="id">
        insert into gather_smoke_price
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="lsPrice != null">ls_price,</if>
            <if test="pfPrice != null">pf_price,</if>
            <if test="category != null">category,</if>
            <if test="createId != null">create_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="lsPrice != null">#{lsPrice},</if>
            <if test="pfPrice != null">#{pfPrice},</if>
            <if test="category != null">#{category},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="update" parameterType="GatherSmokePrice">
        update gather_smoke_price
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="lsPrice != null">ls_price = #{lsPrice},</if>
            <if test="pfPrice != null">pf_price = #{pfPrice},</if>
            <if test="category != null">category = #{category},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="Long">
        delete from gather_smoke_price where id = #{id}
    </delete>

    <delete id="deleteByIds" parameterType="String">
        delete from gather_smoke_price where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
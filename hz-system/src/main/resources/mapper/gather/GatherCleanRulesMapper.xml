<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hz.gather.mapper.GatherCleanRulesMapper">
    
    <resultMap type="GatherCleanRules" id="GatherCleanRulesResult">
        <result property="id"                 column="id"    />
        <result property="name"               column="name"    />
        <result property="type"               column="type"    />
        <result property="floorPfPriceEnable" column="floor_pf_price_enable"    />
        <result property="floorLsPriceEnable" column="floor_ls_price_enable"    />
        <result property="floorRatio"         column="floor_ratio"    />
        <result property="floorNum"           column="floor_num"    />
        <result property="ceilPfPriceEnable"  column="ceil_pf_price_enable"    />
        <result property="ceilLsPriceEnable"  column="ceil_ls_price_enable"    />
        <result property="ceilRatio"          column="ceil_ratio"    />
        <result property="ceilNum"            column="ceil_num"    />
        <result property="isDefault"          column="is_default"    />
        <result property="delFlag"            column="del_flag"    />
        <result property="unenable"           column="unenable"    />
        <result property="createId"           column="create_id"    />
        <result property="createBy"           column="create_by"    />
        <result property="createTime"         column="create_time"    />
        <result property="updateId"           column="update_id"    />
        <result property="updateBy"           column="update_by"    />
        <result property="updateTime"         column="update_time"    />
        <result property="remark"             column="remark"    />
    </resultMap>

    <sql id="selectGatherCleanRulesVo">
        select id, name, type, floor_pf_price_enable, floor_ls_price_enable, floor_ratio, floor_num, ceil_pf_price_enable, ceil_ls_price_enable, ceil_ratio, ceil_num, is_default, del_flag, unenable, create_id, create_by, create_time, update_id, update_by, update_time, remark from gather_clean_rules
    </sql>

    <select id="selectList" parameterType="GatherCleanRules" resultMap="GatherCleanRulesResult">
        <include refid="selectGatherCleanRulesVo"/>
        <where>  
            <if test="name               != null  and name               != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="type               != null  and type               != ''"> and type                  = #{type}</if>
            <if test="floorPfPriceEnable != null  and floorPfPriceEnable != ''"> and floor_pf_price_enable = #{floorPfPriceEnable}</if>
            <if test="floorLsPriceEnable != null  and floorLsPriceEnable != ''"> and floor_ls_price_enable = #{floorLsPriceEnable}</if>
            <if test="floorRatio         != null                              "> and floor_ratio           = #{floorRatio}</if>
            <if test="floorNum           != null                              "> and floor_num             = #{floorNum}</if>
            <if test="ceilPfPriceEnable  != null  and ceilPfPriceEnable  != ''"> and ceil_pf_price_enable  = #{ceilPfPriceEnable}</if>
            <if test="ceilLsPriceEnable  != null  and ceilLsPriceEnable  != ''"> and ceil_ls_price_enable  = #{ceilLsPriceEnable}</if>
            <if test="ceilRatio          != null                              "> and ceil_ratio            = #{ceilRatio}</if>
            <if test="ceilNum            != null                              "> and ceil_num              = #{ceilNum}</if>
            <if test="isDefault          != null                              "> and is_default            = #{isDefault}</if>
            <if test="delFlag            != null  and delFlag            != ''"> and del_flag              = #{delFlag}</if>
            <if test="unenable           != null  and unenable           != ''"> and unenable              = #{unenable}</if>
        </where>
        order by create_time
    </select>
    
    <select id="selectById" parameterType="Long" resultMap="GatherCleanRulesResult">
        <include refid="selectGatherCleanRulesVo"/>
        where id = #{id}
    </select>

    <insert id="insert" parameterType="GatherCleanRules" useGeneratedKeys="true" keyProperty="id">
        insert into gather_clean_rules
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id                 != null                             ">id,</if>
            <if test="name               != null  and name              != ''">name,</if>
            <if test="type               != null                             ">type,</if>
            <if test="floorPfPriceEnable != null and floorPfPriceEnable != ''">floor_pf_price_enable,</if>
            <if test="floorLsPriceEnable != null and floorLsPriceEnable != ''">floor_ls_price_enable,</if>
            <if test="floorRatio         != null                             ">floor_ratio,</if>
            <if test="floorNum           != null                             ">floor_num,</if>
            <if test="ceilPfPriceEnable  != null and ceilPfPriceEnable  != ''">ceil_pf_price_enable,</if>
            <if test="ceilLsPriceEnable  != null and ceilLsPriceEnable  != ''">ceil_ls_price_enable,</if>
            <if test="ceilRatio          != null                             ">ceil_ratio,</if>
            <if test="ceilNum            != null                             ">ceil_num,</if>
            <if test="isDefault          != null                             ">is_default,</if>
            <if test="delFlag            != null                             ">del_flag,</if>
            <if test="unenable           != null                             ">unenable,</if>
            <if test="createId           != null                             ">create_id,</if>
            <if test="createBy           != null                             ">create_by,</if>
            <if test="createTime         != null                             ">create_time,</if>
            <if test="updateId           != null                             ">update_id,</if>
            <if test="updateBy           != null                             ">update_by,</if>
            <if test="updateTime         != null                             ">update_time,</if>
            <if test="remark             != null                             ">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id                 != null                             ">#{id},</if>
            <if test="name               != null and name               != ''">#{name},</if>
            <if test="type               != null                             ">#{type},</if>
            <if test="floorPfPriceEnable != null and floorPfPriceEnable != ''">#{floorPfPriceEnable},</if>
            <if test="floorLsPriceEnable != null and floorLsPriceEnable != ''">#{floorLsPriceEnable},</if>
            <if test="floorRatio         != null                             ">#{floorRatio},</if>
            <if test="floorNum           != null                             ">#{floorNum},</if>
            <if test="ceilPfPriceEnable  != null and ceilPfPriceEnable  != ''">#{ceilPfPriceEnable},</if>
            <if test="ceilLsPriceEnable  != null and ceilLsPriceEnable  != ''">#{ceilLsPriceEnable},</if>
            <if test="ceilRatio          != null                             ">#{ceilRatio},</if>
            <if test="ceilNum            != null                             ">#{ceilNum},</if>
            <if test="isDefault          != null                             ">#{isDefault},</if>
            <if test="delFlag            != null                             ">#{delFlag},</if>
            <if test="unenable           != null                             ">#{unenable},</if>
            <if test="createId           != null                             ">#{createId},</if>
            <if test="createBy           != null                             ">#{createBy},</if>
            <if test="createTime         != null                             ">#{createTime},</if>
            <if test="updateId           != null                             ">#{updateId},</if>
            <if test="updateBy           != null                             ">#{updateBy},</if>
            <if test="updateTime         != null                             ">#{updateTime},</if>
            <if test="remark             != null                             ">#{remark},</if>
         </trim>
    </insert>

    <update id="update" parameterType="GatherCleanRules">
        update gather_clean_rules
        <trim prefix="SET" suffixOverrides=",">
            <if test="name               != null and name               != ''">name = #{name},</if>
            <if test="type               != null                             ">type = #{type},</if>
            <if test="floorPfPriceEnable != null and floorPfPriceEnable != ''">floor_pf_price_enable = #{floorPfPriceEnable},</if>
            <if test="floorLsPriceEnable != null and floorLsPriceEnable != ''">floor_ls_price_enable = #{floorLsPriceEnable},</if>
            <if test="floorRatio         != null                             ">floor_ratio = #{floorRatio},</if>
            <if test="floorNum           != null                             ">floor_num = #{floorNum},</if>
            <if test="ceilPfPriceEnable  != null and ceilPfPriceEnable  != ''">ceil_pf_price_enable = #{ceilPfPriceEnable},</if>
            <if test="ceilLsPriceEnable  != null and ceilLsPriceEnable  != ''">ceil_ls_price_enable = #{ceilLsPriceEnable},</if>
            <if test="ceilRatio          != null                             ">ceil_ratio = #{ceilRatio},</if>
            <if test="ceilNum            != null                             ">ceil_num = #{ceilNum},</if>
            <if test="isDefault          != null                             ">is_default = #{isDefault},</if>
            <if test="delFlag                != null                         ">del_flag = #{delFlag},</if>
            <if test="unenable           != null                             ">unenable = #{unenable},</if>
            <if test="createId           != null                             ">create_id = #{createId},</if>
            <if test="createBy           != null                             ">create_by = #{createBy},</if>
            <if test="createTime         != null                             ">create_time = #{createTime},</if>
            <if test="updateId           != null                             ">update_id = #{updateId},</if>
            <if test="updateBy           != null                             ">update_by = #{updateBy},</if>
            <if test="updateTime         != null                             ">update_time = #{updateTime},</if>
            <if test="remark             != null                             ">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="Long">
        delete from gather_clean_rules where id = #{id}
    </delete>

    <delete id="deleteByIds" parameterType="String">
        delete from gather_clean_rules where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
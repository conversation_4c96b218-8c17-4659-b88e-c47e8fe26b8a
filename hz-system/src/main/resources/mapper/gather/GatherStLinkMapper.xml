<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hz.gather.mapper.GatherStLinkMapper">
    
    <resultMap type="GatherStLink" id="GatherStLinkResult">
        <result property="id"      column="id"      />
        <result property="stId"    column="st_id"   />
        <result property="shXkz"   column="sh_xkz"  />
        <result property="tjr"     column="tjr"     />
        <result property="tjsj"    column="tjsj"    />
        <result property="content" column="content" />
    </resultMap>

    <sql id="selectVo">
        select id, st_id, sh_xkz, tjr, tjsj, content from gather_st_link
    </sql>

    <select id="selectList" parameterType="GatherStLink" resultMap="GatherStLinkResult">
        <include refid="selectVo"/>
        <where>  
            <if test="stId != null "> and st_id = #{stId}</if>
            <if test="shXkz != null  and shXkz != ''"> and sh_xkz = #{shXkz}</if>
            <if test="tjr != null  and tjr != ''"> and tjr = #{tjr}</if>
            <if test="tjsj != null "> and tjsj = #{tjsj}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
        </where>
    </select>
    
    <select id="selectById" parameterType="Long" resultMap="GatherStLinkResult">
        <include refid="selectVo"/>
        where id = #{id}
    </select>

    <insert id="insert" parameterType="GatherStLink" useGeneratedKeys="true" keyProperty="id">
        insert into gather_st_link
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stId != null">st_id,</if>
            <if test="shXkz != null and shXkz != ''">sh_xkz,</if>
            <if test="tjr != null">tjr,</if>
            <if test="tjsj != null">tjsj,</if>
            <if test="content != null">content,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stId != null">#{stId},</if>
            <if test="shXkz != null and shXkz != ''">#{shXkz},</if>
            <if test="tjr != null">#{tjr},</if>
            <if test="tjsj != null">#{tjsj},</if>
            <if test="content != null">#{content},</if>
         </trim>
    </insert>
    <insert id="insertBatch">
        INSERT INTO gather_st_link (st_id, sh_xkz, tjr, tjsj, content)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.stId}, #{item.shXkz}, #{item.tjr}, #{item.tjsj}, #{item.content})
        </foreach>
    </insert>


    <update id="update" parameterType="GatherStLink">
        update gather_st_link
        <trim prefix="SET" suffixOverrides=",">
            <if test="stId != null">st_id = #{stId},</if>
            <if test="shXkz != null and shXkz != ''">sh_xkz = #{shXkz},</if>
            <if test="tjr != null">tjr = #{tjr},</if>
            <if test="tjsj != null">tjsj = #{tjsj},</if>
            <if test="content != null">content = #{content},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="Long">
        delete from gather_st_link where id = #{id}
    </delete>

    <delete id="deleteByIds" parameterType="String">
        delete from gather_st_link where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByTaskId">
        DELETE FROM gather_st_link WHERE st_id = #{taskId}
    </delete>
</mapper>
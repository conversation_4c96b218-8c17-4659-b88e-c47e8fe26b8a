<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hz.gather.mapper.GatherStInventoryMapper">
    
    <resultMap type="GatherStInventory" id="GatherStInventoryResult">
        <result property="id"           column="id"            />
        <result property="stId"         column="st_id"         />
        <result property="shXkz"        column="sh_xkz"        />
        <result property="shDw"         column="sh_dw"         />
        <result property="shCz"         column="sh_cz"         />
        <result property="shYt"         column="sh_yt"         />
        <result property="shYxx"        column="sh_yxx"        />
        <result property="spId"         column="sp_id"         />
        <result property="oldInventory" column="old_inventory" />
        <result property="newInventory" column="new_inventory" />
        <result property="clear1"       column="clear1"        />
        <result property="clear2"       column="clear2"        />
        <result property="tjsj"         column="tjsj"        />
    </resultMap>

    <sql id="selectVo">
        select id, st_id, sh_xkz, sh_dw, sh_cz, sh_yt, sh_yxx, sp_id, old_inventory, new_inventory, clear1, clear2, tjsj from gather_st_inventory
    </sql>

    <select id="selectList" parameterType="GatherStInventory" resultMap="GatherStInventoryResult">
        <include refid="selectVo"/>
        <where>  
            <if test="stId         != null                 "> and st_id         = #{stId         }</if>
            <if test="shXkz        != null  and shXkz != ''"> and sh_xkz        = #{shXkz        }</if>
            <if test="shDw         != null  and shDw  != ''"> and sh_dw         = #{shDw         }</if>
            <if test="shCz         != null  and shCz  != ''"> and sh_cz         = #{shCz         }</if>
            <if test="shYt         != null  and shYt  != ''"> and sh_yt         = #{shYt         }</if>
            <if test="shYxx        != null  and shYxx != ''"> and sh_yxx        = #{shYxx        }</if>
            <if test="spId         != null                 "> and sp_id         = #{spId         }</if>
            <if test="oldInventory != null                 "> and old_inventory = #{oldInventory }</if>
            <if test="newInventory != null                 "> and new_inventory = #{newInventory }</if>
            <if test="clear1       != null                 "> and clear1        = #{clear1       }</if>
            <if test="clear2       != null                 "> and clear2        = #{clear2       }</if>
            <if test="tjsj         != null                 "> and tjsj          = #{tjsj         }</if>
        </where>
    </select>

    <insert id="insertBatch">
        INSERT INTO gather_st_inventory (st_id, sh_xkz, sh_dw, sh_cz, sh_yt, sh_yxx, sp_id, old_inventory, new_inventory, clear1, clear2, tjsj)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.stId}, #{item.shXkz}, #{item.shDw}, #{item.shCz}, #{item.shYt}, #{item.shYxx}, #{item.spId}, #{item.oldInventory}, #{item.newInventory}, #{item.clear1}, #{item.clear2}, #{item.tjsj})
        </foreach>
    </insert>
    
    <select id="selectById" parameterType="Long" resultMap="GatherStInventoryResult">
        <include refid="selectVo"/>
        where id = #{id}
    </select>


    <insert id="insert" parameterType="GatherStInventory" useGeneratedKeys="true" keyProperty="id">
        insert into gather_st_inventory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stId         != null                ">st_id,</if>
            <if test="shXkz        != null and shXkz != ''">sh_xkz,</if>
            <if test="shDw         != null                ">sh_dw,</if>
            <if test="shCz         != null                ">sh_cz,</if>
            <if test="shYt         != null                ">sh_yt,</if>
            <if test="shYxx        != null                ">sh_yxx,</if>
            <if test="spId         != null                ">sp_id,</if>
            <if test="oldInventory != null                ">old_inventory,</if>
            <if test="newInventory != null                ">new_inventory,</if>
            <if test="clear1       != null                ">clear1,</if>
            <if test="clear2       != null                ">clear2,</if>
            <if test="tjsj         != null                ">tjsj,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stId         != null                ">#{stId        },</if>
            <if test="shXkz        != null and shXkz != ''">#{shXkz       },</if>
            <if test="shDw         != null                ">#{shDw        },</if>
            <if test="shCz         != null                ">#{shCz        },</if>
            <if test="shYt         != null                ">#{shYt        },</if>
            <if test="shYxx        != null                ">#{shYxx       },</if>
            <if test="spId         != null                ">#{spId        },</if>
            <if test="oldInventory != null                ">#{oldInventory},</if>
            <if test="newInventory != null                ">#{newInventory},</if>
            <if test="clear1       != null                ">#{clear1      },</if>
            <if test="clear2       != null                ">#{clear2      },</if>
            <if test="tjsj         != null                ">#{tjsj        },</if>
         </trim>
    </insert>

    <update id="update" parameterType="GatherStInventory">
        update gather_st_inventory
        <trim prefix="SET" suffixOverrides=",">
            <if test="stId         != null                ">st_id         = #{stId        },</if>
            <if test="shXkz        != null and shXkz != ''">sh_xkz        = #{shXkz       },</if>
            <if test="shDw         != null                ">sh_dw         = #{shDw        },</if>
            <if test="shCz         != null                ">sh_cz         = #{shCz        },</if>
            <if test="shYt         != null                ">sh_yt         = #{shYt        },</if>
            <if test="shYxx        != null                ">sh_yxx        = #{shYxx       },</if>
            <if test="spId         != null                ">sp_id         = #{spId        },</if>
            <if test="oldInventory != null                ">old_inventory = #{oldInventory},</if>
            <if test="newInventory != null                ">new_inventory = #{newInventory},</if>
            <if test="clear1       != null                ">clear1        = #{clear1      },</if>
            <if test="clear2       != null                ">clear2        = #{clear2      },</if>
            <if test="tjsj         != null                ">tjsj          = #{tjsj        },</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="Long">
        delete from gather_st_inventory where id = #{id}
    </delete>

    <delete id="deleteByIds" parameterType="String">
        delete from gather_st_inventory where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByTaskId">
        DELETE FROM gather_st_inventory WHERE st_id = #{taskId}
    </delete>

    <!-- 批量更新 -->
    <update id="batchUpdateClear2">
        update gather_st_inventory set clear2 = #{clear2} WHERE id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="countYxxShopNumOfErrorByTaskId" resultType="com.alibaba.fastjson2.JSONObject">
        select p.sh_yxx,
            count(sh_xkz) as sh_xkz_count,
            count(distinct sh_xkz) as sh_xkz_distinct_count
        from (
            select sh_yxx, sh_xkz
            from gather_st_inventory
            where st_id = 6 and clear1 = 1
            group by sh_yxx, sh_xkz, tjsj
            ) p
        group by p.sh_yxx
    </select>
</mapper>
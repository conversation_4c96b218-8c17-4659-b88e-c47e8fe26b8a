<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hz.gather.mapper.GatherSurveyTaskMapper">

    <resultMap type="GatherSurveyTask" id="TaskResultMap">
        <result property="id"                    column="id"                      />
        <result property="name"                  column="name"                    />
        <result property="year"                  column="year"                    />
        <result property="month"                 column="month"                   />
        <result property="week"                  column="week"                    />
        <result property="yearMonthWeek"         column="year_month_week"         />

        <result property="importStatusPrice"     column="import_status_price"     />
        <result property="importStatusInventory" column="import_status_inventory" />
        <result property="importStatusLink"      column="import_status_link"      />
        <result property="importTimePrice"       column="import_time_price"       />
        <result property="importTimeInventory"   column="import_time_inventory"   />
        <result property="importTimeLink"        column="import_time_link"        />
        <result property="importFilePrice"       column="import_file_price"       />
        <result property="importFileInventory"   column="import_file_inventory"   />
        <result property="importFileLink"        column="import_file_link"        />

        <result property="description"           column="description"             />
        <result property="createId"              column="create_id"               />
        <result property="createBy"              column="create_by"               />
        <result property="createTime"            column="create_time"             />
        <result property="updateId"              column="update_id"               />
        <result property="updateBy"              column="update_by"               />
        <result property="updateTime"            column="update_time"             />
        <result property="remark"                column="remark"                  />
    </resultMap>

    <sql id="selectVo">
        select id, name, year, month, week, year_month_week, import_status_price, import_status_inventory, import_status_link, import_time_price, import_time_inventory, import_time_link, import_file_price, import_file_inventory, import_file_link, description, create_id, create_by, create_time, update_id, update_by, update_time, remark from gather_survey_task
    </sql>

    <select id="selectList" parameterType="GatherSurveyTask" resultMap="TaskResultMap">
        <include refid="selectVo"/>
        <where>
            <if test="name                  != null  and name != ''         "> and name     like concat('%', #{name}, '%')           </if>
            <if test="year                  != null  and year != ''         "> and year                    = #{year}                 </if>
            <if test="month                 != null  and month != ''        "> and month                   = #{month}                </if>
            <if test="week                  != null  and week != ''         "> and week                    = #{week}                 </if>
            <if test="yearMonthWeek         != null  and yearMonthWeek != ''"> and year_month_week         = #{yearMonthWeek}        </if>
            <if test="importStatusPrice     != null                         "> and import_status_price     = #{importStatusPrice}    </if>
            <if test="importStatusInventory != null                         "> and import_status_inventory = #{importStatusInventory}</if>
            <if test="importStatusLink      != null                         "> and import_status_link      = #{importStatusLink}     </if>
            <if test="importTimePrice       != null                         "> and import_time_price       = #{importTimePrice}      </if>
            <if test="importTimeInventory   != null                         "> and import_time_inventory   = #{importTimeInventory}  </if>
            <if test="importTimeLink        != null                         "> and import_time_link        = #{importTimeLink}       </if>
            <if test="importFilePrice       != null                         "> and import_file_price       = #{importFilePrice}      </if>
            <if test="importFileInventory   != null                         "> and import_file_inventory   = #{importFileInventory}  </if>
            <if test="importFileLink        != null                         "> and import_file_link        = #{importFileLink}       </if>
            <if test="description           != null  and description != ''  "> and description             = #{description}          </if>
            <if test="startTime             != null  and startTime != ''    "> and year_month_week         >= #{startTime}           </if>
            <if test="endTime               != null  and endTime != ''      "> and year_month_week         &lt;= #{endTime}          </if>
        </where>
        order by year_month_week desc
    </select>

    <select id="selectById" parameterType="Long" resultMap="TaskResultMap">
        <include refid="selectVo"/>
        where id = #{id}
    </select>

    <insert id="insert" parameterType="GatherSurveyTask" useGeneratedKeys="true" keyProperty="id">
        insert into gather_survey_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name                  != null and name          != ''">name                   ,</if>
            <if test="year                  != null and year          != ''">year                   ,</if>
            <if test="month                 != null and month         != ''">month                  ,</if>
            <if test="week                  != null and week          != ''">week                   ,</if>
            <if test="yearMonthWeek         != null and yearMonthWeek != ''">year_month_week        ,</if>
            <if test="importStatusPrice     != null"                        >import_status_price    ,</if>
            <if test="importStatusInventory != null"                        >import_status_inventory,</if>
            <if test="importStatusLink      != null"                        >import_status_link     ,</if>
            <if test="importTimePrice       != null"                        >import_time_price      ,</if>
            <if test="importTimeInventory   != null"                        >import_time_inventory  ,</if>
            <if test="importTimeLink        != null"                        >import_time_link       ,</if>
            <if test="importFilePrice       != null"                        >import_file_price      ,</if>
            <if test="importFileInventory   != null"                        >import_file_inventory  ,</if>
            <if test="importFileLink        != null"                        >import_file_link       ,</if>
            <if test="description           != null"                        >description            ,</if>
            <if test="createId              != null"                        >create_id              ,</if>
            <if test="createBy              != null"                        >create_by              ,</if>
            <if test="createTime            != null"                        >create_time            ,</if>
            <if test="updateId              != null"                        >update_id              ,</if>
            <if test="updateBy              != null"                        >update_by              ,</if>
            <if test="updateTime            != null"                        >update_time            ,</if>
            <if test="remark                != null"                        >remark                 ,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name                  != null and name          != ''">#{name}                  ,</if>
            <if test="year                  != null and year          != ''">#{year}                  ,</if>
            <if test="month                 != null and month         != ''">#{month}                 ,</if>
            <if test="week                  != null and week          != ''">#{week}                  ,</if>
            <if test="yearMonthWeek         != null and yearMonthWeek != ''">#{yearMonthWeek}         ,</if>
            <if test="importStatusPrice     != null"                        >#{importStatusPrice}     ,</if>
            <if test="importStatusInventory != null"                        >#{importStatusInventory} ,</if>
            <if test="importStatusLink      != null"                        >#{importStatusLink}      ,</if>
            <if test="importTimePrice       != null"                        >#{importTimePrice}       ,</if>
            <if test="importTimeInventory   != null"                        >#{importTimeInventory}   ,</if>
            <if test="importTimeLink        != null"                        >#{importTimeLink}        ,</if>
            <if test="importFilePrice       != null"                        >#{importFilePrice}       ,</if>
            <if test="importFileInventory   != null"                        >#{importFileInventory}   ,</if>
            <if test="importFileLink        != null"                        >#{importFileLink}        ,</if>
            <if test="description           != null"                        >#{description}           ,</if>
            <if test="createId              != null"                        >#{createId}              ,</if>
            <if test="createBy              != null"                        >#{createBy}              ,</if>
            <if test="createTime            != null"                        >#{createTime}            ,</if>
            <if test="updateId              != null"                        >#{updateId}              ,</if>
            <if test="updateBy              != null"                        >#{updateBy}              ,</if>
            <if test="updateTime            != null"                        >#{updateTime}            ,</if>
            <if test="remark                != null"                        >#{remark}                ,</if>
        </trim>
    </insert>

    <update id="update" parameterType="GatherSurveyTask">
        update gather_survey_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="name                  != null and name          != ''">name                    = #{name}                  ,</if>
            <if test="year                  != null and year          != ''">year                    = #{year}                  ,</if>
            <if test="month                 != null and month         != ''">month                   = #{month}                 ,</if>
            <if test="week                  != null and week          != ''">week                    = #{week}                  ,</if>
            <if test="yearMonthWeek         != null and yearMonthWeek != ''">year_month_week         = #{yearMonthWeek}         ,</if>
            <if test="importStatusPrice     != null"                        >import_status_price     = #{importStatusPrice}     ,</if>
            <if test="importStatusInventory != null"                        >import_status_inventory = #{importStatusInventory} ,</if>
            <if test="importStatusLink      != null"                        >import_status_link      = #{importStatusLink}      ,</if>
            <if test="importTimePrice       != null"                        >import_time_price       = #{importTimePrice}       ,</if>
            <if test="importTimeInventory   != null"                        >import_time_inventory   = #{importTimeInventory}   ,</if>
            <if test="importTimeLink        != null"                        >import_time_link        = #{importTimeLink}        ,</if>
            <if test="importFilePrice       != null"                        >import_file_price       = #{importFilePrice}       ,</if>
            <if test="importFileInventory   != null"                        >import_file_inventory   = #{importFileInventory}   ,</if>
            <if test="importFileLink        != null"                        >import_file_link        = #{importFileLink}        ,</if>
            <if test="description           != null"                        >description             = #{description}           ,</if>
            <if test="createId              != null"                        >create_id               = #{createId}              ,</if>
            <if test="createBy              != null"                        >create_by               = #{createBy}              ,</if>
            <if test="createTime            != null"                        >create_time             = #{createTime}            ,</if>
            <if test="updateId              != null"                        >update_id               = #{updateId}              ,</if>
            <if test="updateBy              != null"                        >update_by               = #{updateBy}              ,</if>
            <if test="updateTime            != null"                        >update_time             = #{updateTime}            ,</if>
            <if test="remark                != null"                        >remark                  = #{remark}                ,</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="Long">
        delete from gather_survey_task where id = #{id}
    </delete>

    <delete id="deleteByIds" parameterType="String">
        delete from gather_survey_task where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hz.examination.mapper.ExaminationReplyMerchantMapper">
    
    <resultMap type="ExaminationReplyMerchant" id="ExaminationReplyMerchantResult">
        <result property="id"    column="id"    />
        <result property="paperId"    column="paper_id"    />
        <result property="merchantId"    column="merchant_id"    />
        <result property="topicNum"    column="topic_num"    />
        <result property="isAnswer"    column="is_answer"    />
        <result property="name"    column="name"    />
        <result property="blanks"    column="blanks"    />
        <result property="score"    column="score"    />
        <result property="sort"    column="sort"    />
        <result property="createId"    column="create_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectExaminationReplyMerchantVo">
        select id, paper_id, merchant_id, topic_num, is_answer, name, blanks, score, sort, create_id, create_by, create_time, remark from examination_reply_merchant
    </sql>

    <select id="selectExaminationReplyMerchantList" parameterType="ExaminationReplyMerchant" resultMap="ExaminationReplyMerchantResult">
        <include refid="selectExaminationReplyMerchantVo"/>
        <where>  
            <if test="paperId != null "> and paper_id = #{paperId}</if>
            <if test="merchantId != null "> and merchant_id = #{merchantId}</if>
            <if test="topicNum != null "> and topic_num = #{topicNum}</if>
            <if test="isAnswer != null  and isAnswer != ''"> and is_answer = #{isAnswer}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="blanks != null  and blanks != ''"> and blanks like concat('%', #{blanks}, '%')</if>
            <if test="score != null "> and score = #{score}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>
    
    <select id="selectExaminationReplyMerchantById" parameterType="Long" resultMap="ExaminationReplyMerchantResult">
        <include refid="selectExaminationReplyMerchantVo"/>
        where id = #{id}
    </select>

    <select id="selectExaminationReplyMerchantByPaperId" parameterType="Long" resultType="Integer">
        select count(1) from examination_reply_merchant where paper_id = #{paperId} and merchant_id = #{merchantId}
    </select>

    <insert id="insertExaminationReplyMerchant" parameterType="ExaminationReplyMerchant" useGeneratedKeys="true" keyProperty="id">
        insert into examination_reply_merchant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="paperId != null">paper_id,</if>
            <if test="merchantId != null">merchant_id,</if>
            <if test="topicNum != null">topic_num,</if>
            <if test="isAnswer != null">is_answer,</if>
            <if test="name != null">name,</if>
            <if test="blanks != null">blanks,</if>
            <if test="score != null">score,</if>
            <if test="sort != null">sort,</if>
            <if test="createId != null">create_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="paperId != null">#{paperId},</if>
            <if test="merchantId != null">#{merchantId},</if>
            <if test="topicNum != null">#{topicNum},</if>
            <if test="isAnswer != null">#{isAnswer},</if>
            <if test="name != null">#{name},</if>
            <if test="blanks != null">#{blanks},</if>
            <if test="score != null">#{score},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="batchInsertExaminationReplyMerchant">
        insert into examination_reply_merchant( paper_id, merchant_id, topic_num, is_answer, name, blanks, score, sort, create_id, create_by, create_time, remark) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.paperId}, #{item.merchantId}, #{item.topicNum}, #{item.isAnswer}, #{item.name}, #{item.blanks}, #{item.score}, #{item.sort}, #{item.createId}, #{item.createBy}, #{item.createTime}, #{item.remark})
        </foreach>
    </insert>

    <update id="updateExaminationReplyMerchant" parameterType="ExaminationReplyMerchant">
        update examination_reply_merchant
        <trim prefix="SET" suffixOverrides=",">
            <if test="paperId != null">paper_id = #{paperId},</if>
            <if test="merchantId != null">merchant_id = #{merchantId},</if>
            <if test="topicNum != null">topic_num = #{topicNum},</if>
            <if test="isAnswer != null">is_answer = #{isAnswer},</if>
            <if test="name != null">name = #{name},</if>
            <if test="blanks != null">blanks = #{blanks},</if>
            <if test="score != null">score = #{score},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExaminationReplyMerchantById" parameterType="Long">
        delete from examination_reply_merchant where id = #{id}
    </delete>

    <delete id="batchDeleteExaminationReplyMerchantById" parameterType="Long">
        delete from examination_reply_merchant where paper_id = #{paperId} and merchant_id = #{merchantId}
    </delete>

    <delete id="deleteExaminationReplyMerchantByIds" parameterType="String">
        delete from examination_reply_merchant where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
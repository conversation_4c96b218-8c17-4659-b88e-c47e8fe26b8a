<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hz.examination.mapper.ExaminationMessageMapper">
    
    <resultMap type="ExaminationMessage" id="ExaminationMessageResult">
        <result property="msgId"    column="msg_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="merchantId"    column="merchant_id"    />
        <result property="monthlyAssessmentsId"    column="monthly_assessments_id"    />
        <result property="paperId"    column="paper_id"    />
        <result property="msgTitle"    column="msg_title"    />
        <result property="msgType"    column="msg_type"    />
        <result property="msgContent"    column="msg_content"    />
        <result property="msgImage"    column="msg_image"    />
        <result property="msgReply"    column="msg_reply"    />
        <result property="status"    column="status"    />
        <result property="receiverId"    column="receiver_id"    />
        <result property="createId"    column="create_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateId"    column="update_id"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="paperName"    column="paper_name"    />
        <result property="licence"    column="licence"    />
        <result property="merchantName"    column="merchant_name"    />
        <result property="county"    column="county"    />
    </resultMap>

    <sql id="selectExaminationMessageVo">
        select msg_id, parent_id, merchant_id, monthly_assessments_id, paper_id, msg_title, msg_type, msg_content, msg_image, msg_reply, status, receiver_id, create_id, create_by, create_time, update_id, update_by, update_time, remark from examination_message
    </sql>

    <select id="selectExaminationMessageList" parameterType="ExaminationMessage" resultMap="ExaminationMessageResult">
        select em.msg_id, em.parent_id, em.merchant_id, em.monthly_assessments_id, em.paper_id, em.msg_title, em.msg_type, em.msg_content, em.msg_image, em.msg_reply, em.status, em.receiver_id, em.create_id, em.create_by, em.create_time, em.update_id, em.update_by, em.update_time, em.remark,
               ep.name as paper_name, mi.licence, mi.merchant_name, mi.county
        from examination_message em left join examination_paper ep on ep.id = em.paper_id left join merchant_info mi on mi.id = em.merchant_id
        <where>  
            <if test="parentId != null "> and em.parent_id = #{parentId}</if>
            <if test="monthlyAssessmentsId != null "> and em.monthly_assessments_id = #{monthlyAssessmentsId}</if>
            <if test="paperId != null "> and em.paper_id = #{paperId}</if>
            <if test="merchantId != null "> and em.merchant_id = #{merchantId}</if>
            <if test="msgTitle != null  and msgTitle != ''"> and em.msg_title = #{msgTitle}</if>
            <if test="msgType != null  and msgType != ''"> and em.msg_type = #{msgType}</if>
            <if test="msgContent != null  and msgContent != ''"> and em.msg_content = #{msgContent}</if>
            <if test="status != null  and status != ''"> and em.status = #{status}</if>
            <if test="receiverId != null  and receiverId != ''"> and em.receiver_id = #{receiverId}</if>
            <if test="createId != null"> and em.create_id = #{createId}</if>
            <if test="licence != null and licence != ''"> and mi.licence like concat('%', #{licence}, '%')</if>
            <if test="merchantName != null and merchantName != ''"> and mi.merchant_name = like concat('%', #{merchantName}, '%')</if>
            <if test="createTimeStr != null  and createTimeStr != ''"> and em.create_time = #{createTimeStr}</if>
        </where>
    </select>
    
    <select id="getCountUnRead" parameterType="Long" resultType="Integer">
        select count(1) from examination_message
        where status = '1'
        <if test="createId != null"> and create_id = #{createId}</if>
    </select>

    <select id="selectExaminationMessageByMsgId" parameterType="Long" resultMap="ExaminationMessageResult">
        <include refid="selectExaminationMessageVo"/>
        where msg_id = #{msgId}
    </select>

    <insert id="insertExaminationMessage" parameterType="ExaminationMessage" useGeneratedKeys="true" keyProperty="msgId">
        insert into examination_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="monthlyAssessmentsId != null">monthly_assessments_id,</if>
            <if test="paperId != null">paper_id,</if>
            <if test="merchantId != null">merchant_id,</if>
            <if test="msgTitle != null">msg_title,</if>
            <if test="msgType != null">msg_type,</if>
            <if test="msgContent != null">msg_content,</if>
            <if test="msgImage != null">msg_image,</if>
            <if test="msgReply != null">msg_reply,</if>
            <if test="status != null">status,</if>
            <if test="receiverId != null">receiver_id,</if>
            <if test="createId != null">create_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="monthlyAssessmentsId != null">#{monthlyAssessmentsId},</if>
            <if test="paperId != null">#{paperId},</if>
            <if test="merchantId != null">#{merchantId},</if>
            <if test="msgTitle != null">#{msgTitle},</if>
            <if test="msgType != null">#{msgType},</if>
            <if test="msgContent != null">#{msgContent},</if>
            <if test="msgImage != null">#{msgImage},</if>
            <if test="msgReply != null">#{msgReply},</if>
            <if test="status != null">#{status},</if>
            <if test="receiverId != null">#{receiverId},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateExaminationMessage" parameterType="ExaminationMessage">
        update examination_message
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="monthlyAssessmentsId != null">monthly_assessments_id = #{monthlyAssessmentsId},</if>
            <if test="paperId != null">paper_id = #{paperId},</if>
            <if test="merchantId != null">merchant_id = #{merchantId},</if>
            <if test="msgTitle != null">msg_title = #{msgTitle},</if>
            <if test="msgType != null">msg_type = #{msgType},</if>
            <if test="msgContent != null">msg_content = #{msgContent},</if>
            <if test="msgImage != null">msg_image = #{msgImage},</if>
            <if test="msgReply != null">msg_reply = #{msgReply},</if>
            <if test="status != null">status = #{status},</if>
            <if test="receiverId != null">receiver_id = #{receiverId},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where msg_id = #{msgId}
    </update>

    <delete id="deleteExaminationMessageByMsgId" parameterType="Long">
        delete from examination_message where msg_id = #{msgId}
    </delete>

    <delete id="deleteExaminationMessageByMsgIds" parameterType="String">
        delete from examination_message where msg_id in 
        <foreach item="msgId" collection="array" open="(" separator="," close=")">
            #{msgId}
        </foreach>
    </delete>
</mapper>
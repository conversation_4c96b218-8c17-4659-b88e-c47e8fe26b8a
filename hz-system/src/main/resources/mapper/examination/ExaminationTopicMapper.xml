<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hz.examination.mapper.ExaminationTopicMapper">
    
    <resultMap type="ExaminationTopic" id="ExaminationTopicResult">
        <result property="id"    column="id"    />
        <result property="paperId"    column="paper_id"    />
        <result property="num"    column="num"    />
        <result property="title"    column="title"    />
        <result property="isRequired"    column="is_required"    />
        <result property="prompt"    column="prompt"    />
        <result property="majorType"    column="major_type"    />
        <result property="majorTypeName"    column="major_type_name"    />
        <result property="smallType"    column="small_type"    />
        <result property="smallTypeName"    column="small_type_name"    />
        <result property="sort"    column="sort"    />
        <result property="createId"    column="create_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="remark"    column="remark"    />
        <collection property="examinationReplyList"   javaType="java.util.List"  resultMap="ExaminationReplyResult" />
        <collection property="examinationReplyMerchantList"   javaType="java.util.List"  resultMap="ExaminationReplyMerchantResult" />
    </resultMap>

    <resultMap id="ExaminationReplyResult" type="ExaminationReply">
        <result property="id"    column="er_id"    />
        <result property="paperId"    column="er_paper_id"    />
        <result property="topicNum"    column="er_topic_num"    />
        <result property="name"    column="er_name"    />
        <result property="score"    column="er_score"    />
        <result property="sort"    column="er_sort"    />
        <result property="createId"    column="er_create_id"    />
        <result property="createBy"    column="er_create_by"    />
        <result property="createTime"    column="er_create_time"    />
        <result property="remark"    column="er_remark"    />
    </resultMap>

    <resultMap id="ExaminationReplyMerchantResult" type="ExaminationReplyMerchant">
        <result property="id"    column="erm_id"    />
        <result property="paperId"    column="erm_paper_id"    />
        <result property="merchantId"    column="erm_merchant_id"    />
        <result property="topicNum"    column="erm_topic_num"    />
        <result property="isAnswer"    column="erm_is_answer"    />
        <result property="name"    column="erm_name"    />
        <result property="blanks"    column="erm_blanks"    />
        <result property="score"    column="erm_score"    />
        <result property="sort"    column="erm_sort"    />
        <result property="createId"    column="erm_create_id"    />
        <result property="createBy"    column="erm_create_by"    />
        <result property="createTime"    column="erm_create_time"    />
        <result property="remark"    column="erm_remark"    />
    </resultMap>

    <sql id="selectExaminationTopicVo">
        select id, paper_id, num, title, is_required, prompt, major_type, major_type_name, small_type, small_type_name, sort, create_id, create_by, create_time, remark from examination_topic
    </sql>

    <select id="selectExaminationTopicList" parameterType="ExaminationTopic" resultMap="ExaminationTopicResult">
        <include refid="selectExaminationTopicVo"/>
        <where>  
            <if test="paperId != null "> and paper_id = #{paperId}</if>
            <if test="num != null "> and num = #{num}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="isRequired != null  and isRequired != ''"> and is_required = #{isRequired}</if>
            <if test="prompt != null  and prompt != ''"> and prompt = #{prompt}</if>
            <if test="majorType != null  and majorType != ''"> and major_type = #{majorType}</if>
            <if test="majorTypeName != null  and majorTypeName != ''"> and major_type_name like concat('%', #{majorTypeName}, '%')</if>
            <if test="smallType != null  and smallType != ''"> and small_type = #{smallType}</if>
            <if test="smallTypeName != null  and smallTypeName != ''"> and small_type_name like concat('%', #{smallTypeName}, '%')</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>
    
    <select id="selectExaminationTopicById" parameterType="Long" resultMap="ExaminationTopicResult">
        <include refid="selectExaminationTopicVo"/>
        where id = #{id}
    </select>

    <select id="selectExaminationTopicByPaperId" parameterType="Long" resultMap="ExaminationTopicResult">
        select et.id, et.paper_id, et.num, et.title, et.is_required, et.prompt, et.major_type, et.major_type_name, et.small_type, et.small_type_name, et.sort, et.create_id, et.create_by, et.create_time, et.remark,
            er.id er_id, er.paper_id er_paper_id, er.topic_num er_topic_num, er.name er_name, er.score er_score, er.sort er_sort, er.create_id er_create_id, er.create_by er_create_by, er.create_time er_create_time, er.remark er_remark
        from examination_topic et left join examination_reply er on er.paper_id = et.paper_id and er.topic_num = et.num
        where et.paper_id = #{paperId} order by et.sort,er.sort
    </select>

    <select id="selectExaminationTopicOfMerchantByPaperId" parameterType="Long" resultMap="ExaminationTopicResult">
        select et.id, et.paper_id, et.num, et.title, et.is_required, et.prompt, et.major_type, et.major_type_name, et.small_type, et.small_type_name, et.sort, et.create_id, et.create_by, et.create_time, et.remark,
            erm.id erm_id, erm.paper_id erm_paper_id, erm.merchant_id erm_merchant_id, erm.topic_num erm_topic_num, erm.is_answer erm_is_answer, erm.name erm_name, erm.blanks erm_blanks, erm.score erm_score, erm.sort erm_sort, erm.create_id erm_create_id, erm.create_by erm_create_by, erm.create_time erm_create_time, erm.remark erm_remark
        from examination_topic et left join examination_reply_merchant erm on erm.paper_id = et.paper_id and erm.topic_num = et.num and erm.merchant_id = #{merchantId}
        where et.paper_id = #{paperId} order by et.sort,erm.sort
    </select>

    <insert id="insertExaminationTopic" parameterType="ExaminationTopic" useGeneratedKeys="true" keyProperty="id">
        insert into examination_topic
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="paperId != null">paper_id,</if>
            <if test="num != null">num,</if>
            <if test="title != null">title,</if>
            <if test="isRequired != null">is_required,</if>
            <if test="prompt != null">prompt,</if>
            <if test="majorType != null">major_type,</if>
            <if test="majorTypeName != null">major_type_name,</if>
            <if test="smallType != null">small_type,</if>
            <if test="smallTypeName != null">small_type_name,</if>
            <if test="sort != null">sort,</if>
            <if test="createId != null">create_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="paperId != null">#{paperId},</if>
            <if test="num != null">#{num},</if>
            <if test="title != null">#{title},</if>
            <if test="isRequired != null">#{isRequired},</if>
            <if test="prompt != null">#{prompt},</if>
            <if test="majorType != null">#{majorType},</if>
            <if test="majorTypeName != null">#{majorTypeName},</if>
            <if test="smallType != null">#{smallType},</if>
            <if test="smallTypeName != null">#{smallTypeName},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="batchInsertExaminationTopic">
        insert into examination_topic( paper_id, num, title, is_required, prompt, major_type, major_type_name, small_type, small_type_name, sort, create_id, create_by, create_time, remark) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.paperId}, #{item.num}, #{item.title}, #{item.isRequired}, #{item.prompt}, #{item.majorType}, #{item.majorTypeName}, #{item.smallType}, #{item.smallTypeName}, #{item.sort}, #{item.createId}, #{item.createBy}, #{item.createTime}, #{item.remark})
        </foreach>
    </insert>

    <update id="updateExaminationTopic" parameterType="ExaminationTopic">
        update examination_topic
        <trim prefix="SET" suffixOverrides=",">
            <if test="paperId != null">paper_id = #{paperId},</if>
            <if test="num != null">num = #{num},</if>
            <if test="title != null">title = #{title},</if>
            <if test="isRequired != null">is_required = #{isRequired},</if>
            <if test="prompt != null">prompt = #{prompt},</if>
            <if test="majorType != null">major_type = #{majorType},</if>
            <if test="majorTypeName != null">major_type_name = #{majorTypeName},</if>
            <if test="smallType != null">small_type = #{smallType},</if>
            <if test="smallTypeName != null">small_type_name = #{smallTypeName},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExaminationTopicById" parameterType="Long">
        delete from examination_topic where id = #{id}
    </delete>

    <delete id="batchDeleteExaminationTopicById" parameterType="Long">
        delete from examination_topic where paper_id = #{paperId}
    </delete>

    <delete id="deleteExaminationTopicByIds" parameterType="String">
        delete from examination_topic where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
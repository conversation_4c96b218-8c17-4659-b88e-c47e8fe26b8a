<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hz.examination.mapper.ExaminationReplyMapper">
    
    <resultMap type="ExaminationReply" id="ExaminationReplyResult">
        <result property="id"    column="id"    />
        <result property="paperId"    column="paper_id"    />
        <result property="topicNum"    column="topic_num"    />
        <result property="name"    column="name"    />
        <result property="score"    column="score"    />
        <result property="sort"    column="sort"    />
        <result property="createId"    column="create_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectExaminationReplyVo">
        select id, paper_id, topic_num, name, score, sort, create_id, create_by, create_time, remark from examination_reply
    </sql>

    <select id="selectExaminationReplyList" parameterType="ExaminationReply" resultMap="ExaminationReplyResult">
        <include refid="selectExaminationReplyVo"/>
        <where>  
            <if test="paperId != null "> and paper_id = #{paperId}</if>
            <if test="topicNum != null "> and topic_num = #{topicNum}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="score != null "> and score = #{score}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>
    
    <select id="selectExaminationReplyById" parameterType="Long" resultMap="ExaminationReplyResult">
        <include refid="selectExaminationReplyVo"/>
        where id = #{id}
    </select>

    <insert id="insertExaminationReply" parameterType="ExaminationReply" useGeneratedKeys="true" keyProperty="id">
        insert into examination_reply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="paperId != null">paper_id,</if>
            <if test="topicNum != null">topic_num,</if>
            <if test="name != null">name,</if>
            <if test="score != null">score,</if>
            <if test="sort != null">sort,</if>
            <if test="createId != null">create_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="paperId != null">#{paperId},</if>
            <if test="topicNum != null">#{topicNum},</if>
            <if test="name != null">#{name},</if>
            <if test="score != null">#{score},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="batchInsertExaminationReply">
        insert into examination_reply( paper_id, topic_num, name, score, sort, create_id, create_by, create_time, remark) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.paperId}, #{item.topicNum}, #{item.name}, #{item.score}, #{item.sort}, #{item.createId}, #{item.createBy}, #{item.createTime}, #{item.remark})
        </foreach>
    </insert>

    <update id="updateExaminationReply" parameterType="ExaminationReply">
        update examination_reply
        <trim prefix="SET" suffixOverrides=",">
            <if test="paperId != null">paper_id = #{paperId},</if>
            <if test="topicNum != null">topic_num = #{topicNum},</if>
            <if test="name != null">name = #{name},</if>
            <if test="score != null">score = #{score},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExaminationReplyById" parameterType="Long">
        delete from examination_reply where id = #{id}
    </delete>

    <delete id="batchDeleteExaminationReplyById" parameterType="Long">
        delete from examination_reply where paper_id = #{paperId}
    </delete>

    <delete id="deleteExaminationReplyByIds" parameterType="String">
        delete from examination_reply where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
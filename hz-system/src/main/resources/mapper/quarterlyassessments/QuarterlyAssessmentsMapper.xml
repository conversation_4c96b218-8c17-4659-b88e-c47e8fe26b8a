<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hz.quarterlyassessments.mapper.QuarterlyAssessmentsMapper">
    
    <resultMap type="QuarterlyAssessments" id="QuarterlyAssessmentsResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="year"    column="year"    />
        <result property="quarter"    column="quarter"    />
        <result property="status"    column="status"    />
        <result property="plannerUserId"    column="planner_user_id"    />
        <result property="createId"    column="create_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <resultMap id="QuarterlyAssessmentsMonthlyAssessmentsResult" type="QuarterlyAssessments" extends="QuarterlyAssessmentsResult">
        <collection property="monthlyAssessmentsList" ofType="MonthlyAssessments" column="id" select="selectMonthlyAssessmentsList" />
    </resultMap>

    <resultMap type="MonthlyAssessments" id="MonthlyAssessmentsResult">
        <result property="id"    column="id"    />
        <result property="mId"    column="m_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="quarterlyAssessmentId"    column="quarterly_assessment_id"    />
        <result property="name"    column="name"    />
        <result property="month"    column="month"    />
        <result property="assessmentType"    column="assessment_type"    />
        <result property="district"    column="district"    />
        <result property="inspectedHouseholds"    column="inspected_households"    />
        <result property="status"    column="status"    />
        <result property="createId"    column="create_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
        <result property="jsonData"    column="json_data"    />
        <result property="kaoheDate"    column="kaohe_date"    />
        <result property="year"    column="year"    />
    </resultMap>

    <resultMap id="MonthlyAssessmentsStaffResult" type="MonthlyAssessments" extends="MonthlyAssessmentsResult">
        <collection property="staffList" ofType="MonthlyAssessmentStaff" column="id" select="selectMonthlyAssessmentStaffList" />
    </resultMap>

    <resultMap type="MonthlyAssessmentStaff" id="MonthlyAssessmentStaffSimpleResult">
        <result property="id"    column="staff_id"    />
        <result property="monthlyAssessmentId"    column="monthly_assessment_id"    />
        <result property="userId"    column="user_id"    />
        <result property="roleId"    column="role_id"    />
        <result property="createdAt"    column="staff_created_at"    />
        <result property="updatedAt"    column="staff_updated_at"    />
        <result property="userName"    column="user_name"    />
        <result property="roleName"    column="role_name"    />
    </resultMap>

    <sql id="selectQuarterlyAssessmentsVo">
        select id, name, year, quarter, status, planner_user_id, create_id, create_by, create_time, created_at, updated_at from quarterly_assessments
    </sql>

    <select id="selectQuarterlyAssessmentsList" parameterType="QuarterlyAssessments" resultMap="QuarterlyAssessmentsResult">
        <include refid="selectQuarterlyAssessmentsVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="year != null  and year != ''"> and year = #{year}</if>
            <if test="quarter != null "> and quarter = #{quarter}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="plannerUserId != null  and plannerUserId != ''"> and planner_user_id = #{plannerUserId}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
        </where>
    </select>
    


    <select id="selectQuarterlyAssessmentsById" parameterType="String" resultMap="QuarterlyAssessmentsMonthlyAssessmentsResult">
        select id, name, year, quarter, status, planner_user_id, create_id, create_by, create_time, created_at, updated_at
        from quarterly_assessments
        where id = #{id}
    </select>

    <select id="selectMonthlyAssessmentsList" resultMap="MonthlyAssessmentsStaffResult">
        select ma.id, ma.m_id, ma.dept_id, ma.quarterly_assessment_id, ma.name, ma.month, ma.assessment_type, ma.district,
               ma.inspected_households, ma.status, ma.create_id, ma.create_time, ma.created_at, ma.updated_at, ma.json_data, ma.kaohe_date,
               u.nick_name as create_by
        from monthly_assessments ma
        left join sys_user u on ma.create_id = u.user_id
        where ma.quarterly_assessment_id = #{quarterly_assessment_id}
        order by ma.month,ma.dept_id
    </select>

    <select id="selectMonthlyAssessmentStaffList" resultMap="MonthlyAssessmentStaffSimpleResult">
        select mas.id as staff_id, mas.monthly_assessment_id, mas.user_id, mas.role_id, 
               mas.created_at as staff_created_at, mas.updated_at as staff_updated_at,
               u.nick_name as user_name, r.role_name
        from monthly_assessment_staff mas
        left join sys_user u on mas.user_id = u.user_id
        left join sys_role r on mas.role_id = r.role_id
        where mas.monthly_assessment_id = #{id}
        order by r.role_sort
    </select>

    <select id="selectMonthlyAssessmentsByQuarterlyId" parameterType="String" resultMap="MonthlyAssessmentsResult">
        select id, quarterly_assessment_id, name, month, assessment_type, district, 
               inspected_households, status, create_id, create_by, create_time, created_at, updated_at, json_data
        from monthly_assessments
        where quarterly_assessment_id = #{quarterlyAssessmentId}
        order by month
    </select>

    <insert id="insertQuarterlyAssessments" parameterType="QuarterlyAssessments" useGeneratedKeys="true" keyProperty="id">
        insert into quarterly_assessments
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="year != null and year != ''">year,</if>
            <if test="quarter != null">quarter,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="plannerUserId != null">planner_user_id,</if>
            <if test="createId != null">create_id,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="year != null and year != ''">#{year},</if>
            <if test="quarter != null">#{quarter},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="plannerUserId != null">#{plannerUserId},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateQuarterlyAssessments" parameterType="QuarterlyAssessments">
        update quarterly_assessments
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="year != null and year != ''">year = #{year},</if>
            <if test="quarter != null">quarter = #{quarter},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="plannerUserId != null">planner_user_id = #{plannerUserId},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQuarterlyAssessmentsById" parameterType="String">
        delete from quarterly_assessments where id = #{id}
    </delete>

    <delete id="deleteQuarterlyAssessmentsByIds" parameterType="String">
        delete from quarterly_assessments where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteMonthlyAssessmentsByQuarterlyAssessmentIds" parameterType="String">
        delete from monthly_assessments where quarterly_assessment_id in 
        <foreach item="quarterlyAssessmentId" collection="array" open="(" separator="," close=")">
            #{quarterlyAssessmentId}
        </foreach>
    </delete>

    <delete id="deleteMonthlyAssessmentsByQuarterlyAssessmentId" parameterType="String">
        delete from monthly_assessments where quarterly_assessment_id = #{quarterlyAssessmentId}
    </delete>

    <insert id="batchMonthlyAssessments">
        insert into monthly_assessments( id, dept_id, quarterly_assessment_id, name, month, assessment_type, district, inspected_households, status, create_id, create_by, create_time, created_at, updated_at, json_data) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.deptId}, #{item.quarterlyAssessmentId}, #{item.name}, #{item.month}, #{item.assessmentType}, #{item.district}, #{item.inspectedHouseholds}, #{item.status}, #{item.createId}, #{item.createBy}, #{item.createTime}, #{item.createdAt}, #{item.updatedAt}, #{item.jsonData})
        </foreach>
    </insert>

    <insert id="batchMonthlyAssessmentStaff">
        insert into monthly_assessment_staff(id, monthly_assessment_id, user_id, role_id, created_at, updated_at) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id}, #{item.monthlyAssessmentId}, #{item.userId}, #{item.roleId}, #{item.createdAt}, #{item.updatedAt})
        </foreach>
    </insert>

    <delete id="deleteMonthlyAssessmentStaffByMonthlyAssessmentId" parameterType="String">
        delete from monthly_assessment_staff where monthly_assessment_id = #{monthlyAssessmentId}
    </delete>

    <delete id="deleteMonthlyAssessmentStaffByQuarterlyAssessmentIds" parameterType="String">
        delete from monthly_assessment_staff where monthly_assessment_id in (
            select id from monthly_assessments where quarterly_assessment_id in 
            <foreach item="quarterlyAssessmentId" collection="array" open="(" separator="," close=")">
                #{quarterlyAssessmentId}
            </foreach>
        )
    </delete>

    <select id="checkYearQuarterExists" resultMap="QuarterlyAssessmentsResult">
        <include refid="selectQuarterlyAssessmentsVo"/>
        where year = #{year} and quarter = #{quarter}
        limit 1
    </select>

    <select id="selectMonthlyAssessmentWithStaffById" parameterType="Long" resultMap="MonthlyAssessmentsStaffResult">
        select ma.id, ma.m_id, ma.dept_id, ma.quarterly_assessment_id, ma.name, ma.month, ma.assessment_type,
               ma.district, ma.inspected_households, ma.status, ma.create_id, ma.create_by,
               ma.create_time, ma.created_at, ma.updated_at, ma.json_data
        from monthly_assessments ma
        where ma.id = #{monthlyAssessmentId}
    </select>
    
    <update id="updateMonthlyAssessments" parameterType="MonthlyAssessments">
        update monthly_assessments
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="assessmentType != null and assessmentType != ''">assessment_type = #{assessmentType},</if>
            <if test="district != null and district != ''">district = #{district},</if>
            <if test="inspectedHouseholds != null">inspected_households = #{inspectedHouseholds},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="jsonData != null">json_data = #{jsonData},</if>
            <if test="kaoheDate != null">kaohe_date = #{kaoheDate},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateMonthlyAssessmentsByMid" parameterType="MonthlyAssessments">
        update monthly_assessments
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="assessmentType != null and assessmentType != ''">assessment_type = #{assessmentType},</if>
            <if test="district != null and district != ''">district = #{district},</if>
            <if test="inspectedHouseholds != null">inspected_households = #{inspectedHouseholds},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="jsonData != null">json_data = #{jsonData},</if>
            <if test="kaoheDate != null">kaohe_date = #{kaoheDate},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where m_id = #{mId}
    </update>

    <update id="release">
        update monthly_assessments set status = #{status} where id = #{monthlyId}
    </update>

    <select id="selectMonthlyAssessmentsWithStaffList" parameterType="MonthlyAssessments" resultMap="MonthlyAssessmentsStaffResult">
        select ma.id, ma.quarterly_assessment_id, ma.name, ma.month, ma.assessment_type, 
               ma.district, ma.inspected_households, ma.status, ma.create_id, ma.create_by, 
               ma.create_time, ma.created_at, ma.updated_at, ma.json_data,
               u.nick_name as create_by, qa.year
        from monthly_assessments ma
        left join sys_user u on ma.create_id = u.user_id
        left join quarterly_assessments qa on ma.quarterly_assessment_id = qa.id
        <where>
            <if test="name != null and name != ''">and ma.name like concat('%', #{name}, '%')</if>
            <if test="month != null">and ma.month = #{month}</if>
            <if test="assessmentType != null and assessmentType != ''">and ma.assessment_type = #{assessmentType}</if>
            <if test="district != null and district != ''">and ma.district = #{district}</if>
            <if test="status != null and status != ''">and ma.status = #{status}</if>
            <if test="quarterlyAssessmentId != null and quarterlyAssessmentId != ''">and ma.quarterly_assessment_id = #{quarterlyAssessmentId}</if>
        </where>
        order by ma.month desc, ma.created_at desc
    </select>

    <select id="selectMonthlyAssessmentsWithStaffListByUser" resultMap="MonthlyAssessmentsStaffResult">
        select ma.id, ma.quarterly_assessment_id, ma.name, ma.month, ma.assessment_type, 
               ma.district, ma.inspected_households, ma.status, ma.create_id, 
               ma.create_time, ma.created_at, ma.updated_at, ma.json_data,
               u.nick_name as create_by, qa.year
        from monthly_assessments ma
        left join sys_user u on ma.create_id = u.user_id
        left join quarterly_assessments qa on ma.quarterly_assessment_id = qa.id
        inner join monthly_assessment_staff mas on ma.id = mas.monthly_assessment_id
        inner join sys_user su on mas.user_id = su.user_id
        where ma.status != 1 and su.user_name = #{userName}
        <if test="monthlyAssessments.name != null and monthlyAssessments.name != ''">and ma.name like concat('%', #{monthlyAssessments.name}, '%')</if>
        <if test="monthlyAssessments.month != null">and ma.month = #{monthlyAssessments.month}</if>
        <if test="monthlyAssessments.assessmentType != null and monthlyAssessments.assessmentType != ''">and ma.assessment_type = #{monthlyAssessments.assessmentType}</if>
        <if test="monthlyAssessments.district != null and monthlyAssessments.district != ''">and ma.district = #{monthlyAssessments.district}</if>
        <if test="monthlyAssessments.status != null and monthlyAssessments.status != ''">and ma.status = #{monthlyAssessments.status}</if>
        <if test="monthlyAssessments.quarterlyAssessmentId != null and monthlyAssessments.quarterlyAssessmentId != ''">and ma.quarterly_assessment_id = #{monthlyAssessments.quarterlyAssessmentId}</if>
        group by ma.id, ma.quarterly_assessment_id, ma.name, ma.month, ma.assessment_type, 
                 ma.district, ma.inspected_households, ma.status, ma.create_id, 
                 ma.create_time, ma.created_at, ma.updated_at, ma.json_data, u.nick_name, qa.year
                 order by qa.year desc, ma.month desc, ma.created_at desc
    </select>
</mapper>
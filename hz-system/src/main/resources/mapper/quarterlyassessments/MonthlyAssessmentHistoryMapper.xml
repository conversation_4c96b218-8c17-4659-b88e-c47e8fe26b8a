<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hz.quarterlyassessments.mapper.MonthlyAssessmentHistoryMapper">
    
    <resultMap type="MonthlyAssessmentHistory" id="MonthlyAssessmentHistoryResult">
        <result property="id"                     column="id"                     />
        <result property="userId"                 column="user_id"                />
        <result property="roleId"                 column="role_id"                />
        <result property="monthlyAssessmentId"    column="monthly_assessment_id"  />
        <result property="year"                   column="year"                   />
        <result property="halfYear"               column="half_year"              />
        <result property="signatureStatus"       column="signature_status"       />
        <result property="createdAt"              column="created_at"             />
    </resultMap>

    <sql id="selectMonthlyAssessmentHistoryVo">
        select id, user_id, role_id, monthly_assessment_id, year, half_year, signature_status, created_at 
        from monthly_assessment_history
    </sql>

    <!-- 查询已被抽中的用户ID列表 -->
    <select id="selectAssignedUserIds" parameterType="map" resultType="Long">
        select distinct user_id 
        from monthly_assessment_history 
        where year = #{year} 
        and role_id = #{roleId}
        <if test="halfYear != null and halfYear != ''">
            and half_year = #{halfYear}
        </if>
    </select>

    <!-- 查询已被抽中的用户ID列表（排除指定月度考核） -->
    <select id="selectAssignedUserIdsExclude" parameterType="map" resultType="Long">
        select distinct user_id 
        from monthly_assessment_history 
        where year = #{year} 
        and role_id = #{roleId}
        <if test="halfYear != null and halfYear != ''">
            and half_year = #{halfYear}
        </if>
        <if test="excludeMonthlyAssessmentId != null">
            and monthly_assessment_id != #{excludeMonthlyAssessmentId}
        </if>
    </select>

    <!-- 查询月度考核抽取历史记录列表 -->
    <select id="selectMonthlyAssessmentHistoryList" parameterType="MonthlyAssessmentHistory" resultMap="MonthlyAssessmentHistoryResult">
        <include refid="selectMonthlyAssessmentHistoryVo"/>
        <where>  
            <if test="userId != null">and user_id = #{userId}</if>
            <if test="roleId != null">and role_id = #{roleId}</if>
            <if test="monthlyAssessmentId != null and monthlyAssessmentId != ''">and monthly_assessment_id = #{monthlyAssessmentId}</if>
            <if test="year != null and year != ''">and year = #{year}</if>
            <if test="halfYear != null and halfYear != ''">and half_year = #{halfYear}</if>
            <if test="signatureStatus != null and signatureStatus != ''">and signature_status = #{signatureStatus}</if>
        </where>
        order by created_at desc
    </select>

    <!-- 新增月度考核抽取历史记录 -->
    <insert id="insertMonthlyAssessmentHistory" parameterType="MonthlyAssessmentHistory">
        insert into monthly_assessment_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="roleId != null">role_id,</if>
            <if test="monthlyAssessmentId != null">monthly_assessment_id,</if>
            <if test="year != null">year,</if>
            <if test="halfYear != null">half_year,</if>
            <if test="signatureStatus != null">signature_status,</if>
            <if test="createdAt != null">created_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="roleId != null">#{roleId},</if>
            <if test="monthlyAssessmentId != null">#{monthlyAssessmentId},</if>
            <if test="year != null">#{year},</if>
            <if test="halfYear != null">#{halfYear},</if>
            <if test="signatureStatus != null">#{signatureStatus},</if>
            <if test="createdAt != null">#{createdAt},</if>
         </trim>
    </insert>

    <!-- 根据月度考核ID删除历史记录 -->
    <delete id="deleteByMonthlyAssessmentId" parameterType="Long">
        delete from monthly_assessment_history where monthly_assessment_id = #{monthlyAssessmentId}
    </delete>

    <!-- 根据季度考核ID删除历史记录 -->
    <delete id="deleteByQuarterlyAssessmentId" parameterType="String">
        delete from monthly_assessment_history 
        where monthly_assessment_id in (
            select id from monthly_assessments where quarterly_assessment_id = #{quarterlyAssessmentId}
        )
    </delete>



    <!-- 根据用户ID、月度考核ID和年份查询历史记录 -->
    <select id="selectByUserIdAndMonthlyAssessmentIdAndYear" resultMap="MonthlyAssessmentHistoryResult">
        <include refid="selectMonthlyAssessmentHistoryVo"/>
        where user_id = #{userId} 
        and monthly_assessment_id = #{monthlyAssessmentId} 
        and year = #{year}
        limit 1
    </select>

    <!-- 根据用户ID、月度考核ID和年份更新签字状态 -->
    <update id="updateSignatureStatusByUserIdAndMonthlyAssessmentIdAndYear">
        update monthly_assessment_history
        set signature_status = #{signatureStatus}
        where user_id = #{userId} 
        and monthly_assessment_id = #{monthlyAssessmentId} 
        and year = #{year}
    </update>

    <!-- 统计指定月度考核和年份的已签字人数 -->
    <select id="countSignedByMonthlyAssessmentIdAndYear" resultType="int">
        select count(*) 
        from monthly_assessment_history 
        where monthly_assessment_id = #{monthlyAssessmentId} 
        and year = #{year}
        and signature_status = '1'
    </select>

    <!-- 统计指定月度考核的应该签字的总人数（从monthly_assessment_staff表） -->
    <select id="countTotalStaffByMonthlyAssessmentId" resultType="int">
        select count(*) 
        from monthly_assessment_history
        where monthly_assessment_id = #{monthlyAssessmentId}
    </select>

    <!-- 根据用户ID和月度考核ID查询历史记录（不依赖年份） -->
    <select id="selectByUserIdAndMonthlyAssessmentId" resultMap="MonthlyAssessmentHistoryResult">
        <include refid="selectMonthlyAssessmentHistoryVo"/>
        where user_id = #{userId} 
        and monthly_assessment_id = #{monthlyAssessmentId}
        limit 1
    </select>

    <!-- 根据用户ID和月度考核ID更新签字状态（不依赖年份） -->
    <update id="updateSignatureStatusByUserIdAndMonthlyAssessmentId">
        update monthly_assessment_history
        set signature_status = #{signatureStatus}
        where user_id = #{userId} 
        and monthly_assessment_id = #{monthlyAssessmentId}
    </update>

    <!-- 统计指定月度考核的已签字人数（不依赖年份） -->
    <select id="countSignedByMonthlyAssessmentId" resultType="int">
        select count(*) 
        from monthly_assessment_history 
        where monthly_assessment_id = #{monthlyAssessmentId} 
        and signature_status = '1'
    </select>

    <!-- 根据月度考核ID和角色ID删除历史记录 -->
    <delete id="deleteByMonthlyAssessmentIdAndRoleId">
        delete from monthly_assessment_history 
        where monthly_assessment_id = #{monthlyAssessmentId} 
        and role_id = #{roleId}
    </delete>

</mapper> 
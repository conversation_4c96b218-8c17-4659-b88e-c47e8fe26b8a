<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hz.quartz.mapper.SysJobMapper">

	<resultMap type="SysJob" id="SysJobResult">
		<id     property="jobId"          column="job_id"          />
		<result property="jobName"        column="job_name"        />
		<result property="jobGroup"       column="job_group"       />
		<result property="invokeTarget"   column="invoke_target"   />
		<result property="cronExpression" column="cron_expression" />
		<result property="misfirePolicy"  column="misfire_policy"  />
		<result property="concurrent"     column="concurrent"      />
		<result property="status"         column="status"          />
		<result property="createBy"       column="create_by"       />
		<result property="createTime"     column="create_time"     />
		<result property="updateBy"       column="update_by"       />
		<result property="updateTime"     column="update_time"     />
		<result property="remark"         column="remark"          />
	</resultMap>
	
	<sql id="selectJobVo">
        select job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark 
		from sys_job
    </sql>
	
	<select id="selectJobList" parameterType="SysJob" resultMap="SysJobResult">
		<include refid="selectJobVo"/>
		<where>
			<if test="jobName != null and jobName != ''">
				AND job_name like concat('%', #{jobName}, '%')
			</if>
			<if test="jobGroup != null and jobGroup != ''">
				AND job_group = #{jobGroup}
			</if>
			<if test="status != null and status != ''">
				AND status = #{status}
			</if>
			<if test="invokeTarget != null and invokeTarget != ''">
				AND invoke_target like concat('%', #{invokeTarget}, '%')
			</if>
		</where>
	</select>
	
	<select id="selectJobAll" resultMap="SysJobResult">
		<include refid="selectJobVo"/>
	</select>
	
	<select id="selectJobById" parameterType="Long" resultMap="SysJobResult">
		<include refid="selectJobVo"/>
		where job_id = #{jobId}
	</select>
	
	<delete id="deleteJobById" parameterType="Long">
 		delete from sys_job where job_id = #{jobId}
 	</delete>
 	
 	<delete id="deleteJobByIds" parameterType="Long">
 		delete from sys_job where job_id in
 		<foreach collection="array" item="jobId" open="(" separator="," close=")">
 			#{jobId}
        </foreach> 
 	</delete>
 	
 	<update id="updateJob" parameterType="SysJob">
 		update sys_job
 		<set>
 			<if test="jobName != null and jobName != ''">job_name = #{jobName},</if>
 			<if test="jobGroup != null and jobGroup != ''">job_group = #{jobGroup},</if>
 			<if test="invokeTarget != null and invokeTarget != ''">invoke_target = #{invokeTarget},</if>
 			<if test="cronExpression != null and cronExpression != ''">cron_expression = #{cronExpression},</if>
 			<if test="misfirePolicy != null and misfirePolicy != ''">misfire_policy = #{misfirePolicy},</if>
 			<if test="concurrent != null and concurrent != ''">concurrent = #{concurrent},</if>
 			<if test="status !=null">status = #{status},</if>
 			<if test="remark != null and remark != ''">remark = #{remark},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			update_time = sysdate()
 		</set>
 		where job_id = #{jobId}
	</update>
 	
 	<insert id="insertJob" parameterType="SysJob" useGeneratedKeys="true" keyProperty="jobId">
 		insert into sys_job(
 			<if test="jobId != null and jobId != 0">job_id,</if>
 			<if test="jobName != null and jobName != ''">job_name,</if>
 			<if test="jobGroup != null and jobGroup != ''">job_group,</if>
 			<if test="invokeTarget != null and invokeTarget != ''">invoke_target,</if>
 			<if test="cronExpression != null and cronExpression != ''">cron_expression,</if>
 			<if test="misfirePolicy != null and misfirePolicy != ''">misfire_policy,</if>
 			<if test="concurrent != null and concurrent != ''">concurrent,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			create_time
 		)values(
 			<if test="jobId != null and jobId != 0">#{jobId},</if>
 			<if test="jobName != null and jobName != ''">#{jobName},</if>
 			<if test="jobGroup != null and jobGroup != ''">#{jobGroup},</if>
 			<if test="invokeTarget != null and invokeTarget != ''">#{invokeTarget},</if>
 			<if test="cronExpression != null and cronExpression != ''">#{cronExpression},</if>
 			<if test="misfirePolicy != null and misfirePolicy != ''">#{misfirePolicy},</if>
 			<if test="concurrent != null and concurrent != ''">#{concurrent},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			sysdate()
 		)
	</insert>

</mapper> 
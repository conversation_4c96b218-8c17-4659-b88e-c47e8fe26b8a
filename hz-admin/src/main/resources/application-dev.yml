# 项目相关配置
hz:
  # 名称
  name: HZ
  # 版本
  version: 3.8.9
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/hz/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /Users/<USER>/ruoyi/yc/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math
  # 打卡范围
  checkInRange: 50
  # 商户智能抽取配置
  merchant-selection:
    # 抽取总数
    total-count: 30
    # 算法执行次数（蒙特卡洛模拟次数）
    simulation-iterations: 20
    # 评分权重：距离（归一化后）
    score-weight-distance: 0.5
    # 评分权重：时间
    score-weight-time: 0.5
    # 配额配置
    quota:
      # 农村非加盟商户：固定数量
      rural-non-affiliate-fixed: 6
      # 农村加盟商户：随机范围（0到此值-1）
      rural-affiliate-random-bound: 7
      # 城镇商户总数：随机范围上限（0到此值）
      urban-total-random-bound: 18
      # 城镇加盟商户：随机范围（0到此值-1）
      urban-affiliate-random-bound: 7

# 数据源配置
spring:
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: *************************************************************************************************************************************************
        username: root
        password: Mysql@#123
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
  data:
    # redis 配置
    redis:
      # 地址
      host: ************
      # 端口，默认为6379
      port: 6379
      # 数据库索引
      database: 0
      # 密码
      password: redis123



# 日志配置
logging:
  level:
    com.hz: debug
    org.springframework: warn
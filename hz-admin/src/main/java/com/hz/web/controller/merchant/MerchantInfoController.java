package com.hz.web.controller.merchant;

import com.hz.common.annotation.Log;
import com.hz.common.core.controller.BaseController;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.core.page.TableDataInfo;
import com.hz.common.enums.BusinessType;
import com.hz.common.utils.poi.ExcelUtil;
import com.hz.merchant.domain.MerchantInfo;
import com.hz.merchant.domain.MerchantSelectionRequest;
import com.hz.merchant.domain.MerchantSelectionResponse;
import com.hz.merchant.service.IMerchantInfoService;
import com.hz.merchant.service.IMerchantSelectionService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商户信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/merchant/info")
public class MerchantInfoController extends BaseController
{
    @Autowired
    private IMerchantInfoService merchantInfoService;
    
    @Autowired
    private IMerchantSelectionService merchantSelectionService;

    /**
     * 查询商户信息列表
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(MerchantInfo merchantInfo)
    {
        startPage();
        List<MerchantInfo> list = merchantInfoService.selectMerchantInfoList(merchantInfo);
        return getDataTable(list);
    }

    /**
     * 导出商户信息列表
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:export')")
    @Log(title = "商户信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MerchantInfo merchantInfo)
    {
        List<MerchantInfo> list = merchantInfoService.selectMerchantInfoList(merchantInfo);
        ExcelUtil<MerchantInfo> util = new ExcelUtil<MerchantInfo>(MerchantInfo.class);
        util.exportExcel(response, list, "商户信息数据");
    }

    /**
     * 获取商户信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(merchantInfoService.selectMerchantInfoById(id));
    }

    /**
     * 新增商户信息
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:add')")
    @Log(title = "商户信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MerchantInfo merchantInfo)
    {
        return toAjax(merchantInfoService.insertMerchantInfo(merchantInfo));
    }

    /**
     * 修改商户信息
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:edit')")
    @Log(title = "商户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MerchantInfo merchantInfo)
    {
        return toAjax(merchantInfoService.updateMerchantInfo(merchantInfo));
    }

    /**
     * 删除商户信息
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:remove')")
    @Log(title = "商户信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(merchantInfoService.deleteMerchantInfoByIds(ids));
    }

    /**
     * 切换商户状态
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:edit')")
    @Log(title = "商户状态切换", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody MerchantInfo merchantInfo)
    {
        return toAjax(merchantInfoService.updateMerchantStatus(merchantInfo));
    }

    /**
     * 商户抽取（智能抽取 + 条件抽取）
     * 根据是否有num参数来区分：
     * - 有num参数：条件抽取，根据指定条件抽取指定数量的商户
     * - 无num参数：智能抽取，使用智能算法抽取30个商户
     */
    @PreAuthorize("@ss.hasPermi('merchant:info:list')")
    @Log(title = "商户抽取", businessType = BusinessType.OTHER)
    @PostMapping("/selectMerchants")
    public AjaxResult selectMerchants(@RequestBody MerchantSelectionRequest request)
    {
        try {
            return success(merchantSelectionService.selectMerchants(request));
        } catch (Exception e) {
            logger.error("商户抽取失败", e);
            return error("商户抽取失败：" + e.getMessage());
        }
    }
}

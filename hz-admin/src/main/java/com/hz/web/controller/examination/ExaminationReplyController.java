package com.hz.web.controller.examination;

import java.util.List;

import com.hz.examination.domain.ExaminationReply;
import com.hz.examination.service.IExaminationReplyService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hz.common.annotation.Log;
import com.hz.common.core.controller.BaseController;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.enums.BusinessType;
import com.hz.common.utils.poi.ExcelUtil;
import com.hz.common.core.page.TableDataInfo;

/**
 * 考核题目回答Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/examination/reply")
public class ExaminationReplyController extends BaseController
{
    @Autowired
    private IExaminationReplyService examinationReplyService;

    /**
     * 查询考核题目回答列表
     */
    @PreAuthorize("@ss.hasPermi('examination:reply:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExaminationReply examinationReply)
    {
        startPage();
        List<ExaminationReply> list = examinationReplyService.selectExaminationReplyList(examinationReply);
        return getDataTable(list);
    }

    /**
     * 导出考核题目回答列表
     */
    @PreAuthorize("@ss.hasPermi('examination:reply:export')")
    @Log(title = "考核题目回答", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExaminationReply examinationReply)
    {
        List<ExaminationReply> list = examinationReplyService.selectExaminationReplyList(examinationReply);
        ExcelUtil<ExaminationReply> util = new ExcelUtil<ExaminationReply>(ExaminationReply.class);
        util.exportExcel(response, list, "考核题目回答数据");
    }

    /**
     * 获取考核题目回答详细信息
     */
    @PreAuthorize("@ss.hasPermi('examination:reply:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(examinationReplyService.selectExaminationReplyById(id));
    }

    /**
     * 新增考核题目回答
     */
    @PreAuthorize("@ss.hasPermi('examination:reply:add')")
    @Log(title = "考核题目回答", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExaminationReply examinationReply)
    {
        return toAjax(examinationReplyService.insertExaminationReply(examinationReply));
    }

    /**
     * 修改考核题目回答
     */
    @PreAuthorize("@ss.hasPermi('examination:reply:edit')")
    @Log(title = "考核题目回答", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExaminationReply examinationReply)
    {
        return toAjax(examinationReplyService.updateExaminationReply(examinationReply));
    }

    /**
     * 删除考核题目回答
     */
    @PreAuthorize("@ss.hasPermi('examination:reply:remove')")
    @Log(title = "考核题目回答", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(examinationReplyService.deleteExaminationReplyByIds(ids));
    }
}

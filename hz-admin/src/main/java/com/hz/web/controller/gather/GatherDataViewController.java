package com.hz.web.controller.gather;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.utils.CalUtils;
import com.hz.gather.bean.GatherPI;
import com.hz.gather.domain.*;
import com.hz.gather.service.*;
import com.hz.merchant.domain.MerchantInfo;
import com.hz.merchant.mapper.MerchantInfoMapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据大屏
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@RestController
@RequestMapping("/gather/dataView")
public class GatherDataViewController {

    @Resource
    private IGatherSurveyTaskService taskService;

    @Resource
    private IGatherSmokePriceService gatherSmokePriceService;

    @Resource
    private IGatherStPriceService gatherStPriceService;

    @Resource
    private IGatherStInventoryService gatherStInventoryService;

    @Resource
    private IGatherStLinkService gatherStLinkService;

    @Resource
    private MerchantInfoMapper merchantInfoMapper;

    /**
     * 卷烟价格（该次）
     *
     * @param classify      库存或者价格
     */
    @GetMapping(value = "/gaicSmokePrice")
    public AjaxResult gaicSmokePrice(String classify){
        // 结果
        Map<String, Object> map = new HashMap<>();
        // 获取任务
        List<GatherSurveyTask> gsts = taskService.selectList(new GatherSurveyTask());
        GatherSurveyTask gst = gsts.stream().filter(o -> o.getImportStatusPrice().equals("1") && o.getImportStatusInventory().equals("1")).findFirst().orElse(null);
        if(gst == null){
            return AjaxResult.success(map);
        }
        Long stId = gsts.get(0).getId();

        // 卷烟品规
        GatherSmokePrice search = new GatherSmokePrice();
        search.setClassify(classify);
        search.setStId(stId);
        List<GatherSmokePrice> list1 = gatherSmokePriceService.gaicSmokePrice(search);
        List<Long> spIds = list1.stream().map(GatherSmokePrice::getId).collect(Collectors.toList());
        List<GatherSmokePrice> list2 = gatherSmokePriceService.selectList(new GatherSmokePrice());
        list2 = list2.stream().filter(o -> !spIds.contains(o.getId())).collect(Collectors.toList());
        list1.addAll(list2);

        // 返回
        map.put("stId", stId);
        map.put("spDataList", list1);
        return AjaxResult.success(map);
    }

    /**
     * 该次的
     *
     * @param classify      库存或者价格
     * @param stId          任务id
     * @param spId          卷烟品规id
     * @param syld          四员联动
     */
    @GetMapping(value = "/gaic")
    public AjaxResult gaic(String classify, Long stId, Long spId, String syld){
        // 结果
        Map<String, Object> map = new HashMap<>();

        // 任务
        GatherSurveyTask task = taskService.selectById(stId);

        // 获取该次任务下某品规的价格/库存数据
        List<GatherPI> pis = new ArrayList<>();
        if(classify.equals("price")){
            GatherStPrice search = new GatherStPrice();
            search.setStId(stId);
            search.setSpId(spId);
            search.setClear1(0);
            search.setClear2(0);
            List<GatherStPrice> prices = gatherStPriceService.selectList(search);
            pis = prices.stream().map(GatherPI::new).toList();
        } else {
            GatherStInventory search = new GatherStInventory();
            search.setStId(stId);
            search.setSpId(spId);
            search.setClear1(0);
            search.setClear2(0);
            List<GatherStInventory> inventories = gatherStInventoryService.selectList(search);
            pis = inventories.stream().map(GatherPI::new).toList();
        }

        // 校验数据
        if(CollectionUtil.isEmpty(pis)){
            return AjaxResult.success(map);
        }

        // 热力图：品规
        List<MerchantInfo> mls = merchantInfoMapper.selectMerchantLocationListBySjcj(classify, stId);
        Map<String, MerchantInfo> mlm = mls.stream().collect(Collectors.toMap(MerchantInfo::getMerchantCode, Function.identity()));
        List<Map<String, List<Double>>> items = new ArrayList<>();
        for (GatherPI pi : pis) {
            MerchantInfo ml = mlm.get(pi.getShXkz());
            if(ml != null && ml.getLongitudeAfterOffset() > 0 && ml.getLatitudeAfterOffset() > 0){
                Map<String, List<Double>> item = new HashMap<>();
                item.put("value", Arrays.asList(ml.getLongitudeAfterOffset(), ml.getLatitudeAfterOffset(), pi.getValue()));
                items.add(item);
            }
        }
        map.put("gjDt", items);

        // 箱线图：设置建议零售价、批发价、调货价
        if(classify.equals("price")){
            GatherSmokePrice smokePrice = gatherSmokePriceService.selectById(spId);
            map.put("lsj", smokePrice.getLsPrice());
            map.put("pfj", smokePrice.getPfPrice());
            map.put("thj", getThj(task.getRemark(), smokePrice.getId()));
        }

        // 箱线图：档位统计（某品规）
        List<String> dw_1_5 = Arrays.asList("一档", "二档", "三档", "四档", "五档");
        List<String> dw_6_10 = Arrays.asList("六档", "七档", "八档", "九档", "十档");
        List<String> dw_11_15 = Arrays.asList("十一档", "十二档", "十三档", "十四档", "十五档");
        List<String> dw_16_20 = Arrays.asList("十六档", "十七档", "十八档", "十九档", "二十档");
        List<String> dw_21_25 = Arrays.asList("二十一档", "二十二档", "二十三档", "二十四档", "二十五档");
        List<String> dw_26_30 = Arrays.asList("二十六档", "二十七档", "二十八档", "二十九档", "三十档");
        List<Double> filter1 = pis.stream().filter(o -> dw_1_5.contains(o.getShDw())).map(GatherPI::getValue).collect(Collectors.toList());
        List<Double> filter2 = pis.stream().filter(o -> dw_6_10.contains(o.getShDw())).map(GatherPI::getValue).collect(Collectors.toList());
        List<Double> filter3 = pis.stream().filter(o -> dw_11_15.contains(o.getShDw())).map(GatherPI::getValue).collect(Collectors.toList());
        List<Double> filter4 = pis.stream().filter(o -> dw_16_20.contains(o.getShDw())).map(GatherPI::getValue).collect(Collectors.toList());
        List<Double> filter5 = pis.stream().filter(o -> dw_21_25.contains(o.getShDw())).map(GatherPI::getValue).collect(Collectors.toList());
        List<Double> filter6 = pis.stream().filter(o -> dw_26_30.contains(o.getShDw())).map(GatherPI::getValue).collect(Collectors.toList());
        List<Double> zxqj1 = CalUtils.calXxt(filter1);
        List<Double> zxqj2 = CalUtils.calXxt(filter2);
        List<Double> zxqj3 = CalUtils.calXxt(filter3);
        List<Double> zxqj4 = CalUtils.calXxt(filter4);
        List<Double> zxqj5 = CalUtils.calXxt(filter5);
        List<Double> zxqj6 = CalUtils.calXxt(filter6);
        List<List<Double>> gjXxtDw = new ArrayList<>();
        gjXxtDw.add(zxqj1);
        gjXxtDw.add(zxqj2);
        gjXxtDw.add(zxqj3);
        gjXxtDw.add(zxqj4);
        gjXxtDw.add(zxqj5);
        gjXxtDw.add(zxqj6);
        map.put("gjXxtDwX", Arrays.asList("1-5档", "6-10档", "11-15档", "16-20档", "21-25档", "26-30档"));
        map.put("gjXxtDwData", gjXxtDw);
        map.put("gjXxtDwJzzx", Arrays.asList(zxqj1.get(2), zxqj2.get(2), zxqj3.get(2), zxqj4.get(2), zxqj5.get(2), zxqj6.get(2)));

        // 箱线图：城镇/乡村统计（某品规）
        List<Double> czxc1 = CalUtils.calXxt(pis.stream().filter(o -> o.getShCz().equals("城镇")).map(GatherPI::getValue).collect(Collectors.toList()));
        List<Double> czxc2 = CalUtils.calXxt(pis.stream().filter(o -> o.getShCz().equals("乡村")).map(GatherPI::getValue).collect(Collectors.toList()));
        List<List<Double>> czxcs = new ArrayList<>();
        czxcs.add(czxc1);
        czxcs.add(czxc2);
        map.put("gjXxtCzxcX", Arrays.asList("城镇", "乡村"));
        map.put("gjXxtCzxcData", czxcs);
        map.put("gjXxtCzxcJzzx", Arrays.asList(czxc1.get(2), czxc2.get(2)));

        // 箱线图：业态统计（某品规）
        List<Double> yt1 = CalUtils.calXxt(pis.stream().filter(o -> o.getShYt().equals("便利店")).map(GatherPI::getValue).collect(Collectors.toList()));
        List<Double> yt2 = CalUtils.calXxt(pis.stream().filter(o -> o.getShYt().equals("烟酒商店")).map(GatherPI::getValue).collect(Collectors.toList()));
        List<Double> yt3 = CalUtils.calXxt(pis.stream().filter(o -> o.getShYt().equals("超市")).map(GatherPI::getValue).collect(Collectors.toList()));
        List<Double> yt4 = CalUtils.calXxt(pis.stream().filter(o -> o.getShYt().equals("商场")).map(GatherPI::getValue).collect(Collectors.toList()));
        List<Double> yt5 = CalUtils.calXxt(pis.stream().filter(o -> o.getShYt().equals("娱乐服务业")).map(GatherPI::getValue).collect(Collectors.toList()));
        List<Double> yt6 = CalUtils.calXxt(pis.stream().filter(o -> o.getShYt().equals("其他")).map(GatherPI::getValue).collect(Collectors.toList()));
        List<List<Double>> yts = new ArrayList<>();
        yts.add(yt1);
        yts.add(yt2);
        yts.add(yt3);
        yts.add(yt4);
        yts.add(yt5);
        yts.add(yt6);
        map.put("gjXxtYtX", Arrays.asList("便利店", "烟酒商店", "超市", "商场", "娱乐服务业", "其他"));
        map.put("gjXxtYtData", yts);
        map.put("gjXxtYtJzzx", Arrays.asList(yt1.get(2), yt2.get(2), yt3.get(2), yt4.get(2), yt5.get(2), yt6.get(2)));

        // 四员联动
        if(StringUtils.isNotBlank(syld) && syld.equals("1")){
            GatherStLink search = new GatherStLink();
            List<GatherStLink> links = gatherStLinkService.selectList(search);
            map.put("links", links);
        }

        // 返回
        return AjaxResult.success(map);
    }

    /**
     * 该次的(柱状图)
     *
     * @param classify      库存或者价格
     * @param stId          任务id
     * @param zztDim        柱状图类型（档位：dw、城镇/乡村：czxc、业态：yt）
     */
    @GetMapping(value = "/gaicZzt")
    public AjaxResult gaicZzt(String classify, Long stId, String zztDim){
        // 结果
        Map<String, Object> map = new HashMap<>();

        // 获取该次任务下的价格/库存数据
        List<GatherPI> pis = new ArrayList<>();
        if(classify.equals("price")){
            GatherStPrice search = new GatherStPrice();
            search.setStId(stId);
            search.setClear1(0);
            search.setClear2(0);
            List<GatherStPrice> prices = gatherStPriceService.selectList(search);
            pis = prices.stream().map(GatherPI::new).toList();
        } else {
            GatherStInventory search = new GatherStInventory();
            search.setStId(stId);
            search.setClear1(0);
            search.setClear2(0);
            List<GatherStInventory> inventories = gatherStInventoryService.selectList(search);
            pis = inventories.stream().map(GatherPI::new).toList();
        }

        // 校验数据
        if(CollectionUtil.isEmpty(pis)){
            return AjaxResult.success(map);
        }

        // 按烟分组
        Map<Long, List<GatherPI>> mg = pis.stream().collect(Collectors.groupingBy(GatherPI::getSpId));

        // 柱状图：档位、城镇/乡村、业态（所有品规）
        List<LinkedHashMap<String, Object>> zzt = new ArrayList<>();
        if(zztDim.equals("dw")){
            // 按烟循环
            for (Map.Entry<Long, List<GatherPI>> entry : mg.entrySet()) {
                Long spId = entry.getKey();
                List<GatherPI> g = entry.getValue();
                List<String> dw_1_5 = Arrays.asList("一档", "二档", "三档", "四档", "五档");
                List<String> dw_6_10 = Arrays.asList("六档", "七档", "八档", "九档", "十档");
                List<String> dw_11_15 = Arrays.asList("十一档", "十二档", "十三档", "十四档", "十五档");
                List<String> dw_16_20 = Arrays.asList("十六档", "十七档", "十八档", "十九档", "二十档");
                List<String> dw_21_25 = Arrays.asList("二十一档", "二十二档", "二十三档", "二十四档", "二十五档");
                List<String> dw_26_30 = Arrays.asList("二十六档", "二十七档", "二十八档", "二十九档", "三十档");
                List<Double> filter1 = g.stream().filter(o -> dw_1_5.contains(o.getShDw())).map(GatherPI::getValue).collect(Collectors.toList());
                List<Double> filter2 = g.stream().filter(o -> dw_6_10.contains(o.getShDw())).map(GatherPI::getValue).collect(Collectors.toList());
                List<Double> filter3 = g.stream().filter(o -> dw_11_15.contains(o.getShDw())).map(GatherPI::getValue).collect(Collectors.toList());
                List<Double> filter4 = g.stream().filter(o -> dw_16_20.contains(o.getShDw())).map(GatherPI::getValue).collect(Collectors.toList());
                List<Double> filter5 = g.stream().filter(o -> dw_21_25.contains(o.getShDw())).map(GatherPI::getValue).collect(Collectors.toList());
                List<Double> filter6 = g.stream().filter(o -> dw_26_30.contains(o.getShDw())).map(GatherPI::getValue).collect(Collectors.toList());
                zzt.add(new LinkedHashMap<>(){{
                    put("product", gatherSmokePriceService.selectById(spId).getName());
                    put("1-5档", CalUtils.getMean(filter1));
                    put("6-10档", CalUtils.getMean(filter2));
                    put("11-15档", CalUtils.getMean(filter3));
                    put("16-20档", CalUtils.getMean(filter4));
                    put("21-25档", CalUtils.getMean(filter5));
                    put("26-30档", CalUtils.getMean(filter6));
                }});
            }
            map.put("gjZztDimensions", Arrays.asList("product", "1-5档", "6-10档", "11-15档", "16-20档", "21-25档", "26-30档"));
        }
        if(zztDim.equals("czxc")){
            // 按烟循环
            for (Map.Entry<Long, List<GatherPI>> entry : mg.entrySet()) {
                Long spId = entry.getKey();
                List<GatherPI> g = entry.getValue();
                List<Double> filter1 = g.stream().filter(o -> o.getShCz().equals("城镇")).map(GatherPI::getValue).collect(Collectors.toList());
                List<Double> filter2 = g.stream().filter(o -> o.getShCz().equals("乡村")).map(GatherPI::getValue).collect(Collectors.toList());
                zzt.add(new LinkedHashMap<>(){{
                    put("product", gatherSmokePriceService.selectById(spId).getName());
                    put("城镇", CalUtils.getMean(filter1));
                    put("乡村", CalUtils.getMean(filter2));
                }});
            }
            map.put("gjZztDimensions", Arrays.asList("product", "城镇", "乡村"));
        }
        if(zztDim.equals("yt")){
            // 按烟循环
            for (Map.Entry<Long, List<GatherPI>> entry : mg.entrySet()) {
                Long spId = entry.getKey();
                List<GatherPI> g = entry.getValue();
                List<Double> filter1 = g.stream().filter(o -> o.getShYt().equals("便利店")).map(GatherPI::getValue).collect(Collectors.toList());
                List<Double> filter2 = g.stream().filter(o -> o.getShYt().equals("烟酒商店")).map(GatherPI::getValue).collect(Collectors.toList());
                List<Double> filter3 = g.stream().filter(o -> o.getShYt().equals("超市")).map(GatherPI::getValue).collect(Collectors.toList());
                List<Double> filter4 = g.stream().filter(o -> o.getShYt().equals("商场")).map(GatherPI::getValue).collect(Collectors.toList());
                List<Double> filter5 = g.stream().filter(o -> o.getShYt().equals("娱乐服务业")).map(GatherPI::getValue).collect(Collectors.toList());
                List<Double> filter6 = g.stream().filter(o -> o.getShYt().equals("其他")).map(GatherPI::getValue).collect(Collectors.toList());
                zzt.add(new LinkedHashMap<>(){{
                    put("product", gatherSmokePriceService.selectById(spId).getName());
                    put("便利店", CalUtils.getMean(filter1));
                    put("烟酒商店", CalUtils.getMean(filter2));
                    put("超市", CalUtils.getMean(filter3));
                    put("商场", CalUtils.getMean(filter4));
                    put("娱乐服务业", CalUtils.getMean(filter5));
                    put("其他", CalUtils.getMean(filter6));
                }});
            }
            map.put("gjZztDimensions", Arrays.asList("product", "便利店", "烟酒商店", "超市", "商场", "娱乐服务业", "其他"));
        }
        map.put("gjZztSource", zzt);

        // 返回
        return AjaxResult.success(map);
    }

    /**
     * 累计的
     *
     * @param isAll         是否显示全部（1：是，2：否）
     * @param classify      库存或者价格
     * @param spId          品规id
     * @param xxtZtLoad     箱线图（整体）是否加载数据
     * @param xxtDwLoad     箱线图（档位）是否加载数据
     * @param xxtCzxcLoad   箱线图（城镇乡村）是否加载数据
     * @param xxtYtLoad     箱线图（业态）是否加载数据
     * @param dwValue       箱线图（各个档位）是否加载数据
     * @param czxcValue     箱线图（各个城镇乡村）是否加载数据
     * @param ytValue       箱线图（各个业态）是否加载数据
     */
    @GetMapping(value = "/leij")
    public AjaxResult leij(String isAll, String classify, Long spId, String xxtZtLoad, String xxtDwLoad, String xxtCzxcLoad, String xxtYtLoad, String dwValue, String czxcValue, String ytValue){
        // 结果
        Map<String, Object> map = new HashMap<>();

        // 获取任务
        List<GatherSurveyTask> gsts = taskService.selectList(new GatherSurveyTask());
        List<GatherSurveyTask> filter = gsts.stream().filter(o -> o.getImportStatusPrice().equals("1") && o.getImportStatusInventory().equals("1")).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(filter)){
            return AjaxResult.success(map);
        }
        if(isAll.equals("2")){
            if(filter.size() > 5){
                filter = filter.subList(0, 5);
            }
        }

        // 反转
        Collections.reverse(filter);

        // 获取品规
        GatherSmokePrice gsp = gatherSmokePriceService.selectById(spId);

        // 零售价、批发价
        map.put("lsj", gsp.getLsPrice());
        map.put("pfj", gsp.getPfPrice());

        // xData
        List<String> xData1 = new ArrayList<>();
        List<String> xData2 = new ArrayList<>();
        // 循环任务列表
        List<List<Double>> sData1 = new ArrayList<>();
        List<List<Double>> sData2 = new ArrayList<>();
        List<List<Double>> sData3 = new ArrayList<>();
        List<List<Double>> sData4 = new ArrayList<>();
        List<Double> zxData1_1 = new ArrayList<>();
        List<Double> zxData1_2 = new ArrayList<>();
        List<Double> zxData1_3 = new ArrayList<>();
        List<Double> zxData1_4 = new ArrayList<>();
        List<Double> zxData2_1 = new ArrayList<>();
        List<Double> zxData2_2 = new ArrayList<>();
        List<Double> zxData2_3 = new ArrayList<>();
        List<Double> zxData2_4 = new ArrayList<>();
        for (GatherSurveyTask surveyTask : filter) {
            xData1.add(surveyTask.getMonth() + "月第" + surveyTask.getWeek() + "周");
            xData2.add(surveyTask.getYear() + "年" + surveyTask.getMonth() + "月第" + surveyTask.getWeek() + "周");
            // 获取数据
            List<GatherPI> pis = new ArrayList<>();
            if(classify.equals("price")){
                GatherStPrice search = new GatherStPrice();
                search.setStId(surveyTask.getId());
                search.setSpId(gsp.getId());
                search.setClear1(0);
                search.setClear2(0);
                List<GatherStPrice> prices = gatherStPriceService.selectList(search);
                pis = prices.stream().map(GatherPI::new).toList();
            } else {
                GatherStInventory search = new GatherStInventory();
                search.setStId(surveyTask.getId());
                search.setSpId(gsp.getId());
                search.setClear1(0);
                search.setClear2(0);
                List<GatherStInventory> inventories = gatherStInventoryService.selectList(search);
                pis = inventories.stream().map(GatherPI::new).toList();
            }

            // 箱线图：整体
            if(StringUtils.isNotBlank(xxtZtLoad) && xxtZtLoad.equals("1")){
                if(CollectionUtil.isNotEmpty(pis)){
                    List<Double> zxqj = CalUtils.calXxt(pis.stream().map(GatherPI::getValue).collect(Collectors.toList()));
                    sData1.add(zxqj);
                    zxData1_1.add(zxqj.get(2));
                    zxData2_1.add(getThj(surveyTask.getRemark(), spId));
                } else {
                    sData1.add(Arrays.asList(0D, 0D, 0D, 0D, 0D));
                    zxData1_1.add(0D);
                    zxData2_1.add(0D);
                }
            }

            // 箱线图：档位
            if(StringUtils.isNotBlank(xxtDwLoad) && xxtDwLoad.equals("1")){
                if(CollectionUtil.isNotEmpty(pis)){
                    List<String> dw_1_5 = Arrays.asList("一档", "二档", "三档", "四档", "五档");
                    List<String> dw_6_10 = Arrays.asList("六档", "七档", "八档", "九档", "十档");
                    List<String> dw_11_15 = Arrays.asList("十一档", "十二档", "十三档", "十四档", "十五档");
                    List<String> dw_16_20 = Arrays.asList("十六档", "十七档", "十八档", "十九档", "二十档");
                    List<String> dw_21_25 = Arrays.asList("二十一档", "二十二档", "二十三档", "二十四档", "二十五档");
                    List<String> dw_26_30 = Arrays.asList("二十六档", "二十七档", "二十八档", "二十九档", "三十档");
                    List<String> mrdw = new ArrayList<>(dw_1_5);
                    if(StringUtils.isNotBlank(dwValue) && dwValue.equals("1")){
                        mrdw = dw_1_5;
                    }
                    if(StringUtils.isNotBlank(dwValue) && dwValue.equals("2")){
                        mrdw = dw_6_10;
                    }
                    if(StringUtils.isNotBlank(dwValue) && dwValue.equals("3")){
                        mrdw = dw_11_15;
                    }
                    if(StringUtils.isNotBlank(dwValue) && dwValue.equals("4")){
                        mrdw = dw_16_20;
                    }
                    if(StringUtils.isNotBlank(dwValue) && dwValue.equals("5")){
                        mrdw = dw_21_25;
                    }
                    if(StringUtils.isNotBlank(dwValue) && dwValue.equals("6")){
                        mrdw = dw_26_30;
                    }
                    List<String> finalMrdw = mrdw;
                    List<Double> zxqj = CalUtils.calXxt(pis.stream().filter(o -> finalMrdw.contains(o.getShDw())).map(GatherPI::getValue).collect(Collectors.toList()));
                    sData2.add(zxqj);
                    zxData1_2.add(zxqj.get(2));
                    zxData2_2.add(getThj(surveyTask.getRemark(), spId));
                } else {
                    sData2.add(Arrays.asList(0D, 0D, 0D, 0D, 0D));
                    zxData1_2.add(0D);
                    zxData2_2.add(0D);
                }
            }

            // 箱线图：城镇乡村
            if(StringUtils.isNotBlank(xxtCzxcLoad) && xxtCzxcLoad.equals("1")){
                if(CollectionUtil.isNotEmpty(pis)){
                    List<Double> zxqj = CalUtils.calXxt(pis.stream().filter(o -> o.getShCz().equals(czxcValue)).map(GatherPI::getValue).collect(Collectors.toList()));
                    sData3.add(zxqj);
                    zxData1_3.add(zxqj.get(2));
                    zxData2_3.add(getThj(surveyTask.getRemark(), spId));
                } else {
                    sData3.add(Arrays.asList(0D, 0D, 0D, 0D, 0D));
                    zxData1_3.add(0D);
                    zxData2_3.add(0D);
                }
            }

            // 箱线图：业态
            if(StringUtils.isNotBlank(xxtYtLoad) && xxtYtLoad.equals("1")){
                if(CollectionUtil.isNotEmpty(pis)){
                    List<Double> vs = pis.stream().filter(o -> o.getShYt().equals(ytValue)).map(GatherPI::getValue).collect(Collectors.toList());
                    List<Double> zxqj = CalUtils.calXxt(vs);
                    sData4.add(zxqj);
                    zxData1_4.add(zxqj.get(2));
                    zxData2_4.add(getThj(surveyTask.getRemark(), spId));
                } else {
                    sData4.add(Arrays.asList(0D, 0D, 0D, 0D, 0D));
                    zxData1_4.add(0D);
                    zxData2_4.add(0D);
                }
            }
        }

        // 设置
        map.put("xData1", xData1);
        map.put("xData2", xData2);
        map.put("sData1", sData1);
        map.put("zxData1_1", zxData1_1);
        map.put("zxData2_1", zxData2_1);
        map.put("sData2", sData2);
        map.put("zxData1_2", zxData1_2);
        map.put("zxData2_2", zxData2_2);
        map.put("sData3", sData3);
        map.put("zxData1_3", zxData1_3);
        map.put("zxData2_3", zxData2_3);
        map.put("sData4", sData4);
        map.put("zxData1_4", zxData1_4);
        map.put("zxData2_4", zxData2_4);

        // 返回
        return AjaxResult.success(map);
    }

    /**
     * 获取调货价
     *
     * @param json
     * @param spId
     * @return
     */
    public Double getThj(String json, Long spId){
        if(StringUtils.isNotBlank(json)){
            JSONObject jsonObject = JSONObject.parseObject(json);
            JSONObject value = jsonObject.getJSONObject("111111");
            Double z = value.getDouble(String.valueOf(spId));
            return z != null ? z : 0.0D;
        } else {
            return 0.00;
        }
    }
}

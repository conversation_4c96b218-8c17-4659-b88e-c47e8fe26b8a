package com.hz.web.controller.merchant;

import com.hz.common.core.domain.entity.SysUser;
import java.util.List;

/**
 * 监督人员分配商户和人员请求类
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public class SupervisorAssignmentRequest
{
    /** 月度考核ID */
    private Long monthlyAssessmentId;

    /** 商户ID列表 */
    private List<Long> merchantIds;

    /** 专卖人员列表 */
    private List<SysUser> monopolyStaffs;

    /** 营销人员列表 */
    private List<SysUser> marketingStaffs;

    public SupervisorAssignmentRequest() {
    }

    public SupervisorAssignmentRequest(Long monthlyAssessmentId, List<Long> merchantIds,
                                     List<SysUser> monopolyStaffs, List<SysUser> marketingStaffs) {
        this.monthlyAssessmentId = monthlyAssessmentId;
        this.merchantIds = merchantIds;
        this.monopolyStaffs = monopolyStaffs;
        this.marketingStaffs = marketingStaffs;
    }

    public Long getMonthlyAssessmentId() {
        return monthlyAssessmentId;
    }

    public void setMonthlyAssessmentId(Long monthlyAssessmentId) {
        this.monthlyAssessmentId = monthlyAssessmentId;
    }

    public List<Long> getMerchantIds() {
        return merchantIds;
    }

    public void setMerchantIds(List<Long> merchantIds) {
        this.merchantIds = merchantIds;
    }

    public List<SysUser> getMonopolyStaffs() {
        return monopolyStaffs;
    }

    public void setMonopolyStaffs(List<SysUser> monopolyStaffs) {
        this.monopolyStaffs = monopolyStaffs;
    }

    public List<SysUser> getMarketingStaffs() {
        return marketingStaffs;
    }

    public void setMarketingStaffs(List<SysUser> marketingStaffs) {
        this.marketingStaffs = marketingStaffs;
    }

    @Override
    public String toString() {
        return "SupervisorAssignmentRequest{" +
                "monthlyAssessmentId=" + monthlyAssessmentId +
                ", merchantIds=" + merchantIds +
                ", monopolyStaffs=" + monopolyStaffs +
                ", marketingStaffs=" + marketingStaffs +
                '}';
    }
}

package com.hz.web.controller.gather;

import com.hz.common.annotation.Log;
import com.hz.common.core.controller.BaseController;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.core.page.TableDataInfo;
import com.hz.common.enums.BusinessType;
import com.hz.common.utils.poi.ExcelUtil;
import com.hz.gather.domain.GatherStInventory;
import com.hz.gather.service.IGatherStInventoryService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 调研库存Controller
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@RestController
@RequestMapping("/gather/inventory")
public class GatherStInventoryController extends BaseController{

    @Resource
    private IGatherStInventoryService inventoryService;

    /**
     * 查询调研库存列表
     */
    @PreAuthorize("@ss.hasPermi('gather:inventory:query')")
    @GetMapping("/list")
    public TableDataInfo list(GatherStInventory inventory){
        startPage();
        List<GatherStInventory> list = inventoryService.selectList(inventory);
        return getDataTable(list);
    }

    /**
     * 导出调研库存列表
     */
    @PreAuthorize("@ss.hasPermi('gather:inventory:export')")
    @Log(title = "调研库存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GatherStInventory inventory){
        List<GatherStInventory> list = inventoryService.selectList(inventory);
        ExcelUtil<GatherStInventory> util = new ExcelUtil<>(GatherStInventory.class);
        util.exportExcel(response, list, "调研库存数据");
    }

    /**
     * 获取调研库存详细信息
     */
    @PreAuthorize("@ss.hasPermi('gather:inventory:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return success(inventoryService.selectById(id));
    }

    /**
     * 新增调研库存
     */
    @PreAuthorize("@ss.hasPermi('gather:inventory:add')")
    @Log(title = "调研库存", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GatherStInventory inventory){
        return toAjax(inventoryService.insert(inventory));
    }

    /**
     * 修改调研库存
     */
    @PreAuthorize("@ss.hasPermi('gather:inventory:edit')")
    @Log(title = "调研库存", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GatherStInventory inventory){
        return toAjax(inventoryService.update(inventory));
    }

    /**
     * 删除调研库存
     */
    @PreAuthorize("@ss.hasPermi('gather:inventory:remove')")
    @Log(title = "调研库存", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids){
        return toAjax(inventoryService.deleteByIds(ids));
    }
}

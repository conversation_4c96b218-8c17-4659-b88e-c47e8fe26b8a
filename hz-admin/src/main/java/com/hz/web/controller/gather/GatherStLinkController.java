package com.hz.web.controller.gather;

import com.hz.common.annotation.Log;
import com.hz.common.core.controller.BaseController;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.core.page.TableDataInfo;
import com.hz.common.enums.BusinessType;
import com.hz.common.utils.poi.ExcelUtil;
import com.hz.gather.domain.GatherStLink;
import com.hz.gather.service.IGatherStLinkService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 调研四员联动Controller
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@RestController
@RequestMapping("/gather/stLink")
public class GatherStLinkController extends BaseController{

    @Resource
    private IGatherStLinkService linkService;

    /**
     * 查询调研四员联动列表
     */
    @PreAuthorize("@ss.hasPermi('gather:stLink:query')")
    @GetMapping("/list")
    public TableDataInfo list(GatherStLink link){
        startPage();
        List<GatherStLink> list = linkService.selectList(link);
        return getDataTable(list);
    }

    /**
     * 导出调研四员联动列表
     */
    @PreAuthorize("@ss.hasPermi('gather:stLink:export')")
    @Log(title = "调研四员联动", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GatherStLink link){
        List<GatherStLink> list = linkService.selectList(link);
        ExcelUtil<GatherStLink> util = new ExcelUtil<>(GatherStLink.class);
        util.exportExcel(response, list, "调研四员联动数据");
    }

    /**
     * 获取调研四员联动详细信息
     */
    @PreAuthorize("@ss.hasPermi('gather:stLink:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return success(linkService.selectById(id));
    }

    /**
     * 新增调研四员联动
     */
    @PreAuthorize("@ss.hasPermi('gather:stLink:add')")
    @Log(title = "调研四员联动", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GatherStLink link){
        return toAjax(linkService.insert(link));
    }

    /**
     * 修改调研四员联动
     */
    @PreAuthorize("@ss.hasPermi('gather:stLink:edit')")
    @Log(title = "调研四员联动", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GatherStLink link){
        return toAjax(linkService.update(link));
    }

    /**
     * 删除调研四员联动
     */
    @PreAuthorize("@ss.hasPermi('gather:stLink:remove')")
    @Log(title = "调研四员联动", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids){
        return toAjax(linkService.deleteByIds(ids));
    }
}

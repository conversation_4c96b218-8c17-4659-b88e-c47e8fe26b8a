package com.hz.web.controller.gather;

import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson2.JSONArray;
import com.hz.common.annotation.Log;
import com.hz.common.config.HzConfig;
import com.hz.common.constant.Constants;
import com.hz.common.core.controller.BaseController;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.core.page.TableDataInfo;
import com.hz.common.enums.BusinessType;
import com.hz.common.utils.DateUtils;
import com.hz.common.utils.StringUtils;
import com.hz.common.utils.file.FileUploadUtils;
import com.hz.common.utils.file.FileUtils;
import com.hz.common.utils.poi.ExcelUtil;
import com.hz.framework.config.ServerConfig;
import com.hz.gather.domain.GatherSurveyTask;
import com.hz.gather.service.IGatherSurveyTaskService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;

/**
 * 调研任务Controller
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@RestController
@RequestMapping("/gather/surveyTask")
public class GatherSurveyTaskController extends BaseController{

    @Resource
    private ServerConfig serverConfig;
    @Resource
    private IGatherSurveyTaskService taskService;

    /**
     * <h1>查询调研任务列表</h1>
     */
    @PreAuthorize("@ss.hasPermi('gather:surveyTask:query')")
    @GetMapping("/list")
    public TableDataInfo list(GatherSurveyTask task){
        startPage();
        JSONArray timeArray = null;
        if(StringUtils.isNotBlank(task.getYearMonthWeek()))
            timeArray = JSONArray.parseArray(task.getYearMonthWeek());
        if(timeArray != null && timeArray.size() == 2){
            task.setStartTime(DateUtils.date2week(DateUtils.parseDate(timeArray.get(0).toString().replace("T", " ").replace(".000Z", ""))));
            task.setEndTime(DateUtils.date2week(DateUtils.parseDate(timeArray.get(1).toString().replace("T", " ").replace(".000Z", ""))));
            task.setYearMonthWeek(null);
        }
        List<GatherSurveyTask> list = taskService.selectList(task);
        return getDataTable(list);
    }

    /**
     * <h1>获取调研任务详细信息</h1>
     */
    @PreAuthorize("@ss.hasPermi('gather:surveyTask:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return success(taskService.selectById(id));
    }

    /**
     * <h1>新增调研任务</h1>
     */
    @PreAuthorize("@ss.hasPermi('gather:surveyTask:add')")
    @Log(title = "调研任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GatherSurveyTask task){
        if(StringUtils.isNotBlank(task.getYear()) && StringUtils.isNotBlank(task.getMonth()) && StringUtils.isNotBlank(task.getWeek())){
            task.setYearMonthWeek(task.getYear() + "-" +
                                          String.format("%02d", Integer.parseInt(task.getMonth())) + "-" +
                                          task.getWeek());
        }
        return toAjax(taskService.insert(task));
    }

    /**
     * <h1>修改调研任务</h1>
     */
    @PreAuthorize("@ss.hasPermi('gather:surveyTask:edit')")
    @Log(title = "调研任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GatherSurveyTask task){
        if(StringUtils.isNotBlank(task.getYear()) && StringUtils.isNotBlank(task.getMonth()) && StringUtils.isNotBlank(task.getWeek())){
            task.setYearMonthWeek(task.getYear() + "-" +
                                          String.format("%02d", Integer.parseInt(task.getMonth())) + "-" +
                                          task.getWeek());
        }
        return toAjax(taskService.update(task));
    }

    /**
     * <h1>删除调研任务</h1>
     */
    @PreAuthorize("@ss.hasPermi('gather:surveyTask:remove')")
    @Log(title = "调研任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids){
        return toAjax(taskService.deleteByIds(ids));
    }

    /**
     * <h1>导入价格调研数据</h1>
     */
    @PostMapping("/upload")
    @PreAuthorize("@ss.hasPermi('gather:surveyTask:edit')")
    @Log(title = "调研任务", businessType = BusinessType.IMPORT)
    public AjaxResult uploadFile(MultipartFile file,
                                 @RequestParam(value = "taskId") long taskId,
                                 @RequestParam(value = "type") String type) throws Exception{
        GatherSurveyTask task = new GatherSurveyTask();
        task.setId(taskId);

        // 上传文件路径
        String filePath = HzConfig.getProfile() + "/gather";
        String fileName = FileUploadUtils.upload(filePath, file);

        // ############################### 导入调研数据 ###############################
        switch(type){
            case "1" -> taskService.importPriceData(file, taskId);     // 导入价格调研数据
            case "2" -> taskService.importInventoryData(file, taskId); // 导入库存调研数据
            case "3" -> taskService.importLinkData(file, taskId);      // 导入四员联动调研数据
        }

        // ############################### 更新任务信息 ###############################
        switch(type){
            case "1" -> {
                task.setImportStatusPrice("1");
                task.setImportFilePrice(fileName);
                task.setImportTimePrice(DateUtils.getNowDate());
            }
            case "2" -> {
                task.setImportStatusInventory("1");
                task.setImportFileInventory(fileName);
                task.setImportTimeInventory(DateUtils.getNowDate());
            }
            case "3" -> {
                task.setImportStatusLink("1");
                task.setImportFileLink(fileName);
                task.setImportTimeLink(DateUtils.getNowDate());
            }
        }
        taskService.update(task);

        AjaxResult ajax = AjaxResult.success();
        ajax.put("fileName", fileName);
        ajax.put("newFileName", FileUtils.getName(fileName));
        ajax.put("originalFilename", file.getOriginalFilename());
        return ajax;
    }

    /**
     * <h1>导出调研任务列表</h1>
     */
    @PreAuthorize("@ss.hasPermi('gather:surveyTask:export')")
    @Log(title = "调研任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GatherSurveyTask task){
        List<GatherSurveyTask> list = taskService.selectList(task);
        ExcelUtil<GatherSurveyTask> util = new ExcelUtil<>(GatherSurveyTask.class);
        util.exportExcel(response, list, "调研任务");
    }

    /**
     * <h1>导出调研任务数据</h1>
     */
    @PreAuthorize("@ss.hasPermi('gather:surveyTask:export')")
    @Log(title = "调研任务", businessType = BusinessType.EXPORT)
    @PostMapping("/exportData")
    public void exportData(HttpServletResponse response, GatherSurveyTask task){
        // 数据准备
        HashMap<String, Object> data11 = taskService.getStatisticTableData(task.getId(), 1, 1); // 查询 价格调研-原始数据
        HashMap<String, Object> data12 = taskService.getStatisticTableData(task.getId(), 1, 2); // 查询 价格调研-清理后数据
        HashMap<String, Object> data14 = taskService.getStatisticTableData(task.getId(), 1, 4); // 查询 价格调研-清理提示
        HashMap<String, Object> data21 = taskService.getStatisticTableData(task.getId(), 1, 1); // 查询 库存调研-原始数据
        HashMap<String, Object> data22 = taskService.getStatisticTableData(task.getId(), 1, 2); // 查询 库存调研-清理后数据
        HashMap<String, Object> data24 = taskService.getStatisticTableData(task.getId(), 1, 4); // 查询 库存调研-清理提示
        HashMap<String, Object> data31 = taskService.getStatisticTableData(task.getId(), 1, 1); // 查询 四员联动-原始数据

        try(ExcelWriter writer = new ExcelWriter(true, "价格调研-原始数据")){
            // 写入第一个工作表
            List<String> tableHeader = (List<String>) data11.get("tableHeader");
            List<List<String>> tableData = (List<List<String>>) data11.get("tableData");
            writer.writeHeadRow(tableHeader);
            tableData.forEach(writer::writeRow);

            writer.setSheet("价格调研-清理后数据");
            tableHeader = (List<String>) data12.get("tableHeader");
            tableData = (List<List<String>>) data12.get("tableData");
            writer.writeHeadRow(tableHeader);
            tableData.forEach(writer::writeRow);

            writer.setSheet("价格调研-清理提示");
            tableHeader = (List<String>) data14.get("tableHeader");
            tableData = (List<List<String>>) data14.get("tableData");
            writer.writeHeadRow(tableHeader);
            tableData.forEach(writer::writeRow);

            writer.setSheet("库存调研-原始数据");
            tableHeader = (List<String>) data21.get("tableHeader");
            tableData = (List<List<String>>) data21.get("tableData");
            writer.writeHeadRow(tableHeader);
            tableData.forEach(writer::writeRow);

            writer.setSheet("库存调研-清理后数据");
            tableHeader = (List<String>) data22.get("tableHeader");
            tableData = (List<List<String>>) data22.get("tableData");
            writer.writeHeadRow(tableHeader);
            tableData.forEach(writer::writeRow);

            writer.setSheet("库存调研-清理提示");
            tableHeader = (List<String>) data24.get("tableHeader");
            tableData = (List<List<String>>) data24.get("tableData");
            writer.writeHeadRow(tableHeader);
            tableData.forEach(writer::writeRow);

            writer.setSheet("四员联动-原始数据");
            tableHeader = (List<String>) data31.get("tableHeader");
            tableData = (List<List<String>>) data31.get("tableData");
            writer.writeHeadRow(tableHeader);
            tableData.forEach(writer::writeRow);

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("multi_sheet_excel.xlsx", StandardCharsets.UTF_8);
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

            // 将 Excel 写入响应输出流
            writer.flush(response.getOutputStream(), true);
        }catch(IOException e){
            logger.error("【调度任务】导出任务详情数据失败", e);
        }
    }

    /**
     * <h1>获取调研数据报表</h1>
     * @param taskId 任务ID
     * @param type 数据类型 1:价格 2:库存 3:四员联动
     * @param operate 1:原始数据 2:清洗后数据 3:对比数据 4:清洗提示 5：清洗商户
     * @return 调研数据报表
     */
    @PostMapping("/statisticTableData")
    @PreAuthorize("@ss.hasPermi('gather:surveyTask:edit')")
    @Log(title = "调研任务", businessType = BusinessType.IMPORT)
    public AjaxResult getStatisticTableData(@RequestParam("taskId") String taskId,
                                            @RequestParam(value = "type") String type,
                                            @RequestParam(value = "operate") String operate){
        return AjaxResult.success(taskService.getStatisticTableData(taskId, type, operate));
    }

    /**
     * 按规则清理数据
     *
     * @param task 调研任务
     */
    @PostMapping("/ruleCleanData")
    @PreAuthorize("@ss.hasPermi('gather:surveyTask:edit')")
    @Log(title = "调研任务", businessType = BusinessType.IMPORT)
    public AjaxResult ruleCleanData(@RequestBody GatherSurveyTask task){
        // 参数判断, 防止规则 Id 和 sPname 长度不一致
        List<String> cleanRules = task.getCrIds();
        List<String> spNames = task.getSpNames();
        cleanRules.removeIf(StringUtils::isBlank);
        spNames.removeIf(StringUtils::isBlank);
        if( cleanRules.size() != spNames.size() ){
            return AjaxResult.error("参数错误");
        }

        int num = switch(task.getType()){
            case "1" -> taskService.cleanGatherStPrice(task.getId(), task.getType(), cleanRules, spNames);
            case "2" -> taskService.cleanGatherStInventory(task.getId(), task.getType(), cleanRules, spNames);
            default -> 0;
        };
        return AjaxResult.success(num);
    }
}

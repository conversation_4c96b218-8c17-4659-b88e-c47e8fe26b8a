package com.hz.web.controller.quarterlyassessments;

import com.hz.common.annotation.Log;
import com.hz.common.core.controller.BaseController;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.core.domain.entity.SysUser;
import com.hz.common.core.page.TableDataInfo;
import com.hz.common.enums.BusinessType;
import com.hz.common.utils.poi.ExcelUtil;
import com.hz.quarterlyassessments.domain.QuarterlyAssessments;
import com.hz.quarterlyassessments.domain.dto.SaveSelectedStaffAndMerchantsRequest;
import com.hz.quarterlyassessments.service.IQuarterlyAssessmentsService;
import com.hz.quarterlyassessments.service.IMonthlyAssessmentStaffService;
import com.hz.quarterlyassessments.service.IMonthlyAssessmentMerchantService;
import com.hz.merchant.domain.MerchantInfo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 季度考核Controller
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/quarterlyassessments/quarterlyassessments")
public class QuarterlyAssessmentsController extends BaseController
{
    @Autowired
    private IQuarterlyAssessmentsService quarterlyAssessmentsService;

    @Autowired
    private IMonthlyAssessmentStaffService monthlyAssessmentStaffService;

    @Autowired
    private IMonthlyAssessmentMerchantService monthlyAssessmentMerchantService;

    /**
     * 查询季度考核列表
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:list')")
    @GetMapping("/list")
    public TableDataInfo list(QuarterlyAssessments quarterlyAssessments)
    {
        startPage();
        List<QuarterlyAssessments> list = quarterlyAssessmentsService.selectQuarterlyAssessmentsList(quarterlyAssessments);
        return getDataTable(list);
    }

    /**
     * 导出季度考核列表
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:export')")
    @Log(title = "季度考核", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QuarterlyAssessments quarterlyAssessments)
    {
        List<QuarterlyAssessments> list = quarterlyAssessmentsService.selectQuarterlyAssessmentsList(quarterlyAssessments);
        ExcelUtil<QuarterlyAssessments> util = new ExcelUtil<QuarterlyAssessments>(QuarterlyAssessments.class);
        util.exportExcel(response, list, "季度考核数据");
    }

    /**
     * 获取季度考核详细信息
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(quarterlyAssessmentsService.selectQuarterlyAssessmentsById(id));
    }

    /**
     * 获取月度考核详细信息（包括人员配置）
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:query')")
    @GetMapping(value = "/monthlyAssessment/detail/{monthlyAssessmentId}")
    public AjaxResult getMonthlyAssessmentDetail(@PathVariable("monthlyAssessmentId") Long monthlyAssessmentId)
    {
        return success(quarterlyAssessmentsService.selectMonthlyAssessmentWithStaffById(monthlyAssessmentId));
    }

    /**
     * 新增季度考核
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:add')")
    @Log(title = "季度考核", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QuarterlyAssessments quarterlyAssessments)
    {
        try {
            int result = quarterlyAssessmentsService.insertQuarterlyAssessments(quarterlyAssessments);
            if (result == -1) {
                // 特殊错误码 -1 表示年份和季度重复
                return AjaxResult.error("该年份第" + quarterlyAssessments.getQuarter() + "季度的考核已存在，请勿重复创建");
            }
            return toAjax(result);
        } catch (Exception e) {
            logger.error("新增季度考核失败", e);
            return AjaxResult.error("新增季度考核失败：" + e.getMessage());
        }
    }

    /**
     * 修改季度考核
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:edit')")
    @Log(title = "季度考核", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QuarterlyAssessments quarterlyAssessments)
    {
        return toAjax(quarterlyAssessmentsService.updateQuarterlyAssessments(quarterlyAssessments));
    }

    /**
     * 发布月度考核
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:edit')")
    @Log(title = "发布月度考核", businessType = BusinessType.UPDATE)
    @PostMapping("/release/{monthlyId}")
    public AjaxResult release(@PathVariable("monthlyId") Long monthlyId)
    {
        return success(quarterlyAssessmentsService.release(monthlyId, "2"));
    }

    /**
     * 发布月度考核
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:edit')")
    @Log(title = "发布月度考核", businessType = BusinessType.UPDATE)
    @PostMapping("/unRelease/{monthlyId}")
    public AjaxResult unRelease(@PathVariable("monthlyId") Long monthlyId)
    {
        return success(quarterlyAssessmentsService.release(monthlyId, "1"));
    }

    /**
     * 删除季度考核
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:remove')")
    @Log(title = "季度考核", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(quarterlyAssessmentsService.deleteQuarterlyAssessmentsByIds(ids));
    }

    /**
     * 同时保存抽取的人员和商户
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:edit')")
    @Log(title = "保存抽取的人员和商户", businessType = BusinessType.UPDATE)
    @PostMapping("/saveSelectedStaffAndMerchants")
    public AjaxResult saveSelectedStaffAndMerchants(@RequestBody SaveSelectedStaffAndMerchantsRequest request)
    {
        try {
            boolean result = quarterlyAssessmentsService.saveSelectedStaffAndMerchants(request);
            if (result) {
                return AjaxResult.success("保存成功");
            } else {
                return AjaxResult.error("保存失败，请检查数据或联系管理员");
            }
        } catch (Exception e) {
            logger.error("保存人员和商户失败", e);
            return AjaxResult.error("保存失败：" + e.getMessage());
        }
    }
}

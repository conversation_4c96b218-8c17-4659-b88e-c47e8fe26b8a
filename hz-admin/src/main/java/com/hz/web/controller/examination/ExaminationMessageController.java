package com.hz.web.controller.examination;

import java.util.List;

import com.hz.examination.domain.ExaminationMessage;
import com.hz.examination.service.IExaminationMessageService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hz.common.annotation.Log;
import com.hz.common.core.controller.BaseController;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.enums.BusinessType;
import com.hz.common.utils.poi.ExcelUtil;
import com.hz.common.core.page.TableDataInfo;

/**
 * 考核消息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@RestController
@RequestMapping("/system/message")
public class ExaminationMessageController extends BaseController
{
    @Autowired
    private IExaminationMessageService examinationMessageService;

    /**
     * 查询考核消息列表
     */
    @PreAuthorize("@ss.hasPermi('system:message:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExaminationMessage examinationMessage)
    {
        startPage();
        String roleKey = getLoginUser().getUser().getRoles().get(0).getRoleKey();
        if ("districtLeaders".equals(roleKey)) {
            examinationMessage.setCreateId(getUserId());
        }
        List<ExaminationMessage> list = examinationMessageService.selectExaminationMessageList(examinationMessage);
        return getDataTable(list);
    }

    /**
     * 查询考核消息列表
     */
    @PreAuthorize("@ss.hasPermi('system:message:list')")
    @GetMapping("/unRead")
    public AjaxResult listByCreate()
    {
        Long createId =null;
        String roleKey = getLoginUser().getUser().getRoles().get(0).getRoleKey();
        if ("districtLeaders".equals(roleKey)) {
            createId = getUserId();
        }
        return success(examinationMessageService.getCountUnRead(createId));
    }

    /**
     * 导出考核消息列表
     */
    @PreAuthorize("@ss.hasPermi('system:message:export')")
    @Log(title = "考核消息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExaminationMessage examinationMessage)
    {
        List<ExaminationMessage> list = examinationMessageService.selectExaminationMessageList(examinationMessage);
        ExcelUtil<ExaminationMessage> util = new ExcelUtil<ExaminationMessage>(ExaminationMessage.class);
        util.exportExcel(response, list, "考核消息数据");
    }

    /**
     * 获取考核消息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:message:query')")
    @GetMapping(value = "/{msgId}")
    public AjaxResult getInfo(@PathVariable("msgId") Long msgId)
    {
        return success(examinationMessageService.selectExaminationMessageByMsgId(msgId));
    }

    /**
     * 新增考核消息
     */
    @PreAuthorize("@ss.hasPermi('system:message:add')")
    @Log(title = "考核消息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExaminationMessage examinationMessage)
    {
        examinationMessage.setCreateId(getUserId());
        examinationMessage.setCreateBy(getUsername());
        return toAjax(examinationMessageService.insertExaminationMessage(examinationMessage));
    }

    /**
     * 修改考核消息
     */
    @PreAuthorize("@ss.hasPermi('system:message:edit')")
    @Log(title = "考核消息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExaminationMessage examinationMessage)
    {
        return toAjax(examinationMessageService.updateExaminationMessage(examinationMessage));
    }

    @PreAuthorize("@ss.hasPermi('system:message:edit')")
    @Log(title = "考核消息", businessType = BusinessType.UPDATE)
    @PostMapping("/reply/{msgId}")
    public AjaxResult reply(@PathVariable("msgId") Long msgId, String msgReply)
    {
        ExaminationMessage examinationMessage = new ExaminationMessage();
        examinationMessage.setMsgId(msgId);
        examinationMessage.setMsgReply(msgReply);
        examinationMessage.setStatus("3");
        examinationMessage.setUpdateId(getUserId());
        examinationMessage.setUpdateBy(getUsername());
        return toAjax(examinationMessageService.updateExaminationMessage(examinationMessage));
    }

    @PreAuthorize("@ss.hasPermi('system:message:edit')")
    @Log(title = "考核消息", businessType = BusinessType.UPDATE)
    @PostMapping("/status/{msgId}")
    public AjaxResult status(@PathVariable("msgId") Long msgId)
    {
        ExaminationMessage examinationMessage = new ExaminationMessage();
        examinationMessage.setMsgId(msgId);
        examinationMessage.setStatus("2");
        examinationMessage.setUpdateId(getUserId());
        examinationMessage.setUpdateBy(getUsername());
        return toAjax(examinationMessageService.updateExaminationMessage(examinationMessage));
    }

    /**
     * 删除考核消息
     */
    @PreAuthorize("@ss.hasPermi('system:message:remove')")
    @Log(title = "考核消息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{msgIds}")
    public AjaxResult remove(@PathVariable Long[] msgIds)
    {
        return toAjax(examinationMessageService.deleteExaminationMessageByMsgIds(msgIds));
    }
}

package com.hz.web.controller.gather;

import java.util.List;
import java.util.stream.Collectors;

import com.hz.gather.domain.GatherCleanRules;
import com.hz.gather.service.IGatherCleanRulesService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hz.common.annotation.Log;
import com.hz.common.core.controller.BaseController;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.enums.BusinessType;
import com.hz.common.utils.poi.ExcelUtil;
import com.hz.common.core.page.TableDataInfo;

/**
 * 香烟数据清理规则Controller
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@RestController
@RequestMapping("/gather/cleanRules")
public class GatherCleanRulesController extends BaseController{

    @Resource
    private IGatherCleanRulesService rulesService;

    /**
     * 查询香烟数据清理规则列表
     */
    @PreAuthorize("@ss.hasPermi('gather:cleanRules:query')")
    @GetMapping("/list")
    public TableDataInfo list(GatherCleanRules rules){
        startPage();
        List<GatherCleanRules> list = rulesService.selectList(rules);
        return getDataTable(list);
    }

    /**
     * 获取香烟数据清理规则详细信息
     */
    @PreAuthorize("@ss.hasPermi('gather:cleanRules:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return success(rulesService.selectById(id));
    }

    /**
     * 新增香烟数据清理规则
     */
    @PreAuthorize("@ss.hasPermi('gather:cleanRules:add')")
    @Log(title = "香烟数据清理规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GatherCleanRules rules){
        return toAjax(rulesService.insert(rules));
    }

    /**
     * 修改香烟数据清理规则
     */
    @PreAuthorize("@ss.hasPermi('gather:cleanRules:edit')")
    @Log(title = "香烟数据清理规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GatherCleanRules rules){
        return toAjax(rulesService.update(rules));
    }

    /**
     * 删除香烟数据清理规则
     */
    @PreAuthorize("@ss.hasPermi('gather:cleanRules:remove')")
    @Log(title = "香烟数据清理规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids){
        return toAjax(rulesService.deleteByIds(ids));
    }

    /**
     * 查询香烟数据清理规则列表
     */
    @PreAuthorize("@ss.hasPermi('gather:cleanRules:query')")
    @GetMapping("/listAll")
    public AjaxResult listAll(GatherCleanRules rules){
        List<GatherCleanRules> list = rulesService.selectList(new GatherCleanRules());
        list = list.stream().filter(o -> o.getType().equals(rules.getType()) || o.getType().equals("3")).collect(Collectors.toList());
        return AjaxResult.success(list);
    }
}

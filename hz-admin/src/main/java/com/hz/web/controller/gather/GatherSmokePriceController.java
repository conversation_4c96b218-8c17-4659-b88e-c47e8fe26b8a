package com.hz.web.controller.gather;

import com.hz.common.annotation.Log;
import com.hz.common.config.HzConfig;
import com.hz.common.constant.Constants;
import com.hz.common.core.controller.BaseController;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.core.domain.entity.SysUser;
import com.hz.common.core.page.TableDataInfo;
import com.hz.common.enums.BusinessType;
import com.hz.common.utils.DateUtils;
import com.hz.common.utils.StringUtils;
import com.hz.common.utils.file.FileUploadUtils;
import com.hz.common.utils.file.FileUtils;
import com.hz.common.utils.poi.ExcelUtil;
import com.hz.gather.domain.GatherSmokePrice;
import com.hz.gather.domain.GatherSurveyTask;
import com.hz.gather.service.IGatherSmokePriceService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 卷烟价格Controller
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@RestController
@RequestMapping("/gather/smokePrice")
public class GatherSmokePriceController extends BaseController{

    @Resource
    private IGatherSmokePriceService priceService;

    /**
     * 查询卷烟价格列表
     */
    @PreAuthorize("@ss.hasPermi('gather:smokePrice:query')")
    @GetMapping("/list")
    public TableDataInfo list(GatherSmokePrice price){
        startPage();
        List<GatherSmokePrice> list = priceService.selectList(price);
        return getDataTable(list);
    }

    /**
     * <h1>导入香烟品规价格</h1>
     */
    @PostMapping("/import")
    @PreAuthorize("@ss.hasPermi('gather:surveyTask:edit')")
    @Log(title = "调研任务", businessType = BusinessType.IMPORT)
    public AjaxResult importFile(MultipartFile file) throws Exception{
        ExcelUtil<GatherSmokePrice> util = new ExcelUtil<GatherSmokePrice>(GatherSmokePrice.class);
        List<GatherSmokePrice> dataList = util.importExcel(file.getInputStream());
        Map<String, String> res = priceService.importSmokePrice(dataList);
        return AjaxResult.success(res);
    }

    /**
     * 导出卷烟价格列表
     */
    @PreAuthorize("@ss.hasPermi('gather:smokePrice:query')")
    @Log(title = "卷烟价格", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GatherSmokePrice price){
        List<GatherSmokePrice> list = priceService.selectList(price);
        ExcelUtil<GatherSmokePrice> util = new ExcelUtil<>(GatherSmokePrice.class);
        util.exportExcel(response, list, "卷烟价格数据");
    }

    /**
     * 获取卷烟价格详细信息
     */
    @PreAuthorize("@ss.hasPermi('gather:smokePrice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return success(priceService.selectById(id));
    }

    /**
     * 新增卷烟价格
     */
    @PreAuthorize("@ss.hasPermi('gather:smokePrice:add')")
    @Log(title = "卷烟价格", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GatherSmokePrice price){
        return toAjax(priceService.insert(price));
    }

    /**
     * 修改卷烟价格
     */
    @PreAuthorize("@ss.hasPermi('gather:smokePrice:edit')")
    @Log(title = "卷烟价格", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GatherSmokePrice price){
        return toAjax(priceService.update(price));
    }

    /**
     * 删除卷烟价格
     */
    @PreAuthorize("@ss.hasPermi('gather:smokePrice:remove')")
    @Log(title = "卷烟价格", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids){
        return toAjax(priceService.deleteByIds(ids));
    }

}

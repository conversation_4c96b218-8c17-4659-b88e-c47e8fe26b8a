package com.hz.web.controller.quarterlyassessments;

import com.hz.common.annotation.Log;
import com.hz.common.core.controller.BaseController;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.core.domain.entity.SysRole;
import com.hz.common.core.domain.entity.SysUser;
import com.hz.common.enums.BusinessType;
import com.hz.quarterlyassessments.domain.dto.SelectStaffRequest;
import com.hz.quarterlyassessments.domain.dto.UpdateMonthlyStaffRequest;
import com.hz.quarterlyassessments.service.IMonthlyAssessmentStaffService;
import com.hz.system.service.ISysRoleService;
import com.hz.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 月度考核人员管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/quarterlyassessments/staff")
public class MonthlyAssessmentStaffController extends BaseController {
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private IMonthlyAssessmentStaffService monthlyAssessmentStaffService;

    /**
     * 获取可用用户列表
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:list')")
    @GetMapping("/users")
    public AjaxResult getUserList() {
        SysUser user = new SysUser();
        user.setStatus("0"); // 只获取正常状态的用户
        List<SysUser> users = userService.selectUserList(user);
        return success(users);
    }

    /**
     * 获取月度考核相关角色列表
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:list')")
    @GetMapping("/roles")
    public AjaxResult getRoleList() {
        SysRole role = new SysRole();
        role.setStatus("0"); // 只获取正常状态的角色
        List<SysRole> roles = roleService.selectRoleList(role);

        // 过滤出月度考核相关的角色
        List<String> monthlyRoleKeys = Arrays.asList("groupLeader", "monopoly", "marketing", "supervise");
        List<SysRole> monthlyRoles = roles.stream()
                .filter(r -> r.getRoleKey() != null && monthlyRoleKeys.contains(r.getRoleKey()))
                .toList();

        return success(monthlyRoles);
    }

    /**
     * 根据用户ID获取用户信息
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:query')")
    @GetMapping("/user/{userId}")
    public AjaxResult getUserById(@PathVariable("userId") Long userId) {
        return success(userService.selectUserById(userId));
    }

    /**
     * 根据角色ID获取角色信息
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:query')")
    @GetMapping("/role/{roleId}")
    public AjaxResult getRoleById(@PathVariable("roleId") Long roleId) {
        return success(roleService.selectRoleById(roleId));
    }

    /**
     * 随机抽取组长 - 每年只能被抽中一次
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:edit')")
    @Log(title = "抽取组长", businessType = BusinessType.OTHER)
    @PostMapping("/selectLeaders")
    public AjaxResult selectRandomLeaders(@RequestBody SelectStaffRequest.SelectLeaderRequest request) {
        try {
            List<SysUser> selectedLeaders = monthlyAssessmentStaffService.selectRandomLeaders(
                    request.getMonthlyAssessmentId(),
                    request.getYear(),
                    request.getCount()
            );

            Map<String, Object> result = new HashMap<>();
            result.put("leaders", selectedLeaders);
            return AjaxResult.success("成功抽取" + selectedLeaders.size() + "名组长", result);
        } catch (Exception e) {
            logger.error("抽取组长失败", e);
            return error("抽取组长失败：" + e.getMessage());
        }
    }

    /**
     * 随机抽取监督人员 - 每半年只能被抽中一次
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:edit')")
    @Log(title = "抽取监督人员", businessType = BusinessType.OTHER)
    @PostMapping("/selectSupervisors")
    public AjaxResult selectRandomSupervisors(@RequestBody SelectStaffRequest.SelectSupervisorRequest request) {
        try {
            List<SysUser> selectedSupervisors = monthlyAssessmentStaffService.selectRandomSupervisors(
                    request.getMonthlyAssessmentId(),
                    request.getYear(),
                    request.getMonth(),
                    request.getCount()
            );

            Map<String, Object> result = new HashMap<>();
            result.put("supervisors", selectedSupervisors);
            return AjaxResult.success("成功抽取" + selectedSupervisors.size() + "名监督人员", result);
        } catch (Exception e) {
            logger.error("抽取监督人员失败", e);
            return error("抽取监督人员失败：" + e.getMessage());
        }
    }

    /**
     * 一键抽取人员（组长+监督人员）
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:edit')")
    @Log(title = "一键抽取人员", businessType = BusinessType.OTHER)
    @PostMapping("/selectAllStaff")
    public AjaxResult selectAllStaff(@RequestBody SelectStaffRequest.SelectAllStaffRequest request) {
        try {
            // 抽取组长
            List<SysUser> selectedLeaders = monthlyAssessmentStaffService.selectRandomLeaders(
                    request.getMonthlyAssessmentId(),
                    request.getYear(),
                    request.getLeaderCount()
            );

            // 抽取监督人员
            List<SysUser> selectedSupervisors = monthlyAssessmentStaffService.selectRandomSupervisors(
                    request.getMonthlyAssessmentId(),
                    request.getYear(),
                    request.getMonth(),
                    request.getSupervisorCount()
            );

            // 保存抽取的人员到月度考核
            boolean saveResult = monthlyAssessmentStaffService.saveSelectedStaff(
                    request.getMonthlyAssessmentId(),
                    selectedLeaders,
                    selectedSupervisors,
                    null, // 一键抽取不包含专卖人员
                    null, // 一键抽取不包含营销人员
                    request.getYear(),
                    request.getMonth()
            );

            if (saveResult) {
                Map<String, Object> result = buildAllStaffSelectionResult(selectedLeaders, selectedSupervisors, request.getMonth());
                return AjaxResult.success("成功抽取并保存人员：组长" + selectedLeaders.size() + "名，监督人员" + selectedSupervisors.size() + "名", result);
            } else {
                return error("抽取成功但保存失败");
            }
        } catch (Exception e) {
            logger.error("一键抽取人员失败", e);
            return error("一键抽取人员失败：" + e.getMessage());
        }
    }

    /**
     * 更新月度考核人员信息
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:edit')")
    @Log(title = "月度考核人员", businessType = BusinessType.UPDATE)
    @PutMapping("/monthly")
    public AjaxResult updateMonthlyAssessmentStaff(@RequestBody UpdateMonthlyStaffRequest request) {
        try {
            if (request.getStaffList() == null || request.getStaffList().isEmpty()) {
                return error("人员列表不能为空");
            }
            boolean result = monthlyAssessmentStaffService.updateMonthlyAssessmentStaff(request);
            if (result) {
                return AjaxResult.success("更新成功");
            } else {
                return error("更新失败");
            }
        } catch (Exception e) {
            logger.error("更新月度考核人员失败", e);
            return error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 构建所有职员选择的结果
     */
    private Map<String, Object> buildAllStaffSelectionResult(List<SysUser> leaders, List<SysUser> supervisors, int month) {
        Map<String, Object> result = new HashMap<>();
        result.put("leaders", leaders);
        result.put("supervisors", supervisors);
        result.put("halfYear", month <= 6 ? "上半年" : "下半年");
        return result;
    }
} 
package com.hz.web.controller.common;

import com.hz.common.core.controller.BaseController;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.core.page.TableDataInfo;
import com.hz.common.utils.poi.ExcelUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.function.Supplier;

/**
 * 基础考核控制器
 * 提供通用的CRUD操作和异常处理方法
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
public abstract class BaseAssessmentController extends BaseController {
    
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 通用分页查询方法
     */
    protected <T> TableDataInfo list(Supplier<List<T>> querySupplier) {
        startPage();
        List<T> list = querySupplier.get();
        return getDataTable(list);
    }

    /**
     * 通用导出方法
     */
    protected <T> void export(HttpServletResponse response, 
                            List<T> list, 
                            Class<T> clazz, 
                            String fileName) {
        ExcelUtil<T> util = new ExcelUtil<T>(clazz);
        util.exportExcel(response, list, fileName);
    }

    /**
     * 安全执行操作并返回结果
     */
    protected AjaxResult safeExecute(Supplier<Boolean> operation, String successMessage, String errorMessage) {
        try {
            boolean result = operation.get();
            if (result) {
                return success(successMessage);
            } else {
                return error(errorMessage);
            }
        } catch (Exception e) {
            logger.error(errorMessage, e);
            return error(errorMessage + "：" + e.getMessage());
        }
    }

    /**
     * 安全执行操作并返回数据
     */
    protected <T> AjaxResult safeExecuteWithData(Supplier<T> operation, String errorMessage) {
        try {
            T result = operation.get();
            return success(result);
        } catch (Exception e) {
            logger.error(errorMessage, e);
            return error(errorMessage + "：" + e.getMessage());
        }
    }

    /**
     * 批量删除操作
     */
    protected AjaxResult batchRemove(Supplier<Integer> deleteOperation) {
        return safeExecute(() -> deleteOperation.get() > 0, "删除成功", "删除失败");
    }

    /**
     * 参数验证
     */
    protected AjaxResult validateRequired(Object value, String fieldName) {
        if (value == null) {
            return error(fieldName + "不能为空");
        }
        if (value instanceof String && ((String) value).trim().isEmpty()) {
            return error(fieldName + "不能为空");
        }
        if (value instanceof List && ((List<?>) value).isEmpty()) {
            return error(fieldName + "不能为空");
        }
        return null;
    }

    /**
     * 批量参数验证
     */
    protected AjaxResult validateMultiple(ValidationRule... rules) {
        for (ValidationRule rule : rules) {
            AjaxResult validationResult = validateRequired(rule.getValue(), rule.getFieldName());
            if (validationResult != null) {
                return validationResult;
            }
        }
        return null;
    }

    /**
     * 验证规则类
     */
    public static class ValidationRule {
        private final Object value;
        private final String fieldName;

        public ValidationRule(Object value, String fieldName) {
            this.value = value;
            this.fieldName = fieldName;
        }

        public Object getValue() {
            return value;
        }

        public String getFieldName() {
            return fieldName;
        }
    }
} 
package com.hz.web.controller.quarterlyassessments;

import java.util.List;
import java.util.Map;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.hz.common.annotation.Log;
import com.hz.common.core.controller.BaseController;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.core.page.TableDataInfo;
import com.hz.common.enums.BusinessType;
import com.hz.common.utils.poi.ExcelUtil;
import com.hz.quarterlyassessments.domain.MonthlyAssessmentMerchant;
import com.hz.quarterlyassessments.service.IMonthlyAssessmentMerchantService;
import com.hz.merchant.domain.MerchantInfo;
import com.hz.quarterlyassessments.domain.dto.MonthlyMerchantAssessmentDto;
import com.hz.quarterlyassessments.domain.dto.SaveSelectedMerchantsRequest;

/**
 * 月度考核商户关联Controller
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@RestController
@RequestMapping("/quarterlyassessments/monthlyMerchant")
public class MonthlyAssessmentMerchantController extends BaseController
{
    @Autowired
    private IMonthlyAssessmentMerchantService monthlyAssessmentMerchantService;

    /**
     * 查询月度考核商户关联列表
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:list')")
    @GetMapping("/list")
    public TableDataInfo list(MonthlyAssessmentMerchant monthlyAssessmentMerchant)
    {
        startPage();
        List<MonthlyAssessmentMerchant> list = monthlyAssessmentMerchantService.selectMonthlyAssessmentMerchantList(monthlyAssessmentMerchant);
        return getDataTable(list);
    }

    /**
     * 根据月度考核ID查询关联的商户列表
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:list')")
    @GetMapping("/listByMonthlyId/{monthlyAssessmentId}")
    public AjaxResult listByMonthlyId(@PathVariable("monthlyAssessmentId") Long monthlyAssessmentId)
    {
        List<MonthlyAssessmentMerchant> list = monthlyAssessmentMerchantService.selectMerchantsByMonthlyAssessmentId(monthlyAssessmentId, "0");
        return success(list);
    }

    /**
     * 导出月度考核商户关联列表
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:export')")
    @Log(title = "月度考核商户关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MonthlyAssessmentMerchant monthlyAssessmentMerchant)
    {
        List<MonthlyAssessmentMerchant> list = monthlyAssessmentMerchantService.selectMonthlyAssessmentMerchantList(monthlyAssessmentMerchant);
        ExcelUtil<MonthlyAssessmentMerchant> util = new ExcelUtil<MonthlyAssessmentMerchant>(MonthlyAssessmentMerchant.class);
        util.exportExcel(response, list, "月度考核商户关联数据");
    }

    /**
     * 获取月度考核商户关联详细信息
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(monthlyAssessmentMerchantService.selectMonthlyAssessmentMerchantById(id,null));
    }

    /**
     * 新增月度考核商户关联
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:add')")
    @Log(title = "月度考核商户关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MonthlyAssessmentMerchant monthlyAssessmentMerchant)
    {
        return toAjax(monthlyAssessmentMerchantService.insertMonthlyAssessmentMerchant(monthlyAssessmentMerchant));
    }

    /**
     * 修改月度考核商户关联
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:edit')")
    @Log(title = "月度考核商户关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MonthlyAssessmentMerchant monthlyAssessmentMerchant)
    {
        return toAjax(monthlyAssessmentMerchantService.updateMonthlyAssessmentMerchant(monthlyAssessmentMerchant));
    }

    /**
     * 删除月度考核商户关联
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:remove')")
    @Log(title = "月度考核商户关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(monthlyAssessmentMerchantService.deleteMonthlyAssessmentMerchantByIds(ids));
    }

    /**
     * 根据月度考核ID删除商户关联
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:remove')")
    @Log(title = "删除月度考核商户关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteByMonthlyId/{monthlyAssessmentId}")
    public AjaxResult deleteByMonthlyId(@PathVariable("monthlyAssessmentId") Long monthlyAssessmentId)
    {
        int result = monthlyAssessmentMerchantService.deleteByMonthlyAssessmentId(monthlyAssessmentId);
        return toAjax(result);
    }

    /**
     * 统计月度考核关联的商户数量
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:list')")
    @GetMapping("/count/{monthlyAssessmentId}")
    public AjaxResult countMerchants(@PathVariable("monthlyAssessmentId") Long monthlyAssessmentId)
    {
        int count = monthlyAssessmentMerchantService.countMerchantsByMonthlyAssessmentId(monthlyAssessmentId);
        return success(count);
    }

    /**
     * 检查商户是否已被关联到指定月度考核
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:list')")
    @GetMapping("/checkAssociation/{monthlyAssessmentId}/{merchantId}")
    public AjaxResult checkAssociation(@PathVariable("monthlyAssessmentId") Long monthlyAssessmentId,
                                     @PathVariable("merchantId") Long merchantId)
    {
        boolean isAssociated = monthlyAssessmentMerchantService.checkMerchantAssociation(monthlyAssessmentId, merchantId);
        return success(isAssociated);
    }





    /**
     * 根据月度考核ID统计商户考核数据（分页）
     */
    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:list')")
    @GetMapping("/summaryByMonthlyId")
    public TableDataInfo summaryByMonthlyIdWithPage(@RequestParam("monthlyAssessmentId") Long monthlyAssessmentId)
    {
//        startPage();
        List<MonthlyMerchantAssessmentDto> list = monthlyAssessmentMerchantService.selectMerchantAssessmentsSummaryByMonthlyId(monthlyAssessmentId);
        return getDataTable(list);
    }
} 
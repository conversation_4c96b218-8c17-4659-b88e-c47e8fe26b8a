package com.hz.web.controller.merchant;

import com.hz.common.annotation.Log;
import com.hz.common.core.controller.BaseController;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.core.page.TableDataInfo;
import com.hz.common.enums.BusinessType;
import com.hz.common.utils.SecurityUtils;
import com.hz.examination.domain.ExaminationTopic;
import com.hz.examination.service.IExaminationTopicService;
import com.hz.merchant.domain.MerchantInfo;
import com.hz.merchant.service.IMerchantInfoService;
import com.hz.quarterlyassessments.domain.MonthlyAssessmentMerchant;
import com.hz.quarterlyassessments.domain.MonthlyAssessments;
import com.hz.quarterlyassessments.domain.MonthlyAssessmentHistory;
import com.hz.quarterlyassessments.domain.MonthlyAssessmentStaff;
import com.hz.quarterlyassessments.domain.dto.UpdateStaffByRoleRequest;
import com.hz.quarterlyassessments.service.IMonthlyAssessmentMerchantService;
import com.hz.quarterlyassessments.service.IQuarterlyAssessmentsService;
import com.hz.merchant.config.MerchantSelectionProperties;
import com.hz.common.core.domain.entity.SysUser;
import com.hz.common.utils.DateUtils;
import com.hz.common.utils.uuid.IdUtils;
import com.hz.quarterlyassessments.mapper.MonthlyAssessmentStaffMapper;

import com.hz.quarterlyassessments.mapper.MonthlyAssessmentHistoryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.text.SimpleDateFormat;


/**
 * 微信小程序对外接口Controller
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@RestController
@RequestMapping("/wx")
public class WxController extends BaseController {

    @Autowired
    private IQuarterlyAssessmentsService quarterlyAssessmentsService;

    @Autowired
    private IMonthlyAssessmentMerchantService monthlyAssessmentMerchantService;

    @Autowired
    private IMerchantInfoService merchantInfoService;

    @Autowired
    private IExaminationTopicService examinationTopicService;

    @Autowired
    private MonthlyAssessmentHistoryMapper monthlyAssessmentHistoryMapper;

    @Autowired
    private MonthlyAssessmentStaffMapper monthlyAssessmentStaffMapper;

    @Autowired
    private MerchantSelectionProperties merchantSelectionProperties;



    /**
     * 小程序接口：分页查询所有月度考核详细信息（包括人员配置）
     * 根据当前登录用户的角色进行数据筛选
     */
//    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:list')")
    @GetMapping("/monthlyAssessments/list")
    public TableDataInfo getMonthlyAssessmentsList(MonthlyAssessments monthlyAssessments)
    {
        try {
            // 获取当前用户信息
            Long currentUserId = SecurityUtils.getUserId();

            // 如果是管理员，直接分页查询所有数据
            if (SecurityUtils.isAdmin(currentUserId)) {
                startPage();
                List<MonthlyAssessments> result = quarterlyAssessmentsService.selectMonthlyAssessmentsWithStaffList(monthlyAssessments);
                return getDataTable(result);
            }

            // 非管理员用户：只查询用户参与的月度考核
            String currentUserName = SecurityUtils.getUsername();
            startPage();
            List<MonthlyAssessments> result = quarterlyAssessmentsService.selectMonthlyAssessmentsWithStaffListByUser(monthlyAssessments, currentUserName);
            return getDataTable(result);

        } catch (Exception e) {
            logger.error("分页查询月度考核详细信息失败", e);
            return getDataTable(List.of());
        }
    }

    /**
     * 根据许可证号查询商户信息
     */
//    @PreAuthorize("@ss.hasPermi('merchant:info:query')")
    @GetMapping("/merchant/licence/{licence}")
    public AjaxResult getMerchantByLicence(@PathVariable("licence") String licence)
    {
        try {
            MerchantInfo merchantInfo = merchantInfoService.selectMerchantInfoByLicence(licence);
            if (merchantInfo != null) {
                return success(merchantInfo);
            } else {
                return error("未找到许可证号为 " + licence + " 的商户信息");
            }
        } catch (Exception e) {
            logger.error("根据许可证号查询商户信息失败", e);
            return error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据月度考核ID查询关联的商户列表
     */
//    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:list')")
    @GetMapping("/monthlyAssessments/merchants/{monthlyAssessmentId}")
    public AjaxResult getMerchantsByMonthlyId(@PathVariable("monthlyAssessmentId") Long monthlyAssessmentId)
    {
        try {
            List<MonthlyAssessmentMerchant> list = monthlyAssessmentMerchantService.selectMerchantsByMonthlyAssessmentId(monthlyAssessmentId, null);
            
            // 获取当前用户的签字状态
            String userSignatureStatus = "0"; // 默认未签字
            try {
                Long currentUserId = SecurityUtils.getUserId();
                if (currentUserId != null) {
                    MonthlyAssessmentHistory historyRecord = monthlyAssessmentHistoryMapper.selectByUserIdAndMonthlyAssessmentId(currentUserId, monthlyAssessmentId);
                    if (historyRecord != null && historyRecord.getSignatureStatus() != null) {
                        userSignatureStatus = historyRecord.getSignatureStatus();
                    }
                }
            } catch (Exception e) {
                logger.warn("获取当前用户签字状态失败，使用默认值", e);
            }
            
            // 获取月度考核的状态
            String monthlyAssessmentStatus = null;
            String monthlyAssessmentStatusText = null;
            try {
                MonthlyAssessments monthlyAssessment = quarterlyAssessmentsService.selectMonthlyAssessmentWithStaffById(monthlyAssessmentId);
                if (monthlyAssessment != null) {
                    monthlyAssessmentStatus = monthlyAssessment.getStatus();
                    monthlyAssessmentStatusText = monthlyAssessment.getStatusText();
                }
            } catch (Exception e) {
                logger.warn("获取月度考核状态失败，使用默认值", e);
            }
            
            // 构建返回数据，包含商户列表、用户签字状态和月度考核状态
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("merchants", list);
            resultData.put("signatureStatus", userSignatureStatus);
            resultData.put("monthlyAssessmentStatus", monthlyAssessmentStatus);
            resultData.put("monthlyAssessmentStatusText", monthlyAssessmentStatusText);
            
            return success(resultData);
        } catch (Exception e) {
            logger.error("根据月度考核ID查询关联商户失败", e);
            return error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据月度考核商户关联ID查询单个商户详情
     */
//    @PreAuthorize("@ss.hasPermi('quarterlyassessments:quarterlyassessments:query')")
    @GetMapping("/monthlyAssessments/merchant/{id}")
    public AjaxResult getMerchantDetailById(@PathVariable("id") Long id)
    {
        try {
            String roleKey = getLoginUser().getUser().getRoles().get(0).getRoleKey();
            String type = null;
            if("monopoly".equals(roleKey)) {
                type = "专卖";
            } else if("marketing".equals(roleKey)) {
                type = "营销";
            }
            MonthlyAssessmentMerchant merchantDetail = monthlyAssessmentMerchantService.selectMonthlyAssessmentMerchantById(id, type);
            if (merchantDetail != null) {
                return success(merchantDetail);
            } else {
                return error("未找到ID为 " + id + " 的商户考核记录");
            }
        } catch (Exception e) {
            logger.error("根据ID查询商户考核详情失败", e);
            return error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 更新签到签退状态（根据用户角色自动判断专卖或营销）
     *
     * @param id 月度考核商户关联ID
     * @param status 状态值：0-未签到，1-已签到，2-已提交表单，3-已签退
     */
    @Log(title = "月度考核商户状态更新", businessType = BusinessType.UPDATE)
    @PostMapping("/updateStatus/{id}/{status}")
    public AjaxResult updateStatus(@PathVariable("id") Long id, @PathVariable("status") String status)
    {
        try {
            // 验证状态值
            if (!"0".equals(status) && !"1".equals(status) && !"2".equals(status) && !"3".equals(status)) {
                return error("无效的状态值，状态值必须为：0-未签到，1-已签到，2-已提交表单，3-已签退");
            }

            // 获取当前用户角色
            String roleKey = getLoginUser().getUser().getRoles().get(0).getRoleKey();
            String roleType = null;
            boolean isZhuanmai = false;
            boolean isYingxiao = false;
            
            if("monopoly".equals(roleKey)) {
                roleType = "专卖";
                isZhuanmai = true;
            } else if("marketing".equals(roleKey)) {
                roleType = "营销";
                isYingxiao = true;
            } else {
                return error("当前用户角色不支持签到签退操作，仅支持专卖(monopoly)和营销(marketing)角色");
            }

            // 根据角色调用对应的状态更新方法
            int result = 0;
            if (isZhuanmai) {
                result = monthlyAssessmentMerchantService.updateZhuanmaiCheckStatus(id, status);
            } else if (isYingxiao) {
                result = monthlyAssessmentMerchantService.updateYingxiaoCheckStatus(id, status);
            }

            if (result > 0) {
                // 如果是签退操作（状态为3），在成功更新后检查是否达到配置数量，如果达到则更新月度考核状态
                if ("3".equals(status)) {
                    MonthlyAssessmentMerchant currentRecord = monthlyAssessmentMerchantService.selectMonthlyAssessmentMerchantById(id, null);
                    if (currentRecord != null) {
                        // 统计专卖和营销都签退的商户数量（同一商户的专卖和营销状态都为3才算1个完成）
                        int bothCheckoutCount = monthlyAssessmentMerchantService.countBothCheckStatusThreeByMonthlyAssessmentId(currentRecord.getMonthlyAssessmentId());

                        if (bothCheckoutCount >= merchantSelectionProperties.getTotalCount()) {
                            // 达到配置数量，更新月度考核状态为"3-需签字"
                            MonthlyAssessments monthlyAssessment = new MonthlyAssessments();
                            monthlyAssessment.setId(String.valueOf(currentRecord.getMonthlyAssessmentId()));
                            monthlyAssessment.setStatus("3"); // 需签字
                            quarterlyAssessmentsService.updateMonthlyAssessments(monthlyAssessment);
                        }
                    }
                }

                // 状态更新成功后，重新查询该记录并返回时间信息
                MonthlyAssessmentMerchant updatedRecord = monthlyAssessmentMerchantService.selectMonthlyAssessmentMerchantById(id, roleType);
                if (updatedRecord != null) {
                    // 根据角色返回对应字段
                    Map<String, Object> result_data = new HashMap<>();

                    // 格式化时间，只保留到时分秒
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    
                    if (isZhuanmai) {
                        result_data.put("checkInTime", updatedRecord.getZhuanmaiCheckInTime() != null ? dateFormat.format(updatedRecord.getZhuanmaiCheckInTime()) : null);
                        result_data.put("checkOutTime", updatedRecord.getZhuanmaiCheckOutTime() != null ? dateFormat.format(updatedRecord.getZhuanmaiCheckOutTime()) : null);
                        result_data.put("checkStatus", updatedRecord.getZhuanmaiCheckStatus());
                    } else if (isYingxiao) {
                        result_data.put("checkInTime", updatedRecord.getYingxiaoCheckInTime() != null ? dateFormat.format(updatedRecord.getYingxiaoCheckInTime()) : null);
                        result_data.put("checkOutTime", updatedRecord.getYingxiaoCheckOutTime() != null ? dateFormat.format(updatedRecord.getYingxiaoCheckOutTime()) : null);
                        result_data.put("checkStatus", updatedRecord.getYingxiaoCheckStatus());
                    }

                    String message = getStatusMessage(status, roleType);
                    return success(message).put("data", result_data);
                } else {
                    String message = getStatusMessage(status, roleType);
                    return success(message);
                }
            } else {
                return error(roleType + "状态更新失败，请检查记录是否存在");
            }
        } catch (Exception e) {
            logger.error("状态更新失败", e);
            return error("状态更新失败：" + e.getMessage());
        }
    }

    /**
     * 根据状态值和角色获取操作描述
     */
    private String getStatusMessage(String status, String roleType) {
        String prefix = roleType != null ? roleType : "";
        switch (status) {
            case "0":
                return prefix + "状态重置成功";
            case "1":
                return prefix + "签到成功";
            case "2":
                return prefix + "表单提交成功";
            case "3":
                return prefix + "签退成功";
            default:
                return prefix + "状态更新成功";
        }
    }

    @GetMapping("/getPaperByMonthlyId")
    public AjaxResult getPaperByMonthlyId(Long paperId, Long merchantId)
    {
        return success(examinationTopicService.selectWxExaminationTopicByPaperId(paperId,merchantId));
    }

    /**
     * 批量新增考核题目
     */
    @PostMapping(value = "/paperBatch/{monthlyId}/{paperId}/{merchantId}")
    public AjaxResult batchAdd(@PathVariable("monthlyId") Long monthlyId, @PathVariable("paperId") Long paperId, @PathVariable("merchantId") Long merchantId,
                               @RequestBody List<ExaminationTopic> examinationTopic)
    {
        String roleKey = getLoginUser().getUser().getRoles().get(0).getRoleKey();
        boolean isMonopoly;
        if ("monopoly".equals(roleKey)) {
            isMonopoly = true;
        } else if("marketing".equals(roleKey)) {
            isMonopoly = false;
        } else {
            return error("操作人员无权限..");
        }
        return success(examinationTopicService.batchInsertExaminationTopicByWx(examinationTopic,monthlyId,paperId,merchantId,getUserId(),getUsername(),isMonopoly));
    }

    /**
     * 将有证商户添加到月度考核关联的商户表
     *
     * @param monthlyAssessmentId 月度考核ID
     * @param merchantId 商户ID
     */
    @Log(title = "添加有证商户到月度考核", businessType = BusinessType.INSERT)
    @PostMapping("/addMerchantToMonthly/{monthlyAssessmentId}/{merchantId}/{selectionOrder}")
    public AjaxResult addMerchantToMonthly(
            @PathVariable("monthlyAssessmentId") Long monthlyAssessmentId,
            @PathVariable("merchantId") Long merchantId,
            @PathVariable("selectionOrder") Integer selectionOrder)

    {
        try {
            // 1. 参数验证
            if (monthlyAssessmentId == null || monthlyAssessmentId <= 0) {
                return error("月度考核ID不能为空且必须大于0");
            }

            if (merchantId == null || merchantId <= 0) {
                return error("商户ID不能为空且必须大于0");
            }

            // 2. 检查商户是否已经关联到该月度考核
            boolean isAssociated = monthlyAssessmentMerchantService.checkMerchantAssociation(monthlyAssessmentId, merchantId);
            if (isAssociated) {
                return error("该商户已经关联到当前月度考核，无需重复添加");
            }

            // 3. 构建新的月度考核商户关联记录
            MonthlyAssessmentMerchant newAssociation = new MonthlyAssessmentMerchant();
            newAssociation.setMonthlyAssessmentId(monthlyAssessmentId);
            newAssociation.setMerchantId(merchantId);
            newAssociation.setSelectionOrder(selectionOrder);
            newAssociation.setSelectionType("1");
            newAssociation.setAssessmentStatus("0"); // 默认未闭店
            newAssociation.setZhuanmaiCheckStatus("0"); // 默认专卖未签到
            newAssociation.setYingxiaoCheckStatus("0"); // 默认营销未签到

            // 4. 保存到数据库
            int result = monthlyAssessmentMerchantService.insertMonthlyAssessmentMerchant(newAssociation);

            if (result > 0) {
                return success("商户已成功添加到月度考核");
            } else {
                return error("添加商户到月度考核失败，请重试");
            }

        } catch (Exception e) {
            logger.error("添加有证商户到月度考核失败", e);
            return error("添加有证商户到月度考核失败：" + e.getMessage());
        }
    }

    /**
     * 添加无证户到月度考核关联的商户表
     *
     * @param monthlyAssessmentId 月度考核ID
     * @param merchantInfoJson 商户信息JSON，包含：merchantName(商户名称), county(所属县域),
     *                        legalName(商户负责人), businessAddress(商户地址), photoUrl(门头照片地址)
     */
    @Log(title = "添加无证户到月度考核", businessType = BusinessType.INSERT)
    @PostMapping("/addUnlicensedMerchantToMonthly/{monthlyAssessmentId}/{selectionOrder}")
    public AjaxResult addUnlicensedMerchantToMonthly(
            @PathVariable("monthlyAssessmentId") Long monthlyAssessmentId,@PathVariable("selectionOrder") Integer selectionOrder,
            @RequestBody String merchantInfoJson)
    {
        try {
            // 1. 参数验证
            if (monthlyAssessmentId == null || monthlyAssessmentId <= 0) {
                return error("月度考核ID不能为空且必须大于0");
            }

            if (selectionOrder == null || selectionOrder > 99) {
                return error("排序不能为空或大于99");
            }

            if (merchantInfoJson == null || merchantInfoJson.trim().isEmpty()) {
                return error("商户信息不能为空");
            }

            // 2. 解析JSON数据
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode;
            try {
                jsonNode = objectMapper.readTree(merchantInfoJson);
            } catch (Exception e) {
                return error("商户信息JSON格式错误：" + e.getMessage());
            }

            // 3. 提取并验证必填字段
            String merchantName = getJsonStringValue(jsonNode, "merchantName");
            String county = getJsonStringValue(jsonNode, "county");
            String legalName = getJsonStringValue(jsonNode, "legalName");
            String businessAddress = getJsonStringValue(jsonNode, "businessAddress");
            String photoUrl = getJsonStringValue(jsonNode, "photo");

            // 验证必填字段
            if (merchantName == null || merchantName.trim().isEmpty()) {
                return error("商户名称不能为空");
            }
            if (legalName == null || legalName.trim().isEmpty()) {
                return error("商户负责人不能为空");
            }
            if (businessAddress == null || businessAddress.trim().isEmpty()) {
                return error("商户地址不能为空");
            }

            // 4. 使用特殊ID表示无证户
            Long unlicensedMerchantId = -(monthlyAssessmentId*100+selectionOrder); // 特殊ID，表示无证户

            // 5. 构建新的月度考核商户关联记录
            MonthlyAssessmentMerchant newAssociation = new MonthlyAssessmentMerchant();
            newAssociation.setMonthlyAssessmentId(monthlyAssessmentId);
            newAssociation.setMerchantId(unlicensedMerchantId); // 使用特殊ID
            newAssociation.setSelectionOrder(selectionOrder);
            newAssociation.setSelectionType("2"); // 无证户
            newAssociation.setAssessmentStatus("0"); // 默认未闭店
            newAssociation.setZhuanmaiCheckStatus("0"); // 默认专卖未签到
            newAssociation.setYingxiaoCheckStatus("0"); // 默认营销未签到

            // 6. 将完整的无证户信息存储在remark字段中（JSON格式）
            Map<String, Object> unlicensedInfo = new HashMap<>();
            unlicensedInfo.put("merchantName", merchantName.trim());
            unlicensedInfo.put("county", county != null ? county.trim() : "");
            unlicensedInfo.put("legalName", legalName.trim());
            unlicensedInfo.put("businessAddress", businessAddress.trim());
            unlicensedInfo.put("photo", photoUrl != null ? photoUrl.trim() : "");
            unlicensedInfo.put("type", "unlicensed"); // 标识为无证户

            String remarkJson = objectMapper.writeValueAsString(unlicensedInfo);
            newAssociation.setRemark(remarkJson);

            // 7. 保存到数据库
            int result = monthlyAssessmentMerchantService.insertMonthlyAssessmentMerchant(newAssociation);

            if (result > 0) {
                // 8. 返回成功响应
                Map<String, Object> responseData = new HashMap<>();
                responseData.put("merchantId", unlicensedMerchantId);
                responseData.put("merchantName", merchantName.trim());
                responseData.put("county", county);
                responseData.put("legalName", legalName.trim());
                responseData.put("businessAddress", businessAddress.trim());
                responseData.put("photo", photoUrl);
                responseData.put("selectionType", "2");

                return success("无证户已成功添加到月度考核")
                        .put("data", responseData);
            } else {
                return error("添加无证户到月度考核失败，请重试");
            }

        } catch (Exception e) {
            logger.error("添加无证户到月度考核失败", e);
            return error("添加无证户到月度考核失败：" + e.getMessage());
        }
    }

    /**
     * 从JsonNode中安全获取字符串值
     */
    private String getJsonStringValue(JsonNode jsonNode, String fieldName) {
        JsonNode fieldNode = jsonNode.get(fieldName);
        if (fieldNode != null && !fieldNode.isNull()) {
            return fieldNode.asText();
        }
        return null;
    }

    /**
     * 设置商户为闭店状态
     *
     * @param id 月度考核商户关联ID
     */
    @Log(title = "设置商户闭店状态", businessType = BusinessType.UPDATE)
    @PostMapping("/setMerchantClosed/{id}")
    public AjaxResult setMerchantClosed(@PathVariable("id") Long id)
    {
        try {
            // 验证ID
            if (id == null || id <= 0) {
                return error("月度考核商户关联ID不能为空且必须大于0");
            }

            // 直接设置为闭店状态（1）
            int result = monthlyAssessmentMerchantService.updateAssessmentStatus(id, "1");
            if (result > 0) {
                return success("商户已设置为闭店状态");
            } else {
                return error("设置闭店状态失败，请检查记录是否存在");
            }
        } catch (Exception e) {
            logger.error("设置闭店状态失败", e);
            return error("设置闭店状态失败：" + e.getMessage());
        }
    }

    /**
     * 设置月度考核历史记录为已签字状态
     *
     * @param monthlyAssessmentId 月度考核ID
     * 
     * @param userId 用户ID（可选，不传则使用当前登录用户）
     */
    @Log(title = "设置月度考核历史记录签字状态", businessType = BusinessType.UPDATE)
    @PostMapping("/setSignatureStatus/{monthlyAssessmentId}")
    public AjaxResult setSignatureStatus(@PathVariable("monthlyAssessmentId") Long monthlyAssessmentId,
                                       @RequestParam(value = "userId", required = false) Long userId)
    {
        try {
            // 验证参数
            if (monthlyAssessmentId == null || monthlyAssessmentId <= 0) {
                return error("月度考核ID不能为空且必须大于0");
            }

            // 如果没有传入userId，则使用当前登录用户的ID
            if (userId == null) {
                userId = SecurityUtils.getUserId();
                if (userId == null) {
                    return error("无法获取当前用户信息，请重新登录");
                }
            }

            // 先检查该用户在该月度考核中是否有历史记录
            MonthlyAssessmentHistory existingRecord = monthlyAssessmentHistoryMapper.selectByUserIdAndMonthlyAssessmentId(userId, monthlyAssessmentId);
            if (existingRecord == null) {
                logger.error("签字失败：未找到用户ID {} 在月度考核ID {} 中的历史记录", userId, monthlyAssessmentId);
                return error("签字失败：您未参与当前月度考核，无法进行签字操作");
            }
            
            logger.info("找到历史记录 - 用户ID: {}, 月度考核ID: {}, 当前签字状态: {}", userId, monthlyAssessmentId, existingRecord.getSignatureStatus());
            
            // 直接执行更新，设置为已签字状态（1）
            // 使用月度考核ID和用户ID来更新签字状态，不依赖年份
            logger.info("准备更新签字状态 - 用户ID: {}, 月度考核ID: {}", userId, monthlyAssessmentId);
            int result = monthlyAssessmentHistoryMapper.updateSignatureStatusByUserIdAndMonthlyAssessmentId(userId, monthlyAssessmentId, "1");
            logger.info("签字状态更新结果: {} (受影响行数)", result);
            
            if (result > 0) {
                // 签字成功后，检查是否所有人员都已签字，如果是则更新月度考核状态为4（已完成）
                // 统计当前已签字人数
                logger.info("开始统计签字情况 - 月度考核ID: {}", monthlyAssessmentId);
                int currentSignedCount = monthlyAssessmentHistoryMapper.countSignedByMonthlyAssessmentId(monthlyAssessmentId);
                // 统计应该签字的总人数
                int totalStaffCount = monthlyAssessmentHistoryMapper.countTotalStaffByMonthlyAssessmentId(monthlyAssessmentId);
                
                logger.info("签字统计结果 - 月度考核ID: {}, 已签字人数: {}, 应签字总人数: {}", monthlyAssessmentId, currentSignedCount, totalStaffCount);

                if (currentSignedCount >= totalStaffCount && totalStaffCount > 0) {
                    logger.info("所有人员已签字，准备更新月度考核状态为已完成");
                    // 所有人员都已签字，更新月度考核状态为"4-已完成"
                    MonthlyAssessments updateMonthlyAssessment = new MonthlyAssessments();
                    String monthlyAssessmentIdStr = String.valueOf(monthlyAssessmentId);
                    updateMonthlyAssessment.setId(monthlyAssessmentIdStr);
                    updateMonthlyAssessment.setStatus("4"); // 已完成
                    
                    logger.info("准备更新月度考核 - ID: {}, 新状态: {}", monthlyAssessmentIdStr, "4");
                    int updateResult = quarterlyAssessmentsService.updateMonthlyAssessments(updateMonthlyAssessment);
                    logger.info("月度考核状态更新结果: {} (0表示未更新到任何记录，>0表示更新成功)", updateResult);

                    if (updateResult <= 0) {
                        logger.error("月度考核状态更新失败 - 可能是ID不存在或其他问题");
                    }
                } else {
                    logger.info("尚未达到全员签字条件，不更新状态 - 当前签字人数: {}, 总人数: {}", currentSignedCount, totalStaffCount);
                }
                return success("签字成功");
            }
            return error("签字失败");
        } catch (Exception e) {
            logger.error("签字失败", e);
            return error("签字失败：" + e.getMessage());
        }
    }

    /**
     * 查询月度考核历史记录的签字状态
     *
     * @param monthlyAssessmentId 月度考核ID
     * @param userId 用户ID（可选，不传则使用当前登录用户）
     */
    @GetMapping("/getSignatureStatus/{monthlyAssessmentId}")
    public AjaxResult getSignatureStatus(@PathVariable("monthlyAssessmentId") Long monthlyAssessmentId,
                                       @RequestParam(value = "userId", required = false) Long userId)
    {
        try {
            // 验证参数
            if (monthlyAssessmentId == null || monthlyAssessmentId <= 0) {
                return error("月度考核ID不能为空且必须大于0");
            }

            // 如果没有传入userId，则使用当前登录用户的ID
            if (userId == null) {
                userId = SecurityUtils.getUserId();
                if (userId == null) {
                    return error("无法获取当前用户信息，请重新登录");
                }
            }

            // 查询记录
            // 使用月度考核ID和用户ID来查询签字状态，不依赖年份
            MonthlyAssessmentHistory record = monthlyAssessmentHistoryMapper.selectByUserIdAndMonthlyAssessmentId(userId, monthlyAssessmentId);

            if (record != null) {
                Map<String, Object> resultData = new HashMap<>();
                resultData.put("userId", record.getUserId());
                resultData.put("monthlyAssessmentId", record.getMonthlyAssessmentId());
                resultData.put("year", record.getYear());
                resultData.put("signatureStatus", record.getSignatureStatus() != null ? record.getSignatureStatus() : "0");
                resultData.put("statusText", "1".equals(record.getSignatureStatus()) ? "已签字" : "未签字");
                resultData.put("createdAt", record.getCreatedAt());

                return success("查询成功").put("data", resultData);
            } else {
                return error("未找到用户ID " + userId + " 的月度考核ID " + monthlyAssessmentId + " 的历史记录");
            }

        } catch (Exception e) {
            logger.error("查询签字状态失败", e);
            return error("查询签字状态失败：" + e.getMessage());
        }
    }

    /**
     * 更新专卖人员
     *
     * @param request 更新专卖人员请求
     */
    @Log(title = "更新专卖人员", businessType = BusinessType.UPDATE)
    @PostMapping("/updateMonopolyStaff")
    public AjaxResult updateMonopolyStaff(@RequestBody UpdateStaffByRoleRequest request)
    {
        try {
            // 验证参数
            if (request.getMonthlyAssessmentId() == null || request.getMonthlyAssessmentId() <= 0) {
                return error("月度考核ID不能为空且必须大于0");
            }

            if (request.getStaffList() == null) {
                return error("人员列表不能为null");
            }

            Long monthlyAssessmentId = request.getMonthlyAssessmentId();
            List<SysUser> monopolyStaffs = request.getStaffList();

            // 先删除现有的专卖人员记录
            monthlyAssessmentStaffMapper.deleteMonthlyAssessmentStaffByMonthlyIdAndRoleId(
                monthlyAssessmentId.toString(), 104L);

            // 如果人员列表不为空，则插入新记录
            if (!monopolyStaffs.isEmpty()) {
                java.util.Date now = DateUtils.getNowDate();

                for (SysUser monopolyStaff : monopolyStaffs) {
                    MonthlyAssessmentStaff staff = new MonthlyAssessmentStaff();
                    staff.setId(IdUtils.fastSimpleUUID());
                    staff.setMonthlyAssessmentId(monthlyAssessmentId);
                    staff.setUserId(monopolyStaff.getUserId());
                    staff.setRoleId(104L);
                    staff.setCreatedAt(now);
                    monthlyAssessmentStaffMapper.insertMonthlyAssessmentStaff(staff);
                }
            }

            return success("专卖人员更新成功，共更新 " + monopolyStaffs.size() + " 名人员");

        } catch (Exception e) {
            logger.error("更新专卖人员失败", e);
            return error("更新专卖人员失败：" + e.getMessage());
        }
    }

    /**
     * 更新营销人员
     *
     * @param request 更新营销人员请求
     */
    @Log(title = "更新营销人员", businessType = BusinessType.UPDATE)
    @PostMapping("/updateMarketingStaff")
    public AjaxResult updateMarketingStaff(@RequestBody UpdateStaffByRoleRequest request)
    {
        try {
            // 验证参数
            if (request.getMonthlyAssessmentId() == null || request.getMonthlyAssessmentId() <= 0) {
                return error("月度考核ID不能为空且必须大于0");
            }

            if (request.getStaffList() == null) {
                return error("人员列表不能为null");
            }

            Long monthlyAssessmentId = request.getMonthlyAssessmentId();
            List<SysUser> marketingStaffs = request.getStaffList();

            // 先删除现有的营销人员记录
            monthlyAssessmentStaffMapper.deleteMonthlyAssessmentStaffByMonthlyIdAndRoleId(
                monthlyAssessmentId.toString(), 105L);

            // 如果人员列表不为空，则插入新记录
            if (!marketingStaffs.isEmpty()) {
                java.util.Date now = DateUtils.getNowDate();

                for (SysUser marketingStaff : marketingStaffs) {
                    MonthlyAssessmentStaff staff = new MonthlyAssessmentStaff();
                    staff.setId(IdUtils.fastSimpleUUID());
                    staff.setMonthlyAssessmentId(monthlyAssessmentId);
                    staff.setUserId(marketingStaff.getUserId());
                    staff.setRoleId(105L);
                    staff.setCreatedAt(now);
                    monthlyAssessmentStaffMapper.insertMonthlyAssessmentStaff(staff);
                }
            }

            return success("营销人员更新成功，共更新 " + marketingStaffs.size() + " 名人员");

        } catch (Exception e) {
            logger.error("更新营销人员失败", e);
            return error("更新营销人员失败：" + e.getMessage());
        }
    }

    /**
     * 监督人员分配商户和人员
     *
     * @param request 分配请求
     */
    @Log(title = "监督人员分配商户和人员", businessType = BusinessType.UPDATE)
    @PostMapping("/assignMerchantsAndStaff")
    public AjaxResult assignMerchantsAndStaff(@RequestBody SupervisorAssignmentRequest request)
    {
        try {
            // 验证参数
            if (request.getMonthlyAssessmentId() == null || request.getMonthlyAssessmentId() <= 0) {
                return error("月度考核ID不能为空且必须大于0");
            }

            if (request.getSupervisorId() == null || request.getSupervisorId() <= 0) {
                return error("监督人员ID不能为空且必须大于0");
            }

            if (request.getMerchantIds() == null || request.getMerchantIds().isEmpty()) {
                return error("商户ID列表不能为空");
            }

            Long monthlyAssessmentId = request.getMonthlyAssessmentId();
            Long supervisorId = request.getSupervisorId();
            List<Long> merchantIds = request.getMerchantIds();
            List<SysUser> monopolyStaffs = request.getMonopolyStaffs();
            List<SysUser> marketingStaffs = request.getMarketingStaffs();

            // 1. 更新商户的考核人员ID为监督人员ID
            int updatedMerchants = monthlyAssessmentMerchantService.updateAssessorIdByMerchantIds(
                monthlyAssessmentId, merchantIds, supervisorId);

            // 2. 更新专卖人员（如果提供）
            if (monopolyStaffs != null && !monopolyStaffs.isEmpty()) {
                // 先删除现有的专卖人员记录
                monthlyAssessmentStaffMapper.deleteMonthlyAssessmentStaffByMonthlyIdAndRoleId(
                    monthlyAssessmentId.toString(), 104L);

                // 插入新的专卖人员记录
                java.util.Date now = DateUtils.getNowDate();
                for (SysUser monopolyStaff : monopolyStaffs) {
                    MonthlyAssessmentStaff staff = new MonthlyAssessmentStaff();
                    staff.setId(IdUtils.fastSimpleUUID());
                    staff.setMonthlyAssessmentId(monthlyAssessmentId);
                    staff.setUserId(monopolyStaff.getUserId());
                    staff.setRoleId(104L);
                    staff.setCreatedAt(now);
                    monthlyAssessmentStaffMapper.insertMonthlyAssessmentStaff(staff);
                }
            }

            // 3. 更新营销人员（如果提供）
            if (marketingStaffs != null && !marketingStaffs.isEmpty()) {
                // 先删除现有的营销人员记录
                monthlyAssessmentStaffMapper.deleteMonthlyAssessmentStaffByMonthlyIdAndRoleId(
                    monthlyAssessmentId.toString(), 105L);

                // 插入新的营销人员记录
                java.util.Date now = DateUtils.getNowDate();
                for (SysUser marketingStaff : marketingStaffs) {
                    MonthlyAssessmentStaff staff = new MonthlyAssessmentStaff();
                    staff.setId(IdUtils.fastSimpleUUID());
                    staff.setMonthlyAssessmentId(monthlyAssessmentId);
                    staff.setUserId(marketingStaff.getUserId());
                    staff.setRoleId(105L);
                    staff.setCreatedAt(now);
                    monthlyAssessmentStaffMapper.insertMonthlyAssessmentStaff(staff);
                }
            }

            String message = String.format("分配成功：更新了%d个商户的考核人员", updatedMerchants);
            if (monopolyStaffs != null) {
                message += String.format("，专卖人员%d名", monopolyStaffs.size());
            }
            if (marketingStaffs != null) {
                message += String.format("，营销人员%d名", marketingStaffs.size());
            }

            return success(message);

        } catch (Exception e) {
            logger.error("监督人员分配商户和人员失败", e);
            return error("分配失败：" + e.getMessage());
        }
    }

}

package com.hz.web.controller.examination;

import java.util.List;

import com.hz.examination.domain.ExaminationPaper;
import com.hz.examination.service.IExaminationPaperService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.hz.common.annotation.Log;
import com.hz.common.core.controller.BaseController;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.enums.BusinessType;
import com.hz.common.utils.poi.ExcelUtil;
import com.hz.common.core.page.TableDataInfo;

/**
 * 考核Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/examination/paper")
public class ExaminationPaperController extends BaseController
{
    @Autowired
    private IExaminationPaperService examinationPaperService;

    /**
     * 查询考核列表
     */
    @PreAuthorize("@ss.hasPermi('examination:paper:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExaminationPaper examinationPaper)
    {
        startPage();
        List<ExaminationPaper> list = examinationPaperService.selectExaminationPaperList(examinationPaper);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('examination:paper:list')")
    @GetMapping("/listByTypeAndBrand")
    public AjaxResult listByTypeAndBrand(@RequestParam String type, @RequestParam String brand)
    {
        return success(examinationPaperService.selectExaminationPaperListByTypeAndBrand(type, brand));
    }

    @PreAuthorize("@ss.hasPermi('examination:paper:list')")
    @GetMapping("/monthlyId")
    public AjaxResult listByMonthlyId(Long monthlyId)
    {
        return success(examinationPaperService.selectExaminationPaperList(monthlyId));
    }

    /**
     * 导出考核列表
     */
    @PreAuthorize("@ss.hasPermi('examination:paper:export')")
    @Log(title = "考核", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExaminationPaper examinationPaper)
    {
        List<ExaminationPaper> list = examinationPaperService.selectExaminationPaperList(examinationPaper);
        ExcelUtil<ExaminationPaper> util = new ExcelUtil<ExaminationPaper>(ExaminationPaper.class);
        util.exportExcel(response, list, "考核数据");
    }

    /**
     * 获取考核详细信息
     */
    @PreAuthorize("@ss.hasPermi('examination:paper:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(examinationPaperService.selectExaminationPaperById(id));
    }

    /**
     * 新增考核
     */
    @PreAuthorize("@ss.hasPermi('examination:paper:add')")
    @Log(title = "考核", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExaminationPaper examinationPaper)
    {
        return toAjax(examinationPaperService.insertExaminationPaper(examinationPaper));
    }

    /**
     * 修改考核
     */
    @PreAuthorize("@ss.hasPermi('examination:paper:edit')")
    @Log(title = "考核", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExaminationPaper examinationPaper)
    {
        return toAjax(examinationPaperService.updateExaminationPaper(examinationPaper));
    }

    /**
     * 删除考核
     */
    @PreAuthorize("@ss.hasPermi('examination:paper:remove')")
    @Log(title = "考核", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(examinationPaperService.deleteExaminationPaperByIds(ids));
    }
}

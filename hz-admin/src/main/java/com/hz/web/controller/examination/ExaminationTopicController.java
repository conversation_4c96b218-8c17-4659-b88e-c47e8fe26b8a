package com.hz.web.controller.examination;

import java.util.List;

import com.hz.examination.domain.ExaminationTopic;
import com.hz.examination.service.IExaminationTopicService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hz.common.annotation.Log;
import com.hz.common.core.controller.BaseController;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.enums.BusinessType;
import com.hz.common.utils.poi.ExcelUtil;
import com.hz.common.core.page.TableDataInfo;

/**
 * 考核题目Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/examination/topic")
public class ExaminationTopicController extends BaseController
{
    @Autowired
    private IExaminationTopicService examinationTopicService;

    /**
     * 查询考核题目列表
     */
    @PreAuthorize("@ss.hasPermi('examination:topic:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExaminationTopic examinationTopic)
    {
        startPage();
        List<ExaminationTopic> list = examinationTopicService.selectExaminationTopicList(examinationTopic);
        return getDataTable(list);
    }

    /**
     * 导出考核题目列表
     */
    @PreAuthorize("@ss.hasPermi('examination:topic:export')")
    @Log(title = "考核题目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExaminationTopic examinationTopic)
    {
        List<ExaminationTopic> list = examinationTopicService.selectExaminationTopicList(examinationTopic);
        ExcelUtil<ExaminationTopic> util = new ExcelUtil<ExaminationTopic>(ExaminationTopic.class);
        util.exportExcel(response, list, "考核题目数据");
    }

    /**
     * 获取考核题目详细信息
     */
    @PreAuthorize("@ss.hasPermi('examination:topic:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(examinationTopicService.selectExaminationTopicById(id));
    }

    /**
     * 新增考核题目
     */
    @PreAuthorize("@ss.hasPermi('examination:topic:add')")
    @Log(title = "考核题目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExaminationTopic examinationTopic)
    {
        return toAjax(examinationTopicService.insertExaminationTopic(examinationTopic));
    }

    /**
     * 批量新增考核题目
     */
    @PostMapping(value = "/paperBatch/{paperId}/{merchantId}")
    public AjaxResult batchAdd(@PathVariable("paperId") Long paperId, @PathVariable("merchantId") Long merchantId,
                               @RequestBody List<ExaminationTopic> examinationTopic)
    {
        return success(examinationTopicService.batchInsertExaminationTopicByWeb(examinationTopic,paperId,merchantId,getUserId(),getUsername()));
    }

    @PreAuthorize("@ss.hasPermi('examination:topic:list')")
    @GetMapping("/ReplyByPaperId")
    public AjaxResult listReply(Long paperId)
    {
        return success(examinationTopicService.selectExaminationTopicByPaperId(paperId));
    }

    @PreAuthorize("@ss.hasPermi('examination:topic:list')")
    @GetMapping("/ReplyMerchantByPaperId")
    public AjaxResult listReplyMerchant(Long paperId, Long merchantId)
    {
        return success(examinationTopicService.selectWxExaminationTopicByPaperId(paperId,merchantId));
    }

    /**
     * 批量新增考核题目
     */
    @PreAuthorize("@ss.hasPermi('examination:topic:add')")
    @Log(title = "考核题目", businessType = BusinessType.INSERT)
    @PostMapping(value = "/batch/{paperId}")
    public AjaxResult batchAdd(@PathVariable("paperId") Long paperId, @RequestBody List<ExaminationTopic> examinationTopic)
    {
        return toAjax(examinationTopicService.batchInsertExaminationTopic(examinationTopic,paperId,getUserId(),getUsername()));
    }

    /**
     * 修改考核题目
     */
    @PreAuthorize("@ss.hasPermi('examination:topic:edit')")
    @Log(title = "考核题目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExaminationTopic examinationTopic)
    {
        return toAjax(examinationTopicService.updateExaminationTopic(examinationTopic));
    }

    /**
     * 删除考核题目
     */
    @PreAuthorize("@ss.hasPermi('examination:topic:remove')")
    @Log(title = "考核题目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(examinationTopicService.deleteExaminationTopicByIds(ids));
    }
}

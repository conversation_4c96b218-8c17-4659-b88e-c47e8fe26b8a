package com.hz.web.controller.examination;

import java.util.List;

import com.hz.examination.domain.ExaminationReplyMerchant;
import com.hz.examination.service.IExaminationReplyMerchantService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hz.common.annotation.Log;
import com.hz.common.core.controller.BaseController;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.enums.BusinessType;
import com.hz.common.utils.poi.ExcelUtil;
import com.hz.common.core.page.TableDataInfo;

/**
 * 考核题目商户回答Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/examination/merchant")
public class ExaminationReplyMerchantController extends BaseController
{
    @Autowired
    private IExaminationReplyMerchantService examinationReplyMerchantService;

    /**
     * 查询考核题目商户回答列表
     */
    @PreAuthorize("@ss.hasPermi('examination:merchant:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExaminationReplyMerchant examinationReplyMerchant)
    {
        startPage();
        List<ExaminationReplyMerchant> list = examinationReplyMerchantService.selectExaminationReplyMerchantList(examinationReplyMerchant);
        return getDataTable(list);
    }

    /**
     * 导出考核题目商户回答列表
     */
    @PreAuthorize("@ss.hasPermi('examination:merchant:export')")
    @Log(title = "考核题目商户回答", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExaminationReplyMerchant examinationReplyMerchant)
    {
        List<ExaminationReplyMerchant> list = examinationReplyMerchantService.selectExaminationReplyMerchantList(examinationReplyMerchant);
        ExcelUtil<ExaminationReplyMerchant> util = new ExcelUtil<ExaminationReplyMerchant>(ExaminationReplyMerchant.class);
        util.exportExcel(response, list, "考核题目商户回答数据");
    }

    /**
     * 获取考核题目商户回答详细信息
     */
    @PreAuthorize("@ss.hasPermi('examination:merchant:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(examinationReplyMerchantService.selectExaminationReplyMerchantById(id));
    }

    /**
     * 新增考核题目商户回答
     */
    @PreAuthorize("@ss.hasPermi('examination:merchant:add')")
    @Log(title = "考核题目商户回答", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExaminationReplyMerchant examinationReplyMerchant)
    {
        return toAjax(examinationReplyMerchantService.insertExaminationReplyMerchant(examinationReplyMerchant));
    }

    /**
     * 修改考核题目商户回答
     */
    @PreAuthorize("@ss.hasPermi('examination:merchant:edit')")
    @Log(title = "考核题目商户回答", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExaminationReplyMerchant examinationReplyMerchant)
    {
        return toAjax(examinationReplyMerchantService.updateExaminationReplyMerchant(examinationReplyMerchant));
    }

    /**
     * 删除考核题目商户回答
     */
    @PreAuthorize("@ss.hasPermi('examination:merchant:remove')")
    @Log(title = "考核题目商户回答", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(examinationReplyMerchantService.deleteExaminationReplyMerchantByIds(ids));
    }
}

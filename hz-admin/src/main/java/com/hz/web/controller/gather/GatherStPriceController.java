package com.hz.web.controller.gather;

import com.hz.common.annotation.Log;
import com.hz.common.core.controller.BaseController;
import com.hz.common.core.domain.AjaxResult;
import com.hz.common.core.page.TableDataInfo;
import com.hz.common.enums.BusinessType;
import com.hz.common.utils.poi.ExcelUtil;
import com.hz.gather.domain.GatherStPrice;
import com.hz.gather.service.IGatherStPriceService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 调研价格Controller
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@RestController
@RequestMapping("/gather/price")
public class GatherStPriceController extends BaseController{

    @Resource
    private IGatherStPriceService gatherStPriceService;

    /**
     * 查询调研价格列表
     */
    @PreAuthorize("@ss.hasPermi('gather:price:query')")
    @GetMapping("/list")
    public TableDataInfo list(GatherStPrice gatherStPrice){
        startPage();
        List<GatherStPrice> list = gatherStPriceService.selectList(gatherStPrice);
        return getDataTable(list);
    }

    /**
     * 导出调研价格列表
     */
    @PreAuthorize("@ss.hasPermi('gather:price:export')")
    @Log(title = "调研价格", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GatherStPrice gatherStPrice){
        List<GatherStPrice> list = gatherStPriceService.selectList(gatherStPrice);
        ExcelUtil<GatherStPrice> util = new ExcelUtil<>(GatherStPrice.class);
        util.exportExcel(response, list, "调研价格数据");
    }

    /**
     * 获取调研价格详细信息
     */
    @PreAuthorize("@ss.hasPermi('gather:price:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id){
        return success(gatherStPriceService.selectById(id));
    }

    /**
     * 新增调研价格
     */
    @PreAuthorize("@ss.hasPermi('gather:price:add')")
    @Log(title = "调研价格", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GatherStPrice gatherStPrice){
        return toAjax(gatherStPriceService.insert(gatherStPrice));
    }

    /**
     * 修改调研价格
     */
    @PreAuthorize("@ss.hasPermi('gather:price:edit')")
    @Log(title = "调研价格", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GatherStPrice gatherStPrice){
        return toAjax(gatherStPriceService.update(gatherStPrice));
    }

    /**
     * 删除调研价格
     */
    @PreAuthorize("@ss.hasPermi('gather:price:remove')")
    @Log(title = "调研价格", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids){
        return toAjax(gatherStPriceService.deleteByIds(ids));
    }
}

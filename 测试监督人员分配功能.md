# 监督人员分配功能测试指南

## 测试前准备

### 1. 用户角色设置
确保测试用户具有监督人员角色：
- 角色Key: `supervise`
- 角色ID: `103`
- 用户必须被分配到该角色

### 2. 测试数据准备
- 月度考核ID: 需要一个有效的月度考核记录
- 商户ID列表: 需要几个有效的商户ID
- 专卖人员: 具有roleId=104的用户
- 营销人员: 具有roleId=105的用户

## 测试用例

### 测试用例1: 正常分配流程
**测试目标:** 验证监督人员能够成功分配商户和人员

**前置条件:**
- 当前用户具有监督人员角色
- 提供有效的月度考核ID和商户ID列表

**测试步骤:**
1. 使用监督人员账号登录
2. 调用 `/wx/assignMerchantsAndStaff` 接口
3. 传入测试数据：
```json
{
  "monthlyAssessmentId": 123,
  "merchantIds": [1001, 1002],
  "monopolyStaffs": [
    {
      "userId": 789,
      "userName": "张三",
      "nickName": "专卖张三"
    }
  ],
  "marketingStaffs": [
    {
      "userId": 790,
      "userName": "李四",
      "nickName": "营销李四"
    }
  ]
}
```

**预期结果:**
- 返回成功响应 (code: 200)
- 商户的assessor_id字段更新为当前监督人员ID
- 专卖和营销人员记录正确插入

### 测试用例2: 权限验证
**测试目标:** 验证非监督人员无法调用接口

**前置条件:**
- 当前用户不具有监督人员角色

**测试步骤:**
1. 使用非监督人员账号登录
2. 调用 `/wx/assignMerchantsAndStaff` 接口

**预期结果:**
- 返回权限错误 (code: 500)
- 错误信息: "当前用户不具有监督人员角色，无权限执行此操作"

### 测试用例3: 参数验证
**测试目标:** 验证参数校验逻辑

**测试数据:**
```json
{
  "monthlyAssessmentId": null,
  "merchantIds": []
}
```

**预期结果:**
- 返回参数错误
- 相应的错误提示信息

### 测试用例4: 只分配商户，不分配人员
**测试目标:** 验证可以只分配商户而不分配专卖/营销人员

**测试数据:**
```json
{
  "monthlyAssessmentId": 123,
  "merchantIds": [1001, 1002]
}
```

**预期结果:**
- 商户分配成功
- 专卖和营销人员记录不变

## 数据库验证

### 验证商户分配
```sql
SELECT merchant_id, assessor_id, update_time, update_by 
FROM monthly_assessment_merchants 
WHERE monthly_assessment_id = 123 
AND merchant_id IN (1001, 1002);
```

### 验证人员分配
```sql
-- 查看专卖人员
SELECT * FROM monthly_assessment_staff 
WHERE monthly_assessment_id = 123 AND role_id = 104;

-- 查看营销人员  
SELECT * FROM monthly_assessment_staff 
WHERE monthly_assessment_id = 123 AND role_id = 105;
```

## 前端集成测试

### Vue组件调用示例
```javascript
// 在Vue组件中调用
import { assignMerchantsAndStaff } from '@/api/quarterlyassessments/monthlyStaff'

export default {
  methods: {
    async handleAssign() {
      try {
        const data = {
          monthlyAssessmentId: this.currentAssessmentId,
          merchantIds: this.selectedMerchantIds,
          monopolyStaffs: this.selectedMonopolyStaffs,
          marketingStaffs: this.selectedMarketingStaffs
        }
        
        const response = await assignMerchantsAndStaff(data)
        if (response.code === 200) {
          this.$message.success(response.msg)
          this.refreshData()
        } else {
          this.$message.error(response.msg)
        }
      } catch (error) {
        this.$message.error('分配失败：' + error.message)
      }
    }
  }
}
```

## 常见问题排查

### 1. 权限错误
- 检查用户是否具有 `supervise` 角色
- 确认角色状态为正常（status = '0'）

### 2. 参数错误
- 确认月度考核ID存在且有效
- 确认商户ID列表不为空且商户存在

### 3. 数据库更新失败
- 检查数据库连接
- 确认相关表结构正确
- 查看日志中的具体错误信息

### 4. 事务回滚
- 检查是否有数据约束冲突
- 确认所有相关表的外键关系正确

## 性能测试

### 批量分配测试
测试大量商户和人员的分配性能：
- 100个商户 + 10个专卖人员 + 10个营销人员
- 监控响应时间和数据库性能

### 并发测试
测试多个监督人员同时分配的情况：
- 模拟5个监督人员同时进行分配操作
- 验证数据一致性和系统稳定性

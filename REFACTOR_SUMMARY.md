# 专卖人员和营销人员更新接口重构总结

## 重构目标
将专卖人员和营销人员的更新逻辑从 `MonthlyAssessmentStaffServiceImpl.saveSelectedStaff` 方法中移动到 `WxController` 中作为独立的接口方法。

## 主要更改

### 1. 新增 DTO 类
**文件**: `hz-system/src/main/java/com/hz/quarterlyassessments/domain/dto/UpdateStaffByRoleRequest.java`
- 创建了新的请求 DTO 类，用于按角色更新人员
- 包含字段：`monthlyAssessmentId`、`roleId`、`staffList`

### 2. WxController 新增接口方法
**文件**: `hz-admin/src/main/java/com/hz/web/controller/merchant/WxController.java`

#### 新增方法：
1. **`updateMonopolyStaff`** - 更新专卖人员
   - 路径：`POST /wx/updateMonopolyStaff`
   - 角色ID：104L（专卖人员）
   
2. **`updateMarketingStaff`** - 更新营销人员
   - 路径：`POST /wx/updateMarketingStaff`
   - 角色ID：105L（营销人员）

#### 功能特点：
- 先删除现有记录，再插入新记录
- 支持空列表（仅删除，不插入）
- 包含完整的错误处理和日志记录
- 返回操作结果和更新人员数量

### 3. MonthlyAssessmentStaffServiceImpl 修改
**文件**: `hz-system/src/main/java/com/hz/quarterlyassessments/service/impl/MonthlyAssessmentStaffServiceImpl.java`

#### 主要更改：
- 移除了专卖人员和营销人员的处理逻辑（原第359-395行）
- 更新了方法注释，明确说明现在只处理组长和监督人员
- 移除了未使用的 `hasUpdates` 变量

### 4. 接口定义更新
**文件**: `hz-system/src/main/java/com/hz/quarterlyassessments/service/IMonthlyAssessmentStaffService.java`
- 更新了 `saveSelectedStaff` 方法的注释
- 标明专卖人员和营销人员参数已废弃

### 5. 前端 API 方法
**文件**: `hz-ui/src/api/quarterlyassessments/monthlyStaff.js`

#### 新增方法：
1. **`updateMonopolyStaff`** - 调用专卖人员更新接口
2. **`updateMarketingStaff`** - 调用营销人员更新接口

## 使用方式

### 后端调用示例
```javascript
// 更新专卖人员
const monopolyRequest = {
  monthlyAssessmentId: 123,
  staffList: [
    { userId: 1, userName: "张三" },
    { userId: 2, userName: "李四" }
  ]
};

// 更新营销人员
const marketingRequest = {
  monthlyAssessmentId: 123,
  staffList: [
    { userId: 3, userName: "王五" },
    { userId: 4, userName: "赵六" }
  ]
};
```

### 前端调用示例
```javascript
import { updateMonopolyStaff, updateMarketingStaff } from '@/api/quarterlyassessments/monthlyStaff'

// 更新专卖人员
updateMonopolyStaff(monopolyRequest).then(response => {
  console.log('专卖人员更新成功:', response.data);
});

// 更新营销人员
updateMarketingStaff(marketingRequest).then(response => {
  console.log('营销人员更新成功:', response.data);
});
```

## 兼容性说明
- `saveSelectedStaff` 方法仍然保留原有的参数签名，但专卖人员和营销人员参数将被忽略
- 现有调用 `saveSelectedStaff` 的代码不会受到影响，只是专卖人员和营销人员不会被处理
- 需要更新专卖人员和营销人员时，应使用新的独立接口

## 优势
1. **职责分离**：专卖人员和营销人员的更新逻辑独立，便于维护
2. **灵活性**：可以单独更新某一类人员，不需要同时处理所有角色
3. **微信端友好**：新接口在 WxController 中，更适合微信小程序调用
4. **错误隔离**：某一类人员更新失败不会影响其他角色的更新

## 测试建议
1. 测试新接口的基本功能（增删改）
2. 测试空列表的处理
3. 测试错误情况的处理
4. 验证原有 `saveSelectedStaff` 方法仍能正常处理组长和监督人员

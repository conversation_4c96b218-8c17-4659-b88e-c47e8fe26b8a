# 监督人员分配商户和人员功能使用说明

## 功能概述

这个功能允许监督人员在wx前端选择商户，然后分配专卖和营销人员，并建立相应的关联关系。

## 数据表关系

1. **monthly_assessment_merchants** - 存储商户与月度考核的关联关系
   - `assessor_id` 字段存储考核人员ID（监督人员ID）
   
2. **monthly_assessment_staff** - 存储人员与月度考核的关联关系
   - `role_id = 103` 表示监督人员
   - `role_id = 104` 表示专卖人员  
   - `role_id = 105` 表示营销人员

## API接口

### 监督人员分配商户和人员

**接口地址：** `POST /wx/assignMerchantsAndStaff`

**请求参数：**
```json
{
  "monthlyAssessmentId": 123,           // 月度考核ID
  "merchantIds": [1001, 1002, 1003],   // 商户ID列表
  "monopolyStaffs": [                   // 专卖人员列表（可选）
    {
      "userId": 789,
      "userName": "张三",
      "nickName": "专卖张三"
    }
  ],
  "marketingStaffs": [                  // 营销人员列表（可选）
    {
      "userId": 790,
      "userName": "李四",
      "nickName": "营销李四"
    }
  ]
}
```

**注意：** 监督人员ID自动从当前登录用户获取，无需在请求中传递。
```

**响应结果：**
```json
{
  "code": 200,
  "msg": "分配成功：更新了3个商户的考核人员，专卖人员1名，营销人员1名",
  "data": null
}
```

**权限要求：**
- 当前登录用户必须具有监督人员角色（roleKey = "supervise"）
- 系统会自动验证用户权限，非监督人员无法调用此接口

## 前端调用示例

```javascript
import { assignMerchantsAndStaff } from '@/api/quarterlyassessments/monthlyStaff'

// 调用分配接口
const assignData = {
  monthlyAssessmentId: 123,
  merchantIds: [1001, 1002, 1003],
  monopolyStaffs: [
    {
      userId: 789,
      userName: "张三",
      nickName: "专卖张三"
    }
  ],
  marketingStaffs: [
    {
      userId: 790,
      userName: "李四",
      nickName: "营销李四"
    }
  ]
}

assignMerchantsAndStaff(assignData).then(response => {
  if (response.code === 200) {
    this.$message.success(response.msg)
  } else {
    this.$message.error(response.msg)
  }
}).catch(error => {
  this.$message.error('分配失败：' + error.message)
})
```

## 业务逻辑说明

1. **权限验证：** 验证当前登录用户是否具有监督人员角色（roleKey = "supervise"）
2. **监督人员获取：** 自动从当前登录用户获取监督人员ID
3. **商户分配：** 将选中的商户的`assessor_id`字段更新为当前监督人员ID
4. **专卖人员管理：** 先删除现有专卖人员记录，再插入新的专卖人员记录
5. **营销人员管理：** 先删除现有营销人员记录，再插入新的营销人员记录
6. **关联关系：** 通过`monthly_assessment_id`字段建立监督人员、专卖人员、营销人员与商户的关联

## 数据库变更

### 新增方法

1. **MonthlyAssessmentMerchantMapper.updateAssessorIdByMerchantIds** - 批量更新商户考核人员ID
2. **IMonthlyAssessmentMerchantService.updateAssessorIdByMerchantIds** - 服务层方法
3. **MonthlyAssessmentMerchantServiceImpl.updateAssessorIdByMerchantIds** - 实现类方法

### 新增文件

1. **SupervisorAssignmentRequest.java** - 请求参数类
2. **WxController.assignMerchantsAndStaff** - 控制器方法

## 注意事项

1. **权限限制：** 只有具有监督人员角色（roleKey = "supervise"）的用户才能调用此接口
2. **自动获取监督人员：** 监督人员ID自动从当前登录用户获取，无需前端传递
3. **商户ID验证：** 商户ID列表不能为空
4. **人员列表可选：** 专卖和营销人员列表可以为空，表示不更新对应人员
5. **数据一致性：** 更新操作会先删除现有记录再插入新记录，确保数据一致性
6. **事务保证：** 所有操作在同一个事务中执行，保证数据完整性
